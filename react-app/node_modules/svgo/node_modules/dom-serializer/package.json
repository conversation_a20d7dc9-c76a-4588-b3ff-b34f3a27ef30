{"name": "dom-serializer", "version": "0.2.2", "description": "render dom nodes to string", "author": "<PERSON> <<EMAIL>>", "keywords": ["html", "xml", "render"], "repository": {"type": "git", "url": "git://github.com/cheeriojs/dom-renderer.git"}, "main": "./index.js", "files": ["index.js", "index.d.ts", "foreignNames.json"], "dependencies": {"domelementtype": "^2.0.1", "entities": "^2.0.0"}, "devDependencies": {"cheerio": "^1.0.0-rc.2", "expect.js": "~0.3.1", "htmlparser2": "^3.10.0", "lodash": "^4.17.11", "mocha": "^6.2.0", "xyz": "^3.0.0"}, "scripts": {"test": "mocha test.js"}, "prettier": {"singleQuote": true}, "license": "MIT"}