"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const range_1 = __importDefault(require("../definitions/range"));
const range = (ajv) => ajv.addKeyword((0, range_1.default)());
exports.default = range;
module.exports = range;
//# sourceMappingURL=range.js.map