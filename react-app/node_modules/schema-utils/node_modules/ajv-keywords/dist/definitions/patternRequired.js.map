{"version": 3, "file": "patternRequired.js", "sourceRoot": "", "sources": ["../../src/definitions/patternRequired.ts"], "names": [], "mappings": ";;AACA,sDAAoD;AACpD,mCAAkC;AAIlC,MAAM,KAAK,GAA2B;IACpC,OAAO,EAAE,CAAC,EAAC,MAAM,EAAE,EAAC,cAAc,EAAC,EAAC,EAAE,EAAE,CACtC,IAAA,aAAG,EAAA,0CAA0C,cAAc,GAAG;IAChE,MAAM,EAAE,CAAC,EAAC,MAAM,EAAE,EAAC,cAAc,EAAC,EAAC,EAAE,EAAE,CAAC,IAAA,WAAC,EAAA,oBAAoB,cAAc,GAAG;CAC/E,CAAA;AAED,SAAwB,MAAM;IAC5B,OAAO;QACL,OAAO,EAAE,iBAAiB;QAC1B,IAAI,EAAE,QAAQ;QACd,UAAU,EAAE,OAAO;QACnB,KAAK;QACL,IAAI,CAAC,GAAe;YAClB,MAAM,EAAC,GAAG,EAAE,MAAM,EAAE,IAAI,EAAC,GAAG,GAAG,CAAA;YAC/B,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC;gBAAE,OAAM;YAC/B,MAAM,KAAK,GAAG,GAAG,CAAC,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,CAAA;YACpC,KAAK,MAAM,GAAG,IAAI,MAAM;gBAAE,kBAAkB,CAAC,GAAG,CAAC,CAAA;YAEjD,SAAS,kBAAkB,CAAC,OAAe;gBACzC,MAAM,OAAO,GAAG,GAAG,CAAC,GAAG,CAAC,SAAS,EAAE,KAAK,CAAC,CAAA;gBAEzC,GAAG,CAAC,KAAK,CAAC,KAAK,EAAE,IAAI,EAAE,CAAC,GAAG,EAAE,EAAE;oBAC7B,GAAG,CAAC,MAAM,CAAC,OAAO,EAAE,IAAA,WAAC,EAAA,GAAG,IAAA,kBAAU,EAAC,GAAG,EAAE,OAAO,CAAC,SAAS,GAAG,GAAG,CAAC,CAAA;oBAChE,GAAG,CAAC,EAAE,CAAC,OAAO,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,KAAK,EAAE,CAAC,CAAA;gBACpC,CAAC,CAAC,CAAA;gBAEF,GAAG,CAAC,SAAS,CAAC,EAAC,cAAc,EAAE,OAAO,EAAC,CAAC,CAAA;gBACxC,GAAG,CAAC,MAAM,CAAC,KAAK,EAAE,IAAA,aAAG,EAAC,KAAK,EAAE,OAAO,CAAC,CAAC,CAAA;gBACtC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;YACjB,CAAC;QACH,CAAC;QACD,UAAU,EAAE;YACV,IAAI,EAAE,OAAO;YACb,KAAK,EAAE,EAAC,IAAI,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAC;YACxC,WAAW,EAAE,IAAI;SAClB;KACF,CAAA;AACH,CAAC;AA/BD,yBA+BC;AAED,MAAM,CAAC,OAAO,GAAG,MAAM,CAAA"}