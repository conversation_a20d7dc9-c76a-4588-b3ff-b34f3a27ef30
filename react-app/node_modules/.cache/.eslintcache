[{"/Users/<USER>/Documents/FatbeamU/react-app/src/index.js": "1", "/Users/<USER>/Documents/FatbeamU/react-app/src/App.js": "2", "/Users/<USER>/Documents/FatbeamU/react-app/src/pages/DepartmentPages.js": "3", "/Users/<USER>/Documents/FatbeamU/react-app/src/pages/LibraryPages.js": "4", "/Users/<USER>/Documents/FatbeamU/react-app/src/pages/MainPage.js": "5", "/Users/<USER>/Documents/FatbeamU/react-app/src/components/Navigation.js": "6", "/Users/<USER>/Documents/FatbeamU/react-app/src/components/UnifiedDashboard.js": "7", "/Users/<USER>/Documents/FatbeamU/react-app/src/pages/ClassroomPage.js": "8", "/Users/<USER>/Documents/FatbeamU/react-app/src/components/MoodleIntegration.js": "9", "/Users/<USER>/Documents/FatbeamU/react-app/src/pages/departments/ConstructionDeptPage.js": "10", "/Users/<USER>/Documents/FatbeamU/react-app/src/pages/departments/FinancialDeptPage.js": "11", "/Users/<USER>/Documents/FatbeamU/react-app/src/pages/departments/NetworkOperationsDeptPage.js": "12", "/Users/<USER>/Documents/FatbeamU/react-app/src/pages/departments/TowerTechniciansDeptPage.js": "13", "/Users/<USER>/Documents/FatbeamU/react-app/src/pages/departments/HumanRelationsDeptPage.js": "14", "/Users/<USER>/Documents/FatbeamU/react-app/src/pages/departments/SalesDeptPage.js": "15", "/Users/<USER>/Documents/FatbeamU/react-app/src/pages/departments/LeadershipTeamPage.js": "16", "/Users/<USER>/Documents/FatbeamU/react-app/src/services/moodleAPI.js": "17"}, {"size": 254, "mtime": 1748427830711, "results": "18", "hashOfConfig": "19"}, {"size": 1706, "mtime": 1748427487029, "results": "20", "hashOfConfig": "19"}, {"size": 9557, "mtime": 1748427685248, "results": "21", "hashOfConfig": "19"}, {"size": 17612, "mtime": 1748429246824, "results": "22", "hashOfConfig": "19"}, {"size": 17320, "mtime": 1748427585831, "results": "23", "hashOfConfig": "19"}, {"size": 7127, "mtime": 1748427522212, "results": "24", "hashOfConfig": "19"}, {"size": 11822, "mtime": 1748427764639, "results": "25", "hashOfConfig": "19"}, {"size": 21280, "mtime": 1748430344980, "results": "26", "hashOfConfig": "19"}, {"size": 10130, "mtime": 1748427796721, "results": "27", "hashOfConfig": "19"}, {"size": 1464, "mtime": 1748428011625, "results": "28", "hashOfConfig": "19"}, {"size": 18115, "mtime": 1748428000839, "results": "29", "hashOfConfig": "19"}, {"size": 1465, "mtime": 1748428046458, "results": "30", "hashOfConfig": "19"}, {"size": 1457, "mtime": 1748428021464, "results": "31", "hashOfConfig": "19"}, {"size": 1458, "mtime": 1748428029374, "results": "32", "hashOfConfig": "19"}, {"size": 1439, "mtime": 1748428037585, "results": "33", "hashOfConfig": "19"}, {"size": 1436, "mtime": 1748428055107, "results": "34", "hashOfConfig": "19"}, {"size": 7435, "mtime": 1748427824295, "results": "35", "hashOfConfig": "19"}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1do7zn0", {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "45", "messages": "46", "suppressedMessages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "57", "messages": "58", "suppressedMessages": "59", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "72", "messages": "73", "suppressedMessages": "74", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "75", "messages": "76", "suppressedMessages": "77", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "/Users/<USER>/Documents/FatbeamU/react-app/src/index.js", [], [], "/Users/<USER>/Documents/FatbeamU/react-app/src/App.js", [], [], "/Users/<USER>/Documents/FatbeamU/react-app/src/pages/DepartmentPages.js", ["87", "88", "89", "90", "91"], [], "/Users/<USER>/Documents/FatbeamU/react-app/src/pages/LibraryPages.js", ["92", "93", "94", "95", "96", "97", "98"], [], "/Users/<USER>/Documents/FatbeamU/react-app/src/pages/MainPage.js", ["99"], [], "/Users/<USER>/Documents/FatbeamU/react-app/src/components/Navigation.js", [], [], "/Users/<USER>/Documents/FatbeamU/react-app/src/components/UnifiedDashboard.js", [], [], "/Users/<USER>/Documents/FatbeamU/react-app/src/pages/ClassroomPage.js", [], [], "/Users/<USER>/Documents/FatbeamU/react-app/src/components/MoodleIntegration.js", [], [], "/Users/<USER>/Documents/FatbeamU/react-app/src/pages/departments/ConstructionDeptPage.js", [], [], "/Users/<USER>/Documents/FatbeamU/react-app/src/pages/departments/FinancialDeptPage.js", ["100", "101", "102"], [], "/Users/<USER>/Documents/FatbeamU/react-app/src/pages/departments/NetworkOperationsDeptPage.js", [], [], "/Users/<USER>/Documents/FatbeamU/react-app/src/pages/departments/TowerTechniciansDeptPage.js", [], [], "/Users/<USER>/Documents/FatbeamU/react-app/src/pages/departments/HumanRelationsDeptPage.js", [], [], "/Users/<USER>/Documents/FatbeamU/react-app/src/pages/departments/SalesDeptPage.js", [], [], "/Users/<USER>/Documents/FatbeamU/react-app/src/pages/departments/LeadershipTeamPage.js", [], [], "/Users/<USER>/Documents/FatbeamU/react-app/src/services/moodleAPI.js", ["103"], [], {"ruleId": "104", "severity": 1, "message": "105", "line": 15, "column": 10, "nodeType": "106", "messageId": "107", "endLine": 15, "endColumn": 19}, {"ruleId": "104", "severity": 1, "message": "108", "line": 15, "column": 21, "nodeType": "106", "messageId": "107", "endLine": 15, "endColumn": 33}, {"ruleId": "104", "severity": 1, "message": "109", "line": 16, "column": 10, "nodeType": "106", "messageId": "107", "endLine": 16, "endColumn": 20}, {"ruleId": "104", "severity": 1, "message": "110", "line": 17, "column": 10, "nodeType": "106", "messageId": "107", "endLine": 17, "endColumn": 20}, {"ruleId": "104", "severity": 1, "message": "111", "line": 17, "column": 22, "nodeType": "106", "messageId": "107", "endLine": 17, "endColumn": 35}, {"ruleId": "104", "severity": 1, "message": "112", "line": 4, "column": 27, "nodeType": "106", "messageId": "107", "endLine": 4, "endColumn": 45}, {"ruleId": "104", "severity": 1, "message": "113", "line": 5, "column": 10, "nodeType": "106", "messageId": "107", "endLine": 5, "endColumn": 25}, {"ruleId": "104", "severity": 1, "message": "114", "line": 5, "column": 27, "nodeType": "106", "messageId": "107", "endLine": 5, "endColumn": 45}, {"ruleId": "104", "severity": 1, "message": "115", "line": 9, "column": 10, "nodeType": "106", "messageId": "107", "endLine": 9, "endColumn": 25}, {"ruleId": "104", "severity": 1, "message": "116", "line": 10, "column": 10, "nodeType": "106", "messageId": "107", "endLine": 10, "endColumn": 22}, {"ruleId": "104", "severity": 1, "message": "117", "line": 11, "column": 10, "nodeType": "106", "messageId": "107", "endLine": 11, "endColumn": 23}, {"ruleId": "118", "severity": 1, "message": "119", "line": 152, "column": 6, "nodeType": "120", "endLine": 152, "endColumn": 57, "suggestions": "121"}, {"ruleId": "104", "severity": 1, "message": "105", "line": 5, "column": 10, "nodeType": "106", "messageId": "107", "endLine": 5, "endColumn": 19}, {"ruleId": "104", "severity": 1, "message": "109", "line": 6, "column": 10, "nodeType": "106", "messageId": "107", "endLine": 6, "endColumn": 20}, {"ruleId": "104", "severity": 1, "message": "110", "line": 7, "column": 10, "nodeType": "106", "messageId": "107", "endLine": 7, "endColumn": 20}, {"ruleId": "104", "severity": 1, "message": "111", "line": 7, "column": 22, "nodeType": "106", "messageId": "107", "endLine": 7, "endColumn": 35}, {"ruleId": "104", "severity": 1, "message": "122", "line": 200, "column": 23, "nodeType": "106", "messageId": "107", "endLine": 200, "endColumn": 31}, "no-unused-vars", "'activeTab' is assigned a value but never used.", "Identifier", "unusedVar", "'setActiveTab' is assigned a value but never used.", "'isScrolled' is assigned a value but never used.", "'isMenuOpen' is assigned a value but never used.", "'setIsMenuOpen' is assigned a value but never used.", "'setBookmarkedItems' is assigned a value but never used.", "'checkedOutItems' is assigned a value but never used.", "'setCheckedOutItems' is assigned a value but never used.", "'showBookDetails' is assigned a value but never used.", "'selectedBook' is assigned a value but never used.", "'filteredBooks' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'allBooks'. Either include it or remove the dependency array.", "ArrayExpression", ["123"], "'siteInfo' is assigned a value but never used.", {"desc": "124", "fix": "125"}, "Update the dependencies array to be: [allBooks, searchQuery, selectedCategory, selectedDifficulty]", {"range": "126", "text": "127"}, [7367, 7418], "[all<PERSON>ooks, search<PERSON><PERSON>y, selected<PERSON><PERSON><PERSON><PERSON>, selected<PERSON><PERSON><PERSON><PERSON>y]"]