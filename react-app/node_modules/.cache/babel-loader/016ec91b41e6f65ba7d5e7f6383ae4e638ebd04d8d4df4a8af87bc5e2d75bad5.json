{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/FatbeamU/react-app/src/pages/departments/FinancialDeptPage.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Link } from 'react-router-dom';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst FinancialDeptPage = () => {\n  _s();\n  const [activeTab, setActiveTab] = useState('overview');\n  const [isScrolled, setIsScrolled] = useState(false);\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n  const [showFacultyDetails, setShowFacultyDetails] = useState(null);\n  useEffect(() => {\n    const handleScroll = () => {\n      if (window.scrollY > 50) {\n        setIsScrolled(true);\n      } else {\n        setIsScrolled(false);\n      }\n    };\n    window.addEventListener('scroll', handleScroll);\n\n    // Particle animation\n    const createParticles = () => {\n      const particleContainer = document.getElementById('particle-container');\n      if (!particleContainer) return;\n      for (let i = 0; i < 30; i++) {\n        const particle = document.createElement('div');\n        particle.className = 'absolute w-1 h-1 rounded-full bg-yellow-400 opacity-0';\n        // Random position\n        particle.style.left = `${Math.random() * 100}%`;\n        particle.style.top = `${Math.random() * 100}%`;\n        // Random animation duration\n        const duration = 3 + Math.random() * 5;\n        particle.style.animation = `float ${duration}s ease-in-out infinite`;\n        particle.style.animationDelay = `${Math.random() * 5}s`;\n        particleContainer.appendChild(particle);\n      }\n    };\n    createParticles();\n    return () => {\n      window.removeEventListener('scroll', handleScroll);\n    };\n  }, []);\n  const facultyMembers = [{\n    name: \"Professor Goldstein\",\n    title: \"Gold Transmutation Master\",\n    experience: \"45 years\",\n    achievements: \"Discovered the Golden Ratio Spell, Author of 'Modern Alchemical Finance'\",\n    image: \"https://readdy.ai/api/search-image?query=A%20distinguished%20elderly%20wizard%20professor%20with%20a%20neatly%20trimmed%20white%20beard%20and%20gold-rimmed%20spectacles&width=300&height=300&seq=prof1&orientation=squarish\"\n  }, {\n    name: \"Dr. Silverton\",\n    title: \"Precious Metals Expert\",\n    experience: \"38 years\",\n    achievements: \"Created the Silver Stream Investment Method, Former Royal Treasury Advisor\",\n    image: \"https://readdy.ai/api/search-image?query=A%20middle-aged%20female%20professor%20with%20silver-streaked%20hair%20in%20an%20elegant%20updo&width=300&height=300&seq=prof2&orientation=squarish\"\n  }, {\n    name: \"Master Bronzewing\",\n    title: \"Risk Assessment Specialist\",\n    experience: \"29 years\",\n    achievements: \"Developed the Bronze Shield Protection Spell, Led the Great Market Stabilization of 2018\",\n    image: \"https://readdy.ai/api/search-image?query=A%20confident%20wizard%20in%20his%20thirties%20with%20short%20bronze-colored%20hair%20and%20a%20well-groomed%20beard&width=300&height=300&seq=prof3&orientation=squarish\"\n  }, {\n    name: \"Lady Platina\",\n    title: \"Investment Strategy Archmage\",\n    experience: \"42 years\",\n    achievements: \"Inventor of the Platinum Growth Portfolio Spell, Five-time winner of the Golden Cauldron Award\",\n    image: \"https://readdy.ai/api/search-image?query=An%20elegant%20older%20woman%20with%20platinum%20blonde%20hair%20in%20a%20sophisticated%20style&width=300&height=300&seq=prof4&orientation=squarish\"\n  }];\n  const programs = [{\n    title: \"Magical Investment Management\",\n    description: \"Learn to harness magical energies to identify investment opportunities and maximize returns through alchemical transformations.\",\n    icon: \"fa-chart-line\"\n  }, {\n    title: \"Alchemical Trading\",\n    description: \"Master the ancient art of transforming base metals into gold while applying modern trading strategies to magical markets.\",\n    icon: \"fa-exchange-alt\"\n  }, {\n    title: \"Mystical Risk Assessment\",\n    description: \"Develop your third eye to foresee market fluctuations and create protective wards against financial losses.\",\n    icon: \"fa-shield-alt\"\n  }, {\n    title: \"Enchanted Portfolio Management\",\n    description: \"Craft balanced portfolios using divination techniques and magical asset allocation to achieve long-term prosperity.\",\n    icon: \"fa-wallet\"\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-white text-gray-800 font-sans pt-20\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      id: \"particle-container\",\n      className: \"fixed inset-0 pointer-events-none z-0\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 104,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-gray-100 py-4\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container mx-auto px-6\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center text-sm text-gray-600\",\n          children: [/*#__PURE__*/_jsxDEV(Link, {\n            to: \"/\",\n            className: \"hover:text-[#15a7dd] transition-colors duration-300 cursor-pointer\",\n            children: \"Home\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 110,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-chevron-right mx-2 text-xs text-gray-400\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 111,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/departments\",\n            className: \"hover:text-[#15a7dd] transition-colors duration-300 cursor-pointer\",\n            children: \"Departments\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 112,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-chevron-right mx-2 text-xs text-gray-400\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 113,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-[#15a7dd]\",\n            children: \"Financial Department (Alchemists)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 114,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 109,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 108,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 107,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"relative h-[50vh] min-h-[600px] overflow-hidden\",\n      children: [/*#__PURE__*/_jsxDEV(\"img\", {\n        src: \"https://readdy.ai/api/search-image?query=A%20magical%20financial%20department%20with%20floating%20gold%20coins%20and%20magical%20ledgers.%20Wizards%20in%20elegant%20robes%20with%20gold%20trim%20work%20at%20enchanted%20desks&width=1440&height=800&seq=herobanner&orientation=landscape\",\n        alt: \"Financial Department\",\n        className: \"w-full h-full object-cover\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 121,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute inset-0 bg-gradient-to-r from-black/70 to-transparent\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 126,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute inset-0 bg-gradient-to-b from-transparent via-transparent to-white/90\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 127,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute inset-0 flex items-center\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"container mx-auto px-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"text-5xl md:text-6xl lg:text-7xl font-serif font-bold text-white mb-6\",\n            children: [\"Financial Department \", /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"bg-clip-text text-transparent bg-gradient-to-r from-yellow-400 to-yellow-600\",\n              children: \"(Alchemists)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 131,\n              columnNumber: 36\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 130,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-xl md:text-2xl text-white max-w-3xl leading-relaxed\",\n            children: \"Where financial expertise meets magical alchemy to transform resources into prosperity through ancient arts and modern techniques.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 133,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 129,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 128,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 120,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container mx-auto px-6 py-12\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-wrap border-b border-gray-200 mb-12\",\n        children: ['overview', 'programs', 'faculty', 'careers'].map(tab => /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setActiveTab(tab),\n          className: `px-6 py-3 font-medium text-sm transition-colors duration-300 rounded-button whitespace-nowrap cursor-pointer ${activeTab === tab ? 'text-[#15a7dd] border-b-2 border-[#15a7dd]' : 'text-gray-500 hover:text-[#15a7dd]'}`,\n          children: tab.charAt(0).toUpperCase() + tab.slice(1)\n        }, tab, false, {\n          fileName: _jsxFileName,\n          lineNumber: 145,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 143,\n        columnNumber: 9\n      }, this), activeTab === 'overview' && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 lg:grid-cols-3 gap-12\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"lg:col-span-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-3xl font-serif font-bold mb-6 text-gray-800\",\n            children: \"Department Overview\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 163,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-700 mb-6 leading-relaxed\",\n            children: \"The Financial Department, known colloquially as the Alchemists, represents the perfect blend of ancient magical wisdom and cutting-edge financial expertise. Our department trains students in the delicate art of transforming raw resources into financial prosperity through a combination of traditional alchemical practices and modern financial theory.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 164,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-700 mb-6 leading-relaxed\",\n            children: \"Founded by Master Goldstein in 1823, our department has a long tradition of excellence in magical finance. We pride ourselves on maintaining the highest ethical standards while teaching students to harness the power of financial alchemy for the greater good of the magical community.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 167,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-gradient-to-br from-yellow-50 to-yellow-100 rounded-lg p-6 shadow-md\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-yellow-600 mb-2\",\n                children: /*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"fas fa-coins text-3xl\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 174,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 173,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-xl font-bold text-gray-800 mb-2\",\n                children: \"98%\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 176,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-600\",\n                children: \"Successful Financial Transformations\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 177,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 172,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-gradient-to-br from-yellow-50 to-yellow-100 rounded-lg p-6 shadow-md\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-yellow-600 mb-2\",\n                children: /*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"fas fa-exchange-alt text-3xl\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 181,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 180,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-xl font-bold text-gray-800 mb-2\",\n                children: \"1:42\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 183,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-600\",\n                children: \"Gold-to-Value Conversion Rate\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 184,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 179,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-gradient-to-br from-yellow-50 to-yellow-100 rounded-lg p-6 shadow-md\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-yellow-600 mb-2\",\n                children: /*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"fas fa-user-graduate text-3xl\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 188,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 187,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-xl font-bold text-gray-800 mb-2\",\n                children: \"96%\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 190,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-600\",\n                children: \"Graduate Placement Rate\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 191,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 186,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 171,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 162,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"lg:col-span-1\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-gradient-to-br from-yellow-50 to-yellow-100 rounded-lg p-6 shadow-md mb-8\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-xl font-serif font-bold mb-4 text-gray-800\",\n              children: \"Department Philosophy\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 198,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-6 flex justify-center\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-32 h-32 rounded-full bg-gradient-to-br from-yellow-300 to-yellow-500 flex items-center justify-center\",\n                children: /*#__PURE__*/_jsxDEV(\"img\", {\n                  src: \"https://readdy.ai/api/search-image?query=An%20alchemical%20symbol%20representing%20financial%20transformation&width=200&height=200&seq=symbol&orientation=squarish\",\n                  alt: \"Alchemical Symbol\",\n                  className: \"w-24 h-24 object-contain\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 201,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 200,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 199,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-700 mb-4\",\n              children: \"\\\"Through the perfect balance of ancient wisdom and modern innovation, we transform not just metals, but minds and markets.\\\"\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 208,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600 italic text-sm\",\n              children: \"- Founding principle established by Master Goldstein, 1823\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 211,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 197,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-lg p-6 shadow-md border border-gray-200\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-xl font-serif font-bold mb-4 text-gray-800\",\n              children: \"Quick Actions\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 217,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-3\",\n              children: [/*#__PURE__*/_jsxDEV(Link, {\n                to: \"/library\",\n                className: \"w-full py-3 bg-gradient-to-r from-yellow-400 to-yellow-600 text-white rounded-full hover:from-yellow-500 hover:to-yellow-700 transition-all duration-300 flex items-center justify-center rounded-button whitespace-nowrap cursor-pointer\",\n                children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"fas fa-book-reader mr-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 220,\n                  columnNumber: 21\n                }, this), \"Visit Library\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 219,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/classroom\",\n                className: \"w-full py-3 border-2 border-yellow-600 text-yellow-600 rounded-full hover:bg-yellow-50 transition-all duration-300 flex items-center justify-center rounded-button whitespace-nowrap cursor-pointer\",\n                children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"fas fa-chalkboard-teacher mr-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 224,\n                  columnNumber: 21\n                }, this), \"Enter Classroom\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 223,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 218,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 216,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 196,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 161,\n        columnNumber: 11\n      }, this), activeTab === 'programs' && /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-3xl font-serif font-bold mb-8 text-gray-800\",\n          children: \"Specialized Programs\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 236,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 md:grid-cols-2 gap-8\",\n          children: programs.map((program, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-lg overflow-hidden shadow-lg border border-yellow-100 hover:shadow-xl transition-all duration-300 group\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-gradient-to-r from-yellow-400 to-yellow-600 h-3\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 240,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-8\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-16 h-16 rounded-full bg-yellow-100 flex items-center justify-center mb-6\",\n                children: /*#__PURE__*/_jsxDEV(\"i\", {\n                  className: `fas ${program.icon} text-yellow-600 text-2xl`\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 243,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 242,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-2xl font-serif font-bold mb-4 text-gray-800 group-hover:text-yellow-600 transition-colors duration-300\",\n                children: program.title\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 245,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-700 mb-6\",\n                children: program.description\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 248,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"px-6 py-2 border border-yellow-600 text-yellow-600 rounded-full hover:bg-yellow-600 hover:text-white transition-all duration-300 flex items-center rounded-button whitespace-nowrap cursor-pointer\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"View Curriculum\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 252,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"fas fa-arrow-right ml-2 transition-transform duration-300 group-hover:translate-x-1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 253,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 251,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 241,\n              columnNumber: 19\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 239,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 237,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 235,\n        columnNumber: 11\n      }, this), activeTab === 'faculty' && /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-3xl font-serif font-bold mb-8 text-gray-800\",\n          children: \"Distinguished Faculty\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 265,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\",\n          children: facultyMembers.map((faculty, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-lg overflow-hidden shadow-md hover:shadow-xl transition-all duration-300 cursor-pointer\",\n            onClick: () => setShowFacultyDetails(showFacultyDetails === index ? null : index),\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative\",\n              children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                src: faculty.image,\n                alt: faculty.name,\n                className: \"w-full h-64 object-cover object-top\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 274,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute inset-0 bg-gradient-to-t from-black/70 to-transparent\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 279,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute bottom-0 left-0 right-0 p-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"text-xl font-serif font-bold text-white\",\n                  children: faculty.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 281,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-yellow-300\",\n                  children: faculty.title\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 282,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 280,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute top-4 right-4 w-16 h-16 rounded-full border-4 border-white bg-gradient-to-br from-yellow-400 to-yellow-600 flex items-center justify-center\",\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-white font-bold text-sm\",\n                  children: faculty.experience\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 285,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 284,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 273,\n              columnNumber: 19\n            }, this), showFacultyDetails === index && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-6 bg-gray-50\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                className: \"font-bold text-gray-800 mb-2\",\n                children: \"Notable Achievements:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 290,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-700 text-sm\",\n                children: faculty.achievements\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 291,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 289,\n              columnNumber: 21\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 268,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 266,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 264,\n        columnNumber: 11\n      }, this), activeTab === 'careers' && /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-3xl font-serif font-bold mb-8 text-gray-800\",\n          children: \"Career Opportunities\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 303,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 md:grid-cols-3 gap-8\",\n          children: [{\n            title: \"Magical Investment Banks\",\n            positions: [\"Junior Gold Transmuter\", \"Market Divination Analyst\", \"Wealth Management Sorcerer\"],\n            icon: \"fa-landmark\"\n          }, {\n            title: \"Alchemical Trading Firms\",\n            positions: [\"Metals Transformation Specialist\", \"Magical Commodities Trader\", \"Alchemical Algorithm Developer\"],\n            icon: \"fa-exchange-alt\"\n          }, {\n            title: \"Mystical Financial Consultancies\",\n            positions: [\"Financial Forecast Diviner\", \"Wealth Protection Warder\", \"Prosperity Spell Consultant\"],\n            icon: \"fa-hand-holding-usd\"\n          }].map((career, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-lg p-6 shadow-lg border border-yellow-100\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-16 h-16 rounded-full bg-yellow-100 flex items-center justify-center mb-6\",\n              children: /*#__PURE__*/_jsxDEV(\"i\", {\n                className: `fas ${career.icon} text-yellow-600 text-2xl`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 324,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 323,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-xl font-serif font-bold mb-4 text-gray-800\",\n              children: career.title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 326,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n              className: \"space-y-2\",\n              children: career.positions.map((position, idx) => /*#__PURE__*/_jsxDEV(\"li\", {\n                className: \"text-gray-700 text-sm flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"fas fa-star text-yellow-500 mr-2 text-xs\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 330,\n                  columnNumber: 25\n                }, this), position]\n              }, idx, true, {\n                fileName: _jsxFileName,\n                lineNumber: 329,\n                columnNumber: 23\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 327,\n              columnNumber: 19\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 322,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 304,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 302,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 141,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 102,\n    columnNumber: 5\n  }, this);\n};\n_s(FinancialDeptPage, \"WiOoyA+VbJp/efDJYMvIHhEgSog=\");\n_c = FinancialDeptPage;\nexport default FinancialDeptPage;\nvar _c;\n$RefreshReg$(_c, \"FinancialDeptPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Link", "jsxDEV", "_jsxDEV", "FinancialDeptPage", "_s", "activeTab", "setActiveTab", "isScrolled", "setIsScrolled", "isMenuOpen", "setIsMenuOpen", "showFacultyDetails", "setShowFacultyDetails", "handleScroll", "window", "scrollY", "addEventListener", "createParticles", "particleContainer", "document", "getElementById", "i", "particle", "createElement", "className", "style", "left", "Math", "random", "top", "duration", "animation", "animationDelay", "append<PERSON><PERSON><PERSON>", "removeEventListener", "facultyMembers", "name", "title", "experience", "achievements", "image", "programs", "description", "icon", "children", "id", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "src", "alt", "map", "tab", "onClick", "char<PERSON>t", "toUpperCase", "slice", "program", "index", "faculty", "positions", "career", "position", "idx", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/FatbeamU/react-app/src/pages/departments/FinancialDeptPage.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Link } from 'react-router-dom';\n\nconst FinancialDeptPage = () => {\n  const [activeTab, setActiveTab] = useState('overview');\n  const [isScrolled, setIsScrolled] = useState(false);\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n  const [showFacultyDetails, setShowFacultyDetails] = useState(null);\n\n  useEffect(() => {\n    const handleScroll = () => {\n      if (window.scrollY > 50) {\n        setIsScrolled(true);\n      } else {\n        setIsScrolled(false);\n      }\n    };\n    \n    window.addEventListener('scroll', handleScroll);\n    \n    // Particle animation\n    const createParticles = () => {\n      const particleContainer = document.getElementById('particle-container');\n      if (!particleContainer) return;\n      \n      for (let i = 0; i < 30; i++) {\n        const particle = document.createElement('div');\n        particle.className = 'absolute w-1 h-1 rounded-full bg-yellow-400 opacity-0';\n        // Random position\n        particle.style.left = `${Math.random() * 100}%`;\n        particle.style.top = `${Math.random() * 100}%`;\n        // Random animation duration\n        const duration = 3 + Math.random() * 5;\n        particle.style.animation = `float ${duration}s ease-in-out infinite`;\n        particle.style.animationDelay = `${Math.random() * 5}s`;\n        particleContainer.appendChild(particle);\n      }\n    };\n    \n    createParticles();\n    \n    return () => {\n      window.removeEventListener('scroll', handleScroll);\n    };\n  }, []);\n\n  const facultyMembers = [\n    {\n      name: \"Professor Goldstein\",\n      title: \"Gold Transmutation Master\",\n      experience: \"45 years\",\n      achievements: \"Discovered the Golden Ratio Spell, Author of 'Modern Alchemical Finance'\",\n      image: \"https://readdy.ai/api/search-image?query=A%20distinguished%20elderly%20wizard%20professor%20with%20a%20neatly%20trimmed%20white%20beard%20and%20gold-rimmed%20spectacles&width=300&height=300&seq=prof1&orientation=squarish\"\n    },\n    {\n      name: \"Dr. Silverton\",\n      title: \"Precious Metals Expert\",\n      experience: \"38 years\",\n      achievements: \"Created the Silver Stream Investment Method, Former Royal Treasury Advisor\",\n      image: \"https://readdy.ai/api/search-image?query=A%20middle-aged%20female%20professor%20with%20silver-streaked%20hair%20in%20an%20elegant%20updo&width=300&height=300&seq=prof2&orientation=squarish\"\n    },\n    {\n      name: \"Master Bronzewing\",\n      title: \"Risk Assessment Specialist\",\n      experience: \"29 years\",\n      achievements: \"Developed the Bronze Shield Protection Spell, Led the Great Market Stabilization of 2018\",\n      image: \"https://readdy.ai/api/search-image?query=A%20confident%20wizard%20in%20his%20thirties%20with%20short%20bronze-colored%20hair%20and%20a%20well-groomed%20beard&width=300&height=300&seq=prof3&orientation=squarish\"\n    },\n    {\n      name: \"Lady Platina\",\n      title: \"Investment Strategy Archmage\",\n      experience: \"42 years\",\n      achievements: \"Inventor of the Platinum Growth Portfolio Spell, Five-time winner of the Golden Cauldron Award\",\n      image: \"https://readdy.ai/api/search-image?query=An%20elegant%20older%20woman%20with%20platinum%20blonde%20hair%20in%20a%20sophisticated%20style&width=300&height=300&seq=prof4&orientation=squarish\"\n    }\n  ];\n\n  const programs = [\n    {\n      title: \"Magical Investment Management\",\n      description: \"Learn to harness magical energies to identify investment opportunities and maximize returns through alchemical transformations.\",\n      icon: \"fa-chart-line\"\n    },\n    {\n      title: \"Alchemical Trading\",\n      description: \"Master the ancient art of transforming base metals into gold while applying modern trading strategies to magical markets.\",\n      icon: \"fa-exchange-alt\"\n    },\n    {\n      title: \"Mystical Risk Assessment\",\n      description: \"Develop your third eye to foresee market fluctuations and create protective wards against financial losses.\",\n      icon: \"fa-shield-alt\"\n    },\n    {\n      title: \"Enchanted Portfolio Management\",\n      description: \"Craft balanced portfolios using divination techniques and magical asset allocation to achieve long-term prosperity.\",\n      icon: \"fa-wallet\"\n    }\n  ];\n\n  return (\n    <div className=\"min-h-screen bg-white text-gray-800 font-sans pt-20\">\n      {/* Particle container for magical effects */}\n      <div id=\"particle-container\" className=\"fixed inset-0 pointer-events-none z-0\"></div>\n      \n      {/* Breadcrumb */}\n      <div className=\"bg-gray-100 py-4\">\n        <div className=\"container mx-auto px-6\">\n          <div className=\"flex items-center text-sm text-gray-600\">\n            <Link to=\"/\" className=\"hover:text-[#15a7dd] transition-colors duration-300 cursor-pointer\">Home</Link>\n            <i className=\"fas fa-chevron-right mx-2 text-xs text-gray-400\"></i>\n            <Link to=\"/departments\" className=\"hover:text-[#15a7dd] transition-colors duration-300 cursor-pointer\">Departments</Link>\n            <i className=\"fas fa-chevron-right mx-2 text-xs text-gray-400\"></i>\n            <span className=\"text-[#15a7dd]\">Financial Department (Alchemists)</span>\n          </div>\n        </div>\n      </div>\n\n      {/* Hero Banner */}\n      <div className=\"relative h-[50vh] min-h-[600px] overflow-hidden\">\n        <img\n          src=\"https://readdy.ai/api/search-image?query=A%20magical%20financial%20department%20with%20floating%20gold%20coins%20and%20magical%20ledgers.%20Wizards%20in%20elegant%20robes%20with%20gold%20trim%20work%20at%20enchanted%20desks&width=1440&height=800&seq=herobanner&orientation=landscape\"\n          alt=\"Financial Department\"\n          className=\"w-full h-full object-cover\"\n        />\n        <div className=\"absolute inset-0 bg-gradient-to-r from-black/70 to-transparent\"></div>\n        <div className=\"absolute inset-0 bg-gradient-to-b from-transparent via-transparent to-white/90\"></div>\n        <div className=\"absolute inset-0 flex items-center\">\n          <div className=\"container mx-auto px-6\">\n            <h1 className=\"text-5xl md:text-6xl lg:text-7xl font-serif font-bold text-white mb-6\">\n              Financial Department <span className=\"bg-clip-text text-transparent bg-gradient-to-r from-yellow-400 to-yellow-600\">(Alchemists)</span>\n            </h1>\n            <p className=\"text-xl md:text-2xl text-white max-w-3xl leading-relaxed\">\n              Where financial expertise meets magical alchemy to transform resources into prosperity through ancient arts and modern techniques.\n            </p>\n          </div>\n        </div>\n      </div>\n\n      {/* Main Content */}\n      <div className=\"container mx-auto px-6 py-12\">\n        {/* Tabs Navigation */}\n        <div className=\"flex flex-wrap border-b border-gray-200 mb-12\">\n          {['overview', 'programs', 'faculty', 'careers'].map((tab) => (\n            <button\n              key={tab}\n              onClick={() => setActiveTab(tab)}\n              className={`px-6 py-3 font-medium text-sm transition-colors duration-300 rounded-button whitespace-nowrap cursor-pointer ${\n                activeTab === tab\n                  ? 'text-[#15a7dd] border-b-2 border-[#15a7dd]'\n                  : 'text-gray-500 hover:text-[#15a7dd]'\n              }`}\n            >\n              {tab.charAt(0).toUpperCase() + tab.slice(1)}\n            </button>\n          ))}\n        </div>\n\n        {/* Overview Section */}\n        {activeTab === 'overview' && (\n          <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-12\">\n            <div className=\"lg:col-span-2\">\n              <h2 className=\"text-3xl font-serif font-bold mb-6 text-gray-800\">Department Overview</h2>\n              <p className=\"text-gray-700 mb-6 leading-relaxed\">\n                The Financial Department, known colloquially as the Alchemists, represents the perfect blend of ancient magical wisdom and cutting-edge financial expertise. Our department trains students in the delicate art of transforming raw resources into financial prosperity through a combination of traditional alchemical practices and modern financial theory.\n              </p>\n              <p className=\"text-gray-700 mb-6 leading-relaxed\">\n                Founded by Master Goldstein in 1823, our department has a long tradition of excellence in magical finance. We pride ourselves on maintaining the highest ethical standards while teaching students to harness the power of financial alchemy for the greater good of the magical community.\n              </p>\n              \n              <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8\">\n                <div className=\"bg-gradient-to-br from-yellow-50 to-yellow-100 rounded-lg p-6 shadow-md\">\n                  <div className=\"text-yellow-600 mb-2\">\n                    <i className=\"fas fa-coins text-3xl\"></i>\n                  </div>\n                  <h3 className=\"text-xl font-bold text-gray-800 mb-2\">98%</h3>\n                  <p className=\"text-gray-600\">Successful Financial Transformations</p>\n                </div>\n                <div className=\"bg-gradient-to-br from-yellow-50 to-yellow-100 rounded-lg p-6 shadow-md\">\n                  <div className=\"text-yellow-600 mb-2\">\n                    <i className=\"fas fa-exchange-alt text-3xl\"></i>\n                  </div>\n                  <h3 className=\"text-xl font-bold text-gray-800 mb-2\">1:42</h3>\n                  <p className=\"text-gray-600\">Gold-to-Value Conversion Rate</p>\n                </div>\n                <div className=\"bg-gradient-to-br from-yellow-50 to-yellow-100 rounded-lg p-6 shadow-md\">\n                  <div className=\"text-yellow-600 mb-2\">\n                    <i className=\"fas fa-user-graduate text-3xl\"></i>\n                  </div>\n                  <h3 className=\"text-xl font-bold text-gray-800 mb-2\">96%</h3>\n                  <p className=\"text-gray-600\">Graduate Placement Rate</p>\n                </div>\n              </div>\n            </div>\n\n            <div className=\"lg:col-span-1\">\n              <div className=\"bg-gradient-to-br from-yellow-50 to-yellow-100 rounded-lg p-6 shadow-md mb-8\">\n                <h3 className=\"text-xl font-serif font-bold mb-4 text-gray-800\">Department Philosophy</h3>\n                <div className=\"mb-6 flex justify-center\">\n                  <div className=\"w-32 h-32 rounded-full bg-gradient-to-br from-yellow-300 to-yellow-500 flex items-center justify-center\">\n                    <img\n                      src=\"https://readdy.ai/api/search-image?query=An%20alchemical%20symbol%20representing%20financial%20transformation&width=200&height=200&seq=symbol&orientation=squarish\"\n                      alt=\"Alchemical Symbol\"\n                      className=\"w-24 h-24 object-contain\"\n                    />\n                  </div>\n                </div>\n                <p className=\"text-gray-700 mb-4\">\n                  \"Through the perfect balance of ancient wisdom and modern innovation, we transform not just metals, but minds and markets.\"\n                </p>\n                <p className=\"text-gray-600 italic text-sm\">\n                  - Founding principle established by Master Goldstein, 1823\n                </p>\n              </div>\n\n              <div className=\"bg-white rounded-lg p-6 shadow-md border border-gray-200\">\n                <h3 className=\"text-xl font-serif font-bold mb-4 text-gray-800\">Quick Actions</h3>\n                <div className=\"space-y-3\">\n                  <Link to=\"/library\" className=\"w-full py-3 bg-gradient-to-r from-yellow-400 to-yellow-600 text-white rounded-full hover:from-yellow-500 hover:to-yellow-700 transition-all duration-300 flex items-center justify-center rounded-button whitespace-nowrap cursor-pointer\">\n                    <i className=\"fas fa-book-reader mr-2\"></i>\n                    Visit Library\n                  </Link>\n                  <Link to=\"/classroom\" className=\"w-full py-3 border-2 border-yellow-600 text-yellow-600 rounded-full hover:bg-yellow-50 transition-all duration-300 flex items-center justify-center rounded-button whitespace-nowrap cursor-pointer\">\n                    <i className=\"fas fa-chalkboard-teacher mr-2\"></i>\n                    Enter Classroom\n                  </Link>\n                </div>\n              </div>\n            </div>\n          </div>\n        )}\n\n        {/* Programs Section */}\n        {activeTab === 'programs' && (\n          <div>\n            <h2 className=\"text-3xl font-serif font-bold mb-8 text-gray-800\">Specialized Programs</h2>\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-8\">\n              {programs.map((program, index) => (\n                <div key={index} className=\"bg-white rounded-lg overflow-hidden shadow-lg border border-yellow-100 hover:shadow-xl transition-all duration-300 group\">\n                  <div className=\"bg-gradient-to-r from-yellow-400 to-yellow-600 h-3\"></div>\n                  <div className=\"p-8\">\n                    <div className=\"w-16 h-16 rounded-full bg-yellow-100 flex items-center justify-center mb-6\">\n                      <i className={`fas ${program.icon} text-yellow-600 text-2xl`}></i>\n                    </div>\n                    <h3 className=\"text-2xl font-serif font-bold mb-4 text-gray-800 group-hover:text-yellow-600 transition-colors duration-300\">\n                      {program.title}\n                    </h3>\n                    <p className=\"text-gray-700 mb-6\">\n                      {program.description}\n                    </p>\n                    <button className=\"px-6 py-2 border border-yellow-600 text-yellow-600 rounded-full hover:bg-yellow-600 hover:text-white transition-all duration-300 flex items-center rounded-button whitespace-nowrap cursor-pointer\">\n                      <span>View Curriculum</span>\n                      <i className=\"fas fa-arrow-right ml-2 transition-transform duration-300 group-hover:translate-x-1\"></i>\n                    </button>\n                  </div>\n                </div>\n              ))}\n            </div>\n          </div>\n        )}\n\n        {/* Faculty Section */}\n        {activeTab === 'faculty' && (\n          <div>\n            <h2 className=\"text-3xl font-serif font-bold mb-8 text-gray-800\">Distinguished Faculty</h2>\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\">\n              {facultyMembers.map((faculty, index) => (\n                <div\n                  key={index}\n                  className=\"bg-white rounded-lg overflow-hidden shadow-md hover:shadow-xl transition-all duration-300 cursor-pointer\"\n                  onClick={() => setShowFacultyDetails(showFacultyDetails === index ? null : index)}\n                >\n                  <div className=\"relative\">\n                    <img\n                      src={faculty.image}\n                      alt={faculty.name}\n                      className=\"w-full h-64 object-cover object-top\"\n                    />\n                    <div className=\"absolute inset-0 bg-gradient-to-t from-black/70 to-transparent\"></div>\n                    <div className=\"absolute bottom-0 left-0 right-0 p-4\">\n                      <h3 className=\"text-xl font-serif font-bold text-white\">{faculty.name}</h3>\n                      <p className=\"text-yellow-300\">{faculty.title}</p>\n                    </div>\n                    <div className=\"absolute top-4 right-4 w-16 h-16 rounded-full border-4 border-white bg-gradient-to-br from-yellow-400 to-yellow-600 flex items-center justify-center\">\n                      <span className=\"text-white font-bold text-sm\">{faculty.experience}</span>\n                    </div>\n                  </div>\n                  {showFacultyDetails === index && (\n                    <div className=\"p-6 bg-gray-50\">\n                      <h4 className=\"font-bold text-gray-800 mb-2\">Notable Achievements:</h4>\n                      <p className=\"text-gray-700 text-sm\">{faculty.achievements}</p>\n                    </div>\n                  )}\n                </div>\n              ))}\n            </div>\n          </div>\n        )}\n\n        {/* Careers Section */}\n        {activeTab === 'careers' && (\n          <div>\n            <h2 className=\"text-3xl font-serif font-bold mb-8 text-gray-800\">Career Opportunities</h2>\n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8\">\n              {[\n                {\n                  title: \"Magical Investment Banks\",\n                  positions: [\"Junior Gold Transmuter\", \"Market Divination Analyst\", \"Wealth Management Sorcerer\"],\n                  icon: \"fa-landmark\"\n                },\n                {\n                  title: \"Alchemical Trading Firms\",\n                  positions: [\"Metals Transformation Specialist\", \"Magical Commodities Trader\", \"Alchemical Algorithm Developer\"],\n                  icon: \"fa-exchange-alt\"\n                },\n                {\n                  title: \"Mystical Financial Consultancies\",\n                  positions: [\"Financial Forecast Diviner\", \"Wealth Protection Warder\", \"Prosperity Spell Consultant\"],\n                  icon: \"fa-hand-holding-usd\"\n                }\n              ].map((career, index) => (\n                <div key={index} className=\"bg-white rounded-lg p-6 shadow-lg border border-yellow-100\">\n                  <div className=\"w-16 h-16 rounded-full bg-yellow-100 flex items-center justify-center mb-6\">\n                    <i className={`fas ${career.icon} text-yellow-600 text-2xl`}></i>\n                  </div>\n                  <h3 className=\"text-xl font-serif font-bold mb-4 text-gray-800\">{career.title}</h3>\n                  <ul className=\"space-y-2\">\n                    {career.positions.map((position, idx) => (\n                      <li key={idx} className=\"text-gray-700 text-sm flex items-center\">\n                        <i className=\"fas fa-star text-yellow-500 mr-2 text-xs\"></i>\n                        {position}\n                      </li>\n                    ))}\n                  </ul>\n                </div>\n              ))}\n            </div>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default FinancialDeptPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,IAAI,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExC,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC9B,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGR,QAAQ,CAAC,UAAU,CAAC;EACtD,MAAM,CAACS,UAAU,EAAEC,aAAa,CAAC,GAAGV,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACW,UAAU,EAAEC,aAAa,CAAC,GAAGZ,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACa,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGd,QAAQ,CAAC,IAAI,CAAC;EAElEC,SAAS,CAAC,MAAM;IACd,MAAMc,YAAY,GAAGA,CAAA,KAAM;MACzB,IAAIC,MAAM,CAACC,OAAO,GAAG,EAAE,EAAE;QACvBP,aAAa,CAAC,IAAI,CAAC;MACrB,CAAC,MAAM;QACLA,aAAa,CAAC,KAAK,CAAC;MACtB;IACF,CAAC;IAEDM,MAAM,CAACE,gBAAgB,CAAC,QAAQ,EAAEH,YAAY,CAAC;;IAE/C;IACA,MAAMI,eAAe,GAAGA,CAAA,KAAM;MAC5B,MAAMC,iBAAiB,GAAGC,QAAQ,CAACC,cAAc,CAAC,oBAAoB,CAAC;MACvE,IAAI,CAACF,iBAAiB,EAAE;MAExB,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE;QAC3B,MAAMC,QAAQ,GAAGH,QAAQ,CAACI,aAAa,CAAC,KAAK,CAAC;QAC9CD,QAAQ,CAACE,SAAS,GAAG,uDAAuD;QAC5E;QACAF,QAAQ,CAACG,KAAK,CAACC,IAAI,GAAG,GAAGC,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG;QAC/CN,QAAQ,CAACG,KAAK,CAACI,GAAG,GAAG,GAAGF,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG;QAC9C;QACA,MAAME,QAAQ,GAAG,CAAC,GAAGH,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,CAAC;QACtCN,QAAQ,CAACG,KAAK,CAACM,SAAS,GAAG,SAASD,QAAQ,wBAAwB;QACpER,QAAQ,CAACG,KAAK,CAACO,cAAc,GAAG,GAAGL,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG;QACvDV,iBAAiB,CAACe,WAAW,CAACX,QAAQ,CAAC;MACzC;IACF,CAAC;IAEDL,eAAe,CAAC,CAAC;IAEjB,OAAO,MAAM;MACXH,MAAM,CAACoB,mBAAmB,CAAC,QAAQ,EAAErB,YAAY,CAAC;IACpD,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMsB,cAAc,GAAG,CACrB;IACEC,IAAI,EAAE,qBAAqB;IAC3BC,KAAK,EAAE,2BAA2B;IAClCC,UAAU,EAAE,UAAU;IACtBC,YAAY,EAAE,0EAA0E;IACxFC,KAAK,EAAE;EACT,CAAC,EACD;IACEJ,IAAI,EAAE,eAAe;IACrBC,KAAK,EAAE,wBAAwB;IAC/BC,UAAU,EAAE,UAAU;IACtBC,YAAY,EAAE,4EAA4E;IAC1FC,KAAK,EAAE;EACT,CAAC,EACD;IACEJ,IAAI,EAAE,mBAAmB;IACzBC,KAAK,EAAE,4BAA4B;IACnCC,UAAU,EAAE,UAAU;IACtBC,YAAY,EAAE,0FAA0F;IACxGC,KAAK,EAAE;EACT,CAAC,EACD;IACEJ,IAAI,EAAE,cAAc;IACpBC,KAAK,EAAE,8BAA8B;IACrCC,UAAU,EAAE,UAAU;IACtBC,YAAY,EAAE,gGAAgG;IAC9GC,KAAK,EAAE;EACT,CAAC,CACF;EAED,MAAMC,QAAQ,GAAG,CACf;IACEJ,KAAK,EAAE,+BAA+B;IACtCK,WAAW,EAAE,iIAAiI;IAC9IC,IAAI,EAAE;EACR,CAAC,EACD;IACEN,KAAK,EAAE,oBAAoB;IAC3BK,WAAW,EAAE,2HAA2H;IACxIC,IAAI,EAAE;EACR,CAAC,EACD;IACEN,KAAK,EAAE,0BAA0B;IACjCK,WAAW,EAAE,6GAA6G;IAC1HC,IAAI,EAAE;EACR,CAAC,EACD;IACEN,KAAK,EAAE,gCAAgC;IACvCK,WAAW,EAAE,qHAAqH;IAClIC,IAAI,EAAE;EACR,CAAC,CACF;EAED,oBACEzC,OAAA;IAAKsB,SAAS,EAAC,qDAAqD;IAAAoB,QAAA,gBAElE1C,OAAA;MAAK2C,EAAE,EAAC,oBAAoB;MAACrB,SAAS,EAAC;IAAuC;MAAAsB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,eAGrF/C,OAAA;MAAKsB,SAAS,EAAC,kBAAkB;MAAAoB,QAAA,eAC/B1C,OAAA;QAAKsB,SAAS,EAAC,wBAAwB;QAAAoB,QAAA,eACrC1C,OAAA;UAAKsB,SAAS,EAAC,yCAAyC;UAAAoB,QAAA,gBACtD1C,OAAA,CAACF,IAAI;YAACkD,EAAE,EAAC,GAAG;YAAC1B,SAAS,EAAC,oEAAoE;YAAAoB,QAAA,EAAC;UAAI;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACvG/C,OAAA;YAAGsB,SAAS,EAAC;UAAiD;YAAAsB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACnE/C,OAAA,CAACF,IAAI;YAACkD,EAAE,EAAC,cAAc;YAAC1B,SAAS,EAAC,oEAAoE;YAAAoB,QAAA,EAAC;UAAW;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACzH/C,OAAA;YAAGsB,SAAS,EAAC;UAAiD;YAAAsB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACnE/C,OAAA;YAAMsB,SAAS,EAAC,gBAAgB;YAAAoB,QAAA,EAAC;UAAiC;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN/C,OAAA;MAAKsB,SAAS,EAAC,iDAAiD;MAAAoB,QAAA,gBAC9D1C,OAAA;QACEiD,GAAG,EAAC,4RAA4R;QAChSC,GAAG,EAAC,sBAAsB;QAC1B5B,SAAS,EAAC;MAA4B;QAAAsB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvC,CAAC,eACF/C,OAAA;QAAKsB,SAAS,EAAC;MAAgE;QAAAsB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACtF/C,OAAA;QAAKsB,SAAS,EAAC;MAAgF;QAAAsB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACtG/C,OAAA;QAAKsB,SAAS,EAAC,oCAAoC;QAAAoB,QAAA,eACjD1C,OAAA;UAAKsB,SAAS,EAAC,wBAAwB;UAAAoB,QAAA,gBACrC1C,OAAA;YAAIsB,SAAS,EAAC,uEAAuE;YAAAoB,QAAA,GAAC,uBAC/D,eAAA1C,OAAA;cAAMsB,SAAS,EAAC,8EAA8E;cAAAoB,QAAA,EAAC;YAAY;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrI,CAAC,eACL/C,OAAA;YAAGsB,SAAS,EAAC,0DAA0D;YAAAoB,QAAA,EAAC;UAExE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN/C,OAAA;MAAKsB,SAAS,EAAC,8BAA8B;MAAAoB,QAAA,gBAE3C1C,OAAA;QAAKsB,SAAS,EAAC,+CAA+C;QAAAoB,QAAA,EAC3D,CAAC,UAAU,EAAE,UAAU,EAAE,SAAS,EAAE,SAAS,CAAC,CAACS,GAAG,CAAEC,GAAG,iBACtDpD,OAAA;UAEEqD,OAAO,EAAEA,CAAA,KAAMjD,YAAY,CAACgD,GAAG,CAAE;UACjC9B,SAAS,EAAE,gHACTnB,SAAS,KAAKiD,GAAG,GACb,4CAA4C,GAC5C,oCAAoC,EACvC;UAAAV,QAAA,EAEFU,GAAG,CAACE,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAGH,GAAG,CAACI,KAAK,CAAC,CAAC;QAAC,GARtCJ,GAAG;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OASF,CACT;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,EAGL5C,SAAS,KAAK,UAAU,iBACvBH,OAAA;QAAKsB,SAAS,EAAC,wCAAwC;QAAAoB,QAAA,gBACrD1C,OAAA;UAAKsB,SAAS,EAAC,eAAe;UAAAoB,QAAA,gBAC5B1C,OAAA;YAAIsB,SAAS,EAAC,kDAAkD;YAAAoB,QAAA,EAAC;UAAmB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACzF/C,OAAA;YAAGsB,SAAS,EAAC,oCAAoC;YAAAoB,QAAA,EAAC;UAElD;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJ/C,OAAA;YAAGsB,SAAS,EAAC,oCAAoC;YAAAoB,QAAA,EAAC;UAElD;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAEJ/C,OAAA;YAAKsB,SAAS,EAAC,4CAA4C;YAAAoB,QAAA,gBACzD1C,OAAA;cAAKsB,SAAS,EAAC,yEAAyE;cAAAoB,QAAA,gBACtF1C,OAAA;gBAAKsB,SAAS,EAAC,sBAAsB;gBAAAoB,QAAA,eACnC1C,OAAA;kBAAGsB,SAAS,EAAC;gBAAuB;kBAAAsB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtC,CAAC,eACN/C,OAAA;gBAAIsB,SAAS,EAAC,sCAAsC;gBAAAoB,QAAA,EAAC;cAAG;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC7D/C,OAAA;gBAAGsB,SAAS,EAAC,eAAe;gBAAAoB,QAAA,EAAC;cAAoC;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClE,CAAC,eACN/C,OAAA;cAAKsB,SAAS,EAAC,yEAAyE;cAAAoB,QAAA,gBACtF1C,OAAA;gBAAKsB,SAAS,EAAC,sBAAsB;gBAAAoB,QAAA,eACnC1C,OAAA;kBAAGsB,SAAS,EAAC;gBAA8B;kBAAAsB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7C,CAAC,eACN/C,OAAA;gBAAIsB,SAAS,EAAC,sCAAsC;gBAAAoB,QAAA,EAAC;cAAI;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC9D/C,OAAA;gBAAGsB,SAAS,EAAC,eAAe;gBAAAoB,QAAA,EAAC;cAA6B;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3D,CAAC,eACN/C,OAAA;cAAKsB,SAAS,EAAC,yEAAyE;cAAAoB,QAAA,gBACtF1C,OAAA;gBAAKsB,SAAS,EAAC,sBAAsB;gBAAAoB,QAAA,eACnC1C,OAAA;kBAAGsB,SAAS,EAAC;gBAA+B;kBAAAsB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9C,CAAC,eACN/C,OAAA;gBAAIsB,SAAS,EAAC,sCAAsC;gBAAAoB,QAAA,EAAC;cAAG;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC7D/C,OAAA;gBAAGsB,SAAS,EAAC,eAAe;gBAAAoB,QAAA,EAAC;cAAuB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN/C,OAAA;UAAKsB,SAAS,EAAC,eAAe;UAAAoB,QAAA,gBAC5B1C,OAAA;YAAKsB,SAAS,EAAC,8EAA8E;YAAAoB,QAAA,gBAC3F1C,OAAA;cAAIsB,SAAS,EAAC,iDAAiD;cAAAoB,QAAA,EAAC;YAAqB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC1F/C,OAAA;cAAKsB,SAAS,EAAC,0BAA0B;cAAAoB,QAAA,eACvC1C,OAAA;gBAAKsB,SAAS,EAAC,yGAAyG;gBAAAoB,QAAA,eACtH1C,OAAA;kBACEiD,GAAG,EAAC,oKAAoK;kBACxKC,GAAG,EAAC,mBAAmB;kBACvB5B,SAAS,EAAC;gBAA0B;kBAAAsB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACN/C,OAAA;cAAGsB,SAAS,EAAC,oBAAoB;cAAAoB,QAAA,EAAC;YAElC;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACJ/C,OAAA;cAAGsB,SAAS,EAAC,8BAA8B;cAAAoB,QAAA,EAAC;YAE5C;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eAEN/C,OAAA;YAAKsB,SAAS,EAAC,0DAA0D;YAAAoB,QAAA,gBACvE1C,OAAA;cAAIsB,SAAS,EAAC,iDAAiD;cAAAoB,QAAA,EAAC;YAAa;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAClF/C,OAAA;cAAKsB,SAAS,EAAC,WAAW;cAAAoB,QAAA,gBACxB1C,OAAA,CAACF,IAAI;gBAACkD,EAAE,EAAC,UAAU;gBAAC1B,SAAS,EAAC,2OAA2O;gBAAAoB,QAAA,gBACvQ1C,OAAA;kBAAGsB,SAAS,EAAC;gBAAyB;kBAAAsB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,iBAE7C;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACP/C,OAAA,CAACF,IAAI;gBAACkD,EAAE,EAAC,YAAY;gBAAC1B,SAAS,EAAC,qMAAqM;gBAAAoB,QAAA,gBACnO1C,OAAA;kBAAGsB,SAAS,EAAC;gBAAgC;kBAAAsB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,mBAEpD;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAGA5C,SAAS,KAAK,UAAU,iBACvBH,OAAA;QAAA0C,QAAA,gBACE1C,OAAA;UAAIsB,SAAS,EAAC,kDAAkD;UAAAoB,QAAA,EAAC;QAAoB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC1F/C,OAAA;UAAKsB,SAAS,EAAC,uCAAuC;UAAAoB,QAAA,EACnDH,QAAQ,CAACY,GAAG,CAAC,CAACM,OAAO,EAAEC,KAAK,kBAC3B1D,OAAA;YAAiBsB,SAAS,EAAC,0HAA0H;YAAAoB,QAAA,gBACnJ1C,OAAA;cAAKsB,SAAS,EAAC;YAAoD;cAAAsB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC1E/C,OAAA;cAAKsB,SAAS,EAAC,KAAK;cAAAoB,QAAA,gBAClB1C,OAAA;gBAAKsB,SAAS,EAAC,4EAA4E;gBAAAoB,QAAA,eACzF1C,OAAA;kBAAGsB,SAAS,EAAE,OAAOmC,OAAO,CAAChB,IAAI;gBAA4B;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/D,CAAC,eACN/C,OAAA;gBAAIsB,SAAS,EAAC,6GAA6G;gBAAAoB,QAAA,EACxHe,OAAO,CAACtB;cAAK;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACZ,CAAC,eACL/C,OAAA;gBAAGsB,SAAS,EAAC,oBAAoB;gBAAAoB,QAAA,EAC9Be,OAAO,CAACjB;cAAW;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnB,CAAC,eACJ/C,OAAA;gBAAQsB,SAAS,EAAC,oMAAoM;gBAAAoB,QAAA,gBACpN1C,OAAA;kBAAA0C,QAAA,EAAM;gBAAe;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC5B/C,OAAA;kBAAGsB,SAAS,EAAC;gBAAqF;kBAAAsB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA,GAhBEW,KAAK;YAAAd,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAiBV,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAGA5C,SAAS,KAAK,SAAS,iBACtBH,OAAA;QAAA0C,QAAA,gBACE1C,OAAA;UAAIsB,SAAS,EAAC,kDAAkD;UAAAoB,QAAA,EAAC;QAAqB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC3F/C,OAAA;UAAKsB,SAAS,EAAC,sDAAsD;UAAAoB,QAAA,EAClET,cAAc,CAACkB,GAAG,CAAC,CAACQ,OAAO,EAAED,KAAK,kBACjC1D,OAAA;YAEEsB,SAAS,EAAC,0GAA0G;YACpH+B,OAAO,EAAEA,CAAA,KAAM3C,qBAAqB,CAACD,kBAAkB,KAAKiD,KAAK,GAAG,IAAI,GAAGA,KAAK,CAAE;YAAAhB,QAAA,gBAElF1C,OAAA;cAAKsB,SAAS,EAAC,UAAU;cAAAoB,QAAA,gBACvB1C,OAAA;gBACEiD,GAAG,EAAEU,OAAO,CAACrB,KAAM;gBACnBY,GAAG,EAAES,OAAO,CAACzB,IAAK;gBAClBZ,SAAS,EAAC;cAAqC;gBAAAsB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChD,CAAC,eACF/C,OAAA;gBAAKsB,SAAS,EAAC;cAAgE;gBAAAsB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACtF/C,OAAA;gBAAKsB,SAAS,EAAC,sCAAsC;gBAAAoB,QAAA,gBACnD1C,OAAA;kBAAIsB,SAAS,EAAC,yCAAyC;kBAAAoB,QAAA,EAAEiB,OAAO,CAACzB;gBAAI;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC3E/C,OAAA;kBAAGsB,SAAS,EAAC,iBAAiB;kBAAAoB,QAAA,EAAEiB,OAAO,CAACxB;gBAAK;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/C,CAAC,eACN/C,OAAA;gBAAKsB,SAAS,EAAC,sJAAsJ;gBAAAoB,QAAA,eACnK1C,OAAA;kBAAMsB,SAAS,EAAC,8BAA8B;kBAAAoB,QAAA,EAAEiB,OAAO,CAACvB;gBAAU;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,EACLtC,kBAAkB,KAAKiD,KAAK,iBAC3B1D,OAAA;cAAKsB,SAAS,EAAC,gBAAgB;cAAAoB,QAAA,gBAC7B1C,OAAA;gBAAIsB,SAAS,EAAC,8BAA8B;gBAAAoB,QAAA,EAAC;cAAqB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACvE/C,OAAA;gBAAGsB,SAAS,EAAC,uBAAuB;gBAAAoB,QAAA,EAAEiB,OAAO,CAACtB;cAAY;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5D,CACN;UAAA,GAxBIW,KAAK;YAAAd,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAyBP,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAGA5C,SAAS,KAAK,SAAS,iBACtBH,OAAA;QAAA0C,QAAA,gBACE1C,OAAA;UAAIsB,SAAS,EAAC,kDAAkD;UAAAoB,QAAA,EAAC;QAAoB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC1F/C,OAAA;UAAKsB,SAAS,EAAC,uCAAuC;UAAAoB,QAAA,EACnD,CACC;YACEP,KAAK,EAAE,0BAA0B;YACjCyB,SAAS,EAAE,CAAC,wBAAwB,EAAE,2BAA2B,EAAE,4BAA4B,CAAC;YAChGnB,IAAI,EAAE;UACR,CAAC,EACD;YACEN,KAAK,EAAE,0BAA0B;YACjCyB,SAAS,EAAE,CAAC,kCAAkC,EAAE,4BAA4B,EAAE,gCAAgC,CAAC;YAC/GnB,IAAI,EAAE;UACR,CAAC,EACD;YACEN,KAAK,EAAE,kCAAkC;YACzCyB,SAAS,EAAE,CAAC,4BAA4B,EAAE,0BAA0B,EAAE,6BAA6B,CAAC;YACpGnB,IAAI,EAAE;UACR,CAAC,CACF,CAACU,GAAG,CAAC,CAACU,MAAM,EAAEH,KAAK,kBAClB1D,OAAA;YAAiBsB,SAAS,EAAC,4DAA4D;YAAAoB,QAAA,gBACrF1C,OAAA;cAAKsB,SAAS,EAAC,4EAA4E;cAAAoB,QAAA,eACzF1C,OAAA;gBAAGsB,SAAS,EAAE,OAAOuC,MAAM,CAACpB,IAAI;cAA4B;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9D,CAAC,eACN/C,OAAA;cAAIsB,SAAS,EAAC,iDAAiD;cAAAoB,QAAA,EAAEmB,MAAM,CAAC1B;YAAK;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACnF/C,OAAA;cAAIsB,SAAS,EAAC,WAAW;cAAAoB,QAAA,EACtBmB,MAAM,CAACD,SAAS,CAACT,GAAG,CAAC,CAACW,QAAQ,EAAEC,GAAG,kBAClC/D,OAAA;gBAAcsB,SAAS,EAAC,yCAAyC;gBAAAoB,QAAA,gBAC/D1C,OAAA;kBAAGsB,SAAS,EAAC;gBAA0C;kBAAAsB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,EAC3De,QAAQ;cAAA,GAFFC,GAAG;gBAAAnB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAGR,CACL;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC;UAAA,GAZGW,KAAK;YAAAd,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAaV,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC7C,EAAA,CAnVID,iBAAiB;AAAA+D,EAAA,GAAjB/D,iBAAiB;AAqVvB,eAAeA,iBAAiB;AAAC,IAAA+D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}