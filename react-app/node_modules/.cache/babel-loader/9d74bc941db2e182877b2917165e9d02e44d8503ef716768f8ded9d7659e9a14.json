{"ast": null, "code": "import React,{useState,useEffect}from'react';import{Link}from'react-router-dom';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const MainPage=_ref=>{let{user,moodleData}=_ref;const[activeTab,setActiveTab]=useState('home');useEffect(()=>{// Particle animation\nconst createParticles=()=>{const particleContainer=document.getElementById('particle-container');if(!particleContainer)return;for(let i=0;i<30;i++){const particle=document.createElement('div');particle.className='absolute w-1 h-1 rounded-full bg-blue-500 opacity-0';// Random position\nparticle.style.left=\"\".concat(Math.random()*100,\"%\");particle.style.top=\"\".concat(Math.random()*100,\"%\");// Random animation duration\nconst duration=3+Math.random()*5;particle.style.animation=\"float \".concat(duration,\"s ease-in-out infinite\");particle.style.animationDelay=\"\".concat(Math.random()*5,\"s\");particleContainer.appendChild(particle);}};createParticles();},[]);const scrollToSection=sectionId=>{const section=document.getElementById(sectionId);if(section){section.scrollIntoView({behavior:'smooth'});setActiveTab(sectionId);}};return/*#__PURE__*/_jsxs(\"div\",{className:\"min-h-screen bg-white text-gray-800 font-sans\",children:[/*#__PURE__*/_jsx(\"div\",{id:\"particle-container\",className:\"fixed inset-0 pointer-events-none z-0\"}),/*#__PURE__*/_jsxs(\"section\",{id:\"home\",className:\"relative min-h-screen flex items-center pt-20\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"absolute inset-0 z-0 overflow-hidden\",children:[/*#__PURE__*/_jsx(\"img\",{src:\"https://readdy.ai/api/search-image?query=A%20majestic%20magical%20castle%20with%20tall%20spires%20and%20towers%20against%20a%20dramatic%20sunset%20sky.%20The%20central%20tower%20features%20glowing%20blue%20magical%20energy%20flowing%20through%20crystalline%20formations.%20Multiple%20magical%20circular%20platforms%20with%20blue%20energy%20rings%20hover%20around%20the%20castle.%20Stone%20pathways%20lead%20to%20the%20grand%20entrance%2C%20while%20mystical%20blue%20banners%20flutter%20in%20the%20wind.%20Mountains%20and%20waterfalls%20frame%20the%20scene%20with%20ethereal%20lighting&width=1920&height=1080&seq=hero2&orientation=landscape\",alt:\"Magical Castle\",className:\"w-full h-full object-cover object-center animate-gentle-zoom\"}),/*#__PURE__*/_jsx(\"div\",{className:\"absolute inset-0 bg-gradient-to-r from-[#201f1f]/60 via-transparent to-transparent\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"absolute inset-0\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"absolute bottom-0 left-0 right-0 h-32 bg-gradient-to-t from-[#201f1f]/40 to-transparent\"}),Array.from({length:5}).map((_,i)=>/*#__PURE__*/_jsx(\"div\",{className:\"absolute w-32 h-32 rounded-full\",style:{left:\"\".concat(Math.random()*100,\"%\"),top:\"\".concat(Math.random()*100,\"%\"),background:'radial-gradient(circle, rgba(21, 167, 221, 0.2) 0%, rgba(21, 167, 221, 0) 70%)',animation:\"pulse \".concat(2+Math.random()*2,\"s infinite\"),transform:'translate(-50%, -50%)'}},i))]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"container mx-auto px-6 flex flex-col md:flex-row items-center relative z-10\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"w-full md:w-1/2 text-white mb-10 md:mb-0\",children:[/*#__PURE__*/_jsxs(\"h1\",{className:\"text-4xl md:text-5xl lg:text-6xl font-serif font-bold mb-6\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-[#15a7dd]\",children:\"Fiber Optic\"}),\" Technomancy\"]}),/*#__PURE__*/_jsx(\"p\",{className:\"text-lg md:text-xl mb-8 text-gray-100\",children:\"Welcome to Fatbeam Fiber University, where the ancient art of magical networking meets modern fiber technology. Begin your journey into the mystical realm of high-speed connectivity.\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-col sm:flex-row space-y-4 sm:space-y-0 sm:space-x-4\",children:[/*#__PURE__*/_jsxs(Link,{to:\"/library\",className:\"px-8 py-3 bg-[#15a7dd] text-white rounded-full hover:bg-[#1397c7] transition-all duration-300 flex items-center justify-center rounded-button whitespace-nowrap cursor-pointer\",children:[/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-book-open mr-2\"}),\"Arcane Library\"]}),/*#__PURE__*/_jsxs(Link,{to:\"/classroom\",className:\"px-8 py-3 bg-transparent border-2 border-[#15a7dd] text-white rounded-full hover:bg-[#15a7dd]/20 transition-all duration-300 flex items-center justify-center rounded-button whitespace-nowrap cursor-pointer\",children:[/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-hat-wizard mr-2\"}),\"Training Hall\"]})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"absolute top-4 right-4 z-20\",children:/*#__PURE__*/_jsx(\"button\",{onClick:()=>{const heroImage=document.querySelector('.animate-gentle-zoom');if(heroImage){heroImage.style.animationPlayState=heroImage.style.animationPlayState==='paused'?'running':'paused';}},className:\"w-12 h-12 rounded-full bg-[#15a7dd]/20 flex items-center justify-center cursor-pointer hover:bg-[#15a7dd]/30 transition-colors duration-300 rounded-button whitespace-nowrap animate-continuous-fade\",children:/*#__PURE__*/_jsx(\"div\",{className:\"w-8 h-8 rounded-full bg-[#15a7dd]/30 flex items-center justify-center\",children:/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-play text-white text-sm\"})})})}),/*#__PURE__*/_jsx(\"div\",{className:\"absolute -bottom-5 -right-5 bg-[#6a3293] text-white p-4 rounded-lg shadow-lg transform rotate-3\",children:/*#__PURE__*/_jsxs(\"p\",{className:\"text-sm font-medium\",children:[/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-star mr-1\"}),\"New Courses Added Weekly\"]})})]}),/*#__PURE__*/_jsx(\"div\",{className:\"absolute bottom-10 left-1/2 transform -translate-x-1/2 text-white animate-bounce\",children:/*#__PURE__*/_jsxs(\"button\",{onClick:()=>scrollToSection('about'),className:\"flex flex-col items-center rounded-button whitespace-nowrap cursor-pointer\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-sm mb-2\",children:\"Scroll to Discover\"}),/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-chevron-down\"})]})})]}),/*#__PURE__*/_jsx(\"section\",{id:\"about\",className:\"py-20 bg-[#f2f2f3]\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"container mx-auto px-6\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-center mb-16\",children:[/*#__PURE__*/_jsxs(\"h2\",{className:\"text-3xl md:text-4xl font-serif font-bold mb-4\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-[#15a7dd]\",children:\"About\"}),\" Our Mystical Academy\"]}),/*#__PURE__*/_jsx(\"div\",{className:\"w-20 h-1 bg-[#15a7dd] mx-auto\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-1 md:grid-cols-2 gap-12 items-center\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h3\",{className:\"text-2xl font-serif font-bold mb-6 text-[#475467]\",children:\"Where Technology Meets Magic\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-[#475467] mb-6\",children:\"Fatbeam Fiber University was founded in the ancient year of 2010 by a council of network sorcerers who sought to blend the arcane arts with cutting-edge fiber technology. Our institution stands as a beacon of knowledge in the realm of digital enchantment.\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-[#475467] mb-6\",children:\"Our mystical instructors harness the power of light itself, channeling it through glass threads to connect the farthest reaches of our kingdom. Students learn to weave spells of connectivity, cast protective wards against cyber threats, and summon data from the ethereal cloud.\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-4\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-12 h-12 rounded-full bg-[#15a7dd] flex items-center justify-center\",children:/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-graduation-cap text-white\"})}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h4\",{className:\"font-bold text-[#475467]\",children:\"500+ Graduates\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-sm text-[#475467]\",children:\"Master Technomancers in the field\"})]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"relative\",children:[/*#__PURE__*/_jsx(\"img\",{src:\"https://readdy.ai/api/search-image?query=A%20mystical%20library%20with%20floating%20holographic%20screens%20displaying%20network%20diagrams.%20Ancient%20tomes%20with%20glowing%20blue%20runes%20sit%20on%20shelves%20alongside%20modern%20server%20equipment.%20Blue%20magical%20energy%20flows%20between%20books%20and%20digital%20displays.%20Fiber%20optic%20cables%20emit%20blue%20light%20across%20the%20room&width=600&height=600&seq=about1&orientation=squarish\",alt:\"Mystical Academy\",className:\"w-full h-auto rounded-lg shadow-xl\"}),/*#__PURE__*/_jsx(\"div\",{className:\"absolute -bottom-6 -left-6 bg-white p-4 rounded-lg shadow-lg\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-2\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-10 h-10 rounded-full bg-[#6a3293] flex items-center justify-center\",children:/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-magic text-white\"})}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h4\",{className:\"font-bold text-[#475467]\",children:\"15 Years\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-xs text-[#475467]\",children:\"Of Magical Innovation\"})]})]})})]})]})]})}),/*#__PURE__*/_jsx(\"section\",{id:\"departments\",className:\"py-20 bg-white\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"container mx-auto px-6\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-center mb-16\",children:[/*#__PURE__*/_jsxs(\"h2\",{className:\"text-3xl md:text-4xl font-serif font-bold mb-4\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-[#15a7dd]\",children:\"Mystical\"}),\" Departments\"]}),/*#__PURE__*/_jsx(\"p\",{className:\"text-[#475467] max-w-2xl mx-auto\",children:\"Explore our specialized schools of technomancy, each focusing on a different aspect of the magical fiber arts.\"}),/*#__PURE__*/_jsx(\"div\",{className:\"w-20 h-1 bg-[#15a7dd] mx-auto mt-4\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\",children:[{title:\"Finance Alchemy\",icon:\"fa-coins\",route:\"/FinanceDept\",image:\"https://readdy.ai/api/search-image?query=A%20mystical%20treasury%20room%20with%20floating%20gold%20coins%20and%20glowing%20blue%20financial%20charts.%20Ancient%20ledgers%20with%20magical%20runes%20sit%20beside%20modern%20computers.%20Blue%20magical%20energy%20connects%20ledgers%20to%20digital%20displays.%20Wizards%20in%20business%20attire%20cast%20financial%20spells&width=400&height=300&seq=dept1&orientation=landscape\",description:\"Transform raw resources into financial prosperity through the ancient arts of budget management and revenue enchantment.\"},{title:\"Tower Levitation\",icon:\"fa-tower-broadcast\",route:\"/TowerTechnicians\",image:\"https://readdy.ai/api/search-image?query=Magical%20technicians%20working%20on%20a%20tall%20communications%20tower%20with%20glowing%20blue%20fiber%20optic%20connections.%20Wizards%20in%20safety%20gear%20casting%20maintenance%20spells.%20Blue%20energy%20flows%20through%20the%20tower%20structure.%20Technical%20diagrams%20float%20as%20magical%20holograms%20around%20the%20workers&width=400&height=300&seq=dept2&orientation=landscape\",description:\"Master the heights of our magical infrastructure, ensuring the uninterrupted flow of arcane data through our network.\"},{title:\"Sales Sorcery\",icon:\"fa-handshake\",route:\"/SalesDept\",image:\"https://readdy.ai/api/search-image?query=Wizards%20in%20business%20attire%20demonstrating%20magical%20fiber%20optic%20products%20to%20clients.%20Glowing%20blue%20holograms%20show%20network%20capabilities.%20Magical%20contracts%20with%20glowing%20signatures.%20Enchanted%20presentation%20room%20with%20fiber%20optic%20decorations%20and%20blue%20energy%20flowing%20through%20displays&width=400&height=300&seq=dept3&orientation=landscape\",description:\"Learn the mystical arts of persuasion and relationship building to spread our magical connectivity throughout the realm.\"},{title:\"Construction Earth Magic\",icon:\"fa-shovel\",route:\"/ConstructionDept\",image:\"https://readdy.ai/api/search-image?query=Wizards%20using%20magical%20tools%20to%20install%20underground%20fiber%20optic%20cables.%20Earth%20elementals%20helping%20to%20move%20soil.%20Blue%20glowing%20trenches%20with%20fiber%20lines%20being%20laid.%20Magical%20mapping%20tools%20projecting%20underground%20pathways.%20Construction%20site%20with%20both%20magical%20and%20modern%20equipment&width=400&height=300&seq=dept4&orientation=landscape\",description:\"Harness the elements to lay the physical foundations of our network, connecting the material world to the digital realm.\"},{title:\"Human Relations\",icon:\"fa-brain\",route:\"/HumanRelationsDept\",image:\"https://readdy.ai/api/search-image?query=A%20serene%20office%20space%20with%20floating%20crystal%20orbs%20showing%20employee%20auras.%20Psychic%20HR%20wizards%20conducting%20telepathic%20interviews.%20Glowing%20blue%20filing%20cabinets%20with%20magical%20employee%20records.%20Meditation%20areas%20with%20floating%20comfort%20crystals.%20Ethereal%20blue%20energy%20flows%20connecting%20minds&width=400&height=300&seq=dept5&orientation=landscape\",description:\"Harness psychic energies to maintain harmony among our magical workforce, managing personnel records with telepathic precision and empathic understanding.\"},{title:\"Network Weavers\",icon:\"fa-diagram-project\",route:\"/NetworkOperations\",image:\"https://readdy.ai/api/search-image?query=Technical%20wizards%20working%20with%20complex%20floating%20network%20diagrams%20made%20of%20blue%20light.%20Magical%20server%20rooms%20with%20glowing%20fiber%20connections.%20Engineers%20casting%20spells%20to%20optimize%20data%20flow.%20Enchanted%20tools%20analyzing%20network%20performance%20with%20magical%20visualizations&width=400&height=300&seq=dept6&orientation=landscape\",description:\"Craft the invisible pathways of data, ensuring seamless communication across the vast distances of our kingdom.\"}].map((dept,index)=>/*#__PURE__*/_jsxs(\"div\",{className:\"bg-white rounded-lg overflow-hidden shadow-md hover:shadow-xl transition-all duration-300 group cursor-pointer\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"relative h-48 overflow-hidden\",children:[/*#__PURE__*/_jsx(\"img\",{src:dept.image,alt:dept.title,className:\"w-full h-full object-cover transition-transform duration-500 group-hover:scale-110\"}),/*#__PURE__*/_jsx(\"div\",{className:\"absolute inset-0 bg-gradient-to-t from-[#201f1f] to-transparent opacity-70 transition-opacity duration-300 group-hover:opacity-50\"}),/*#__PURE__*/_jsx(\"div\",{className:\"absolute bottom-4 left-4 text-white\",children:/*#__PURE__*/_jsx(\"h3\",{className:\"text-xl font-serif font-bold\",children:dept.title})}),/*#__PURE__*/_jsx(\"div\",{className:\"absolute top-4 right-4 w-10 h-10 rounded-full bg-[#15a7dd]/90 flex items-center justify-center shadow-lg backdrop-blur-sm ring-2 ring-white/30\",children:/*#__PURE__*/_jsx(\"i\",{className:\"fas \".concat(dept.icon,\" text-white text-lg drop-shadow-[0_2px_4px_rgba(255,255,255,0.3)]\")})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"p-6\",children:[/*#__PURE__*/_jsx(\"p\",{className:\"text-[#475467] mb-4\",children:dept.description}),/*#__PURE__*/_jsxs(Link,{to:dept.route,className:\"text-[#15a7dd] font-medium flex items-center rounded-button whitespace-nowrap cursor-pointer\",children:[/*#__PURE__*/_jsx(\"span\",{children:\"Explore Department\"}),/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-arrow-right ml-2 transition-transform duration-300 group-hover:translate-x-2\"})]})]})]},index))}),/*#__PURE__*/_jsx(\"div\",{className:\"mt-16 text-center\",children:/*#__PURE__*/_jsxs(Link,{to:\"/departments\",className:\"px-8 py-3 bg-[#15a7dd] text-white rounded-full hover:bg-[#1397c7] transition-all duration-300 rounded-button whitespace-nowrap cursor-pointer inline-flex items-center\",children:[/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-book mr-2\"}),\"View All Departments\"]})})]})})]});};export default MainPage;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Link", "jsx", "_jsx", "jsxs", "_jsxs", "MainPage", "_ref", "user", "moodleData", "activeTab", "setActiveTab", "createParticles", "particleContainer", "document", "getElementById", "i", "particle", "createElement", "className", "style", "left", "concat", "Math", "random", "top", "duration", "animation", "animationDelay", "append<PERSON><PERSON><PERSON>", "scrollToSection", "sectionId", "section", "scrollIntoView", "behavior", "children", "id", "src", "alt", "Array", "from", "length", "map", "_", "background", "transform", "to", "onClick", "heroImage", "querySelector", "animationPlayState", "title", "icon", "route", "image", "description", "dept", "index"], "sources": ["/Users/<USER>/Documents/FatbeamU/react-app/src/pages/MainPage.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Link } from 'react-router-dom';\n\nconst MainPage = ({ user, moodleData }) => {\n  const [activeTab, setActiveTab] = useState('home');\n\n  useEffect(() => {\n    // Particle animation\n    const createParticles = () => {\n      const particleContainer = document.getElementById('particle-container');\n      if (!particleContainer) return;\n\n      for (let i = 0; i < 30; i++) {\n        const particle = document.createElement('div');\n        particle.className = 'absolute w-1 h-1 rounded-full bg-blue-500 opacity-0';\n        // Random position\n        particle.style.left = `${Math.random() * 100}%`;\n        particle.style.top = `${Math.random() * 100}%`;\n        // Random animation duration\n        const duration = 3 + Math.random() * 5;\n        particle.style.animation = `float ${duration}s ease-in-out infinite`;\n        particle.style.animationDelay = `${Math.random() * 5}s`;\n        particleContainer.appendChild(particle);\n      }\n    };\n\n    createParticles();\n  }, []);\n\n  const scrollToSection = (sectionId) => {\n    const section = document.getElementById(sectionId);\n    if (section) {\n      section.scrollIntoView({ behavior: 'smooth' });\n      setActiveTab(sectionId);\n    }\n  };\n\n  return (\n    <div className=\"min-h-screen bg-white text-gray-800 font-sans\">\n      {/* Particle container for magical effects */}\n      <div id=\"particle-container\" className=\"fixed inset-0 pointer-events-none z-0\"></div>\n\n      {/* Hero Section */}\n      <section id=\"home\" className=\"relative min-h-screen flex items-center pt-20\">\n        <div className=\"absolute inset-0 z-0 overflow-hidden\">\n          <img\n            src=\"https://readdy.ai/api/search-image?query=A%20majestic%20magical%20castle%20with%20tall%20spires%20and%20towers%20against%20a%20dramatic%20sunset%20sky.%20The%20central%20tower%20features%20glowing%20blue%20magical%20energy%20flowing%20through%20crystalline%20formations.%20Multiple%20magical%20circular%20platforms%20with%20blue%20energy%20rings%20hover%20around%20the%20castle.%20Stone%20pathways%20lead%20to%20the%20grand%20entrance%2C%20while%20mystical%20blue%20banners%20flutter%20in%20the%20wind.%20Mountains%20and%20waterfalls%20frame%20the%20scene%20with%20ethereal%20lighting&width=1920&height=1080&seq=hero2&orientation=landscape\"\n            alt=\"Magical Castle\"\n            className=\"w-full h-full object-cover object-center animate-gentle-zoom\"\n          />\n          <div className=\"absolute inset-0 bg-gradient-to-r from-[#201f1f]/60 via-transparent to-transparent\"></div>\n          <div className=\"absolute inset-0\">\n            <div className=\"absolute bottom-0 left-0 right-0 h-32 bg-gradient-to-t from-[#201f1f]/40 to-transparent\"></div>\n            {Array.from({ length: 5 }).map((_, i) => (\n              <div\n                key={i}\n                className=\"absolute w-32 h-32 rounded-full\"\n                style={{\n                  left: `${Math.random() * 100}%`,\n                  top: `${Math.random() * 100}%`,\n                  background: 'radial-gradient(circle, rgba(21, 167, 221, 0.2) 0%, rgba(21, 167, 221, 0) 70%)',\n                  animation: `pulse ${2 + Math.random() * 2}s infinite`,\n                  transform: 'translate(-50%, -50%)'\n                }}\n              />\n            ))}\n          </div>\n        </div>\n\n        <div className=\"container mx-auto px-6 flex flex-col md:flex-row items-center relative z-10\">\n          <div className=\"w-full md:w-1/2 text-white mb-10 md:mb-0\">\n            <h1 className=\"text-4xl md:text-5xl lg:text-6xl font-serif font-bold mb-6\">\n              <span className=\"text-[#15a7dd]\">Fiber Optic</span> Technomancy\n            </h1>\n            <p className=\"text-lg md:text-xl mb-8 text-gray-100\">\n              Welcome to Fatbeam Fiber University, where the ancient art of magical networking meets modern fiber technology. Begin your journey into the mystical realm of high-speed connectivity.\n            </p>\n            <div className=\"flex flex-col sm:flex-row space-y-4 sm:space-y-0 sm:space-x-4\">\n              <Link\n                to=\"/library\"\n                className=\"px-8 py-3 bg-[#15a7dd] text-white rounded-full hover:bg-[#1397c7] transition-all duration-300 flex items-center justify-center rounded-button whitespace-nowrap cursor-pointer\"\n              >\n                <i className=\"fas fa-book-open mr-2\"></i>\n                Arcane Library\n              </Link>\n              <Link\n                to=\"/classroom\"\n                className=\"px-8 py-3 bg-transparent border-2 border-[#15a7dd] text-white rounded-full hover:bg-[#15a7dd]/20 transition-all duration-300 flex items-center justify-center rounded-button whitespace-nowrap cursor-pointer\"\n              >\n                <i className=\"fas fa-hat-wizard mr-2\"></i>\n                Training Hall\n              </Link>\n            </div>\n          </div>\n\n          <div className=\"absolute top-4 right-4 z-20\">\n            <button\n              onClick={() => {\n                const heroImage = document.querySelector('.animate-gentle-zoom');\n                if (heroImage) {\n                  heroImage.style.animationPlayState = heroImage.style.animationPlayState === 'paused' ? 'running' : 'paused';\n                }\n              }}\n              className=\"w-12 h-12 rounded-full bg-[#15a7dd]/20 flex items-center justify-center cursor-pointer hover:bg-[#15a7dd]/30 transition-colors duration-300 rounded-button whitespace-nowrap animate-continuous-fade\"\n            >\n              <div className=\"w-8 h-8 rounded-full bg-[#15a7dd]/30 flex items-center justify-center\">\n                <i className=\"fas fa-play text-white text-sm\"></i>\n              </div>\n            </button>\n          </div>\n\n          <div className=\"absolute -bottom-5 -right-5 bg-[#6a3293] text-white p-4 rounded-lg shadow-lg transform rotate-3\">\n            <p className=\"text-sm font-medium\">\n              <i className=\"fas fa-star mr-1\"></i>\n              New Courses Added Weekly\n            </p>\n          </div>\n        </div>\n\n        <div className=\"absolute bottom-10 left-1/2 transform -translate-x-1/2 text-white animate-bounce\">\n          <button onClick={() => scrollToSection('about')} className=\"flex flex-col items-center rounded-button whitespace-nowrap cursor-pointer\">\n            <span className=\"text-sm mb-2\">Scroll to Discover</span>\n            <i className=\"fas fa-chevron-down\"></i>\n          </button>\n        </div>\n      </section>\n\n      {/* About Section */}\n      <section id=\"about\" className=\"py-20 bg-[#f2f2f3]\">\n        <div className=\"container mx-auto px-6\">\n          <div className=\"text-center mb-16\">\n            <h2 className=\"text-3xl md:text-4xl font-serif font-bold mb-4\">\n              <span className=\"text-[#15a7dd]\">About</span> Our Mystical Academy\n            </h2>\n            <div className=\"w-20 h-1 bg-[#15a7dd] mx-auto\"></div>\n          </div>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-12 items-center\">\n            <div>\n              <h3 className=\"text-2xl font-serif font-bold mb-6 text-[#475467]\">Where Technology Meets Magic</h3>\n              <p className=\"text-[#475467] mb-6\">\n                Fatbeam Fiber University was founded in the ancient year of 2010 by a council of network sorcerers who sought to blend the arcane arts with cutting-edge fiber technology. Our institution stands as a beacon of knowledge in the realm of digital enchantment.\n              </p>\n              <p className=\"text-[#475467] mb-6\">\n                Our mystical instructors harness the power of light itself, channeling it through glass threads to connect the farthest reaches of our kingdom. Students learn to weave spells of connectivity, cast protective wards against cyber threats, and summon data from the ethereal cloud.\n              </p>\n\n              <div className=\"flex items-center space-x-4\">\n                <div className=\"w-12 h-12 rounded-full bg-[#15a7dd] flex items-center justify-center\">\n                  <i className=\"fas fa-graduation-cap text-white\"></i>\n                </div>\n                <div>\n                  <h4 className=\"font-bold text-[#475467]\">500+ Graduates</h4>\n                  <p className=\"text-sm text-[#475467]\">Master Technomancers in the field</p>\n                </div>\n              </div>\n            </div>\n\n            <div className=\"relative\">\n              <img\n                src=\"https://readdy.ai/api/search-image?query=A%20mystical%20library%20with%20floating%20holographic%20screens%20displaying%20network%20diagrams.%20Ancient%20tomes%20with%20glowing%20blue%20runes%20sit%20on%20shelves%20alongside%20modern%20server%20equipment.%20Blue%20magical%20energy%20flows%20between%20books%20and%20digital%20displays.%20Fiber%20optic%20cables%20emit%20blue%20light%20across%20the%20room&width=600&height=600&seq=about1&orientation=squarish\"\n                alt=\"Mystical Academy\"\n                className=\"w-full h-auto rounded-lg shadow-xl\"\n              />\n              <div className=\"absolute -bottom-6 -left-6 bg-white p-4 rounded-lg shadow-lg\">\n                <div className=\"flex items-center space-x-2\">\n                  <div className=\"w-10 h-10 rounded-full bg-[#6a3293] flex items-center justify-center\">\n                    <i className=\"fas fa-magic text-white\"></i>\n                  </div>\n                  <div>\n                    <h4 className=\"font-bold text-[#475467]\">15 Years</h4>\n                    <p className=\"text-xs text-[#475467]\">Of Magical Innovation</p>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Departments Section */}\n      <section id=\"departments\" className=\"py-20 bg-white\">\n        <div className=\"container mx-auto px-6\">\n          <div className=\"text-center mb-16\">\n            <h2 className=\"text-3xl md:text-4xl font-serif font-bold mb-4\">\n              <span className=\"text-[#15a7dd]\">Mystical</span> Departments\n            </h2>\n            <p className=\"text-[#475467] max-w-2xl mx-auto\">\n              Explore our specialized schools of technomancy, each focusing on a different aspect of the magical fiber arts.\n            </p>\n            <div className=\"w-20 h-1 bg-[#15a7dd] mx-auto mt-4\"></div>\n          </div>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\">\n            {[\n              {\n                title: \"Finance Alchemy\",\n                icon: \"fa-coins\",\n                route: \"/FinanceDept\",\n                image: \"https://readdy.ai/api/search-image?query=A%20mystical%20treasury%20room%20with%20floating%20gold%20coins%20and%20glowing%20blue%20financial%20charts.%20Ancient%20ledgers%20with%20magical%20runes%20sit%20beside%20modern%20computers.%20Blue%20magical%20energy%20connects%20ledgers%20to%20digital%20displays.%20Wizards%20in%20business%20attire%20cast%20financial%20spells&width=400&height=300&seq=dept1&orientation=landscape\",\n                description: \"Transform raw resources into financial prosperity through the ancient arts of budget management and revenue enchantment.\"\n              },\n              {\n                title: \"Tower Levitation\",\n                icon: \"fa-tower-broadcast\",\n                route: \"/TowerTechnicians\",\n                image: \"https://readdy.ai/api/search-image?query=Magical%20technicians%20working%20on%20a%20tall%20communications%20tower%20with%20glowing%20blue%20fiber%20optic%20connections.%20Wizards%20in%20safety%20gear%20casting%20maintenance%20spells.%20Blue%20energy%20flows%20through%20the%20tower%20structure.%20Technical%20diagrams%20float%20as%20magical%20holograms%20around%20the%20workers&width=400&height=300&seq=dept2&orientation=landscape\",\n                description: \"Master the heights of our magical infrastructure, ensuring the uninterrupted flow of arcane data through our network.\"\n              },\n              {\n                title: \"Sales Sorcery\",\n                icon: \"fa-handshake\",\n                route: \"/SalesDept\",\n                image: \"https://readdy.ai/api/search-image?query=Wizards%20in%20business%20attire%20demonstrating%20magical%20fiber%20optic%20products%20to%20clients.%20Glowing%20blue%20holograms%20show%20network%20capabilities.%20Magical%20contracts%20with%20glowing%20signatures.%20Enchanted%20presentation%20room%20with%20fiber%20optic%20decorations%20and%20blue%20energy%20flowing%20through%20displays&width=400&height=300&seq=dept3&orientation=landscape\",\n                description: \"Learn the mystical arts of persuasion and relationship building to spread our magical connectivity throughout the realm.\"\n              },\n              {\n                title: \"Construction Earth Magic\",\n                icon: \"fa-shovel\",\n                route: \"/ConstructionDept\",\n                image: \"https://readdy.ai/api/search-image?query=Wizards%20using%20magical%20tools%20to%20install%20underground%20fiber%20optic%20cables.%20Earth%20elementals%20helping%20to%20move%20soil.%20Blue%20glowing%20trenches%20with%20fiber%20lines%20being%20laid.%20Magical%20mapping%20tools%20projecting%20underground%20pathways.%20Construction%20site%20with%20both%20magical%20and%20modern%20equipment&width=400&height=300&seq=dept4&orientation=landscape\",\n                description: \"Harness the elements to lay the physical foundations of our network, connecting the material world to the digital realm.\"\n              },\n              {\n                title: \"Human Relations\",\n                icon: \"fa-brain\",\n                route: \"/HumanRelationsDept\",\n                image: \"https://readdy.ai/api/search-image?query=A%20serene%20office%20space%20with%20floating%20crystal%20orbs%20showing%20employee%20auras.%20Psychic%20HR%20wizards%20conducting%20telepathic%20interviews.%20Glowing%20blue%20filing%20cabinets%20with%20magical%20employee%20records.%20Meditation%20areas%20with%20floating%20comfort%20crystals.%20Ethereal%20blue%20energy%20flows%20connecting%20minds&width=400&height=300&seq=dept5&orientation=landscape\",\n                description: \"Harness psychic energies to maintain harmony among our magical workforce, managing personnel records with telepathic precision and empathic understanding.\"\n              },\n              {\n                title: \"Network Weavers\",\n                icon: \"fa-diagram-project\",\n                route: \"/NetworkOperations\",\n                image: \"https://readdy.ai/api/search-image?query=Technical%20wizards%20working%20with%20complex%20floating%20network%20diagrams%20made%20of%20blue%20light.%20Magical%20server%20rooms%20with%20glowing%20fiber%20connections.%20Engineers%20casting%20spells%20to%20optimize%20data%20flow.%20Enchanted%20tools%20analyzing%20network%20performance%20with%20magical%20visualizations&width=400&height=300&seq=dept6&orientation=landscape\",\n                description: \"Craft the invisible pathways of data, ensuring seamless communication across the vast distances of our kingdom.\"\n              }\n            ].map((dept, index) => (\n              <div key={index} className=\"bg-white rounded-lg overflow-hidden shadow-md hover:shadow-xl transition-all duration-300 group cursor-pointer\">\n                <div className=\"relative h-48 overflow-hidden\">\n                  <img\n                    src={dept.image}\n                    alt={dept.title}\n                    className=\"w-full h-full object-cover transition-transform duration-500 group-hover:scale-110\"\n                  />\n                  <div className=\"absolute inset-0 bg-gradient-to-t from-[#201f1f] to-transparent opacity-70 transition-opacity duration-300 group-hover:opacity-50\"></div>\n                  <div className=\"absolute bottom-4 left-4 text-white\">\n                    <h3 className=\"text-xl font-serif font-bold\">{dept.title}</h3>\n                  </div>\n                  <div className=\"absolute top-4 right-4 w-10 h-10 rounded-full bg-[#15a7dd]/90 flex items-center justify-center shadow-lg backdrop-blur-sm ring-2 ring-white/30\">\n                    <i className={`fas ${dept.icon} text-white text-lg drop-shadow-[0_2px_4px_rgba(255,255,255,0.3)]`}></i>\n                  </div>\n                </div>\n                <div className=\"p-6\">\n                  <p className=\"text-[#475467] mb-4\">{dept.description}</p>\n                  <Link\n                    to={dept.route}\n                    className=\"text-[#15a7dd] font-medium flex items-center rounded-button whitespace-nowrap cursor-pointer\"\n                  >\n                    <span>Explore Department</span>\n                    <i className=\"fas fa-arrow-right ml-2 transition-transform duration-300 group-hover:translate-x-2\"></i>\n                  </Link>\n                </div>\n              </div>\n            ))}\n          </div>\n\n          <div className=\"mt-16 text-center\">\n            <Link to=\"/departments\" className=\"px-8 py-3 bg-[#15a7dd] text-white rounded-full hover:bg-[#1397c7] transition-all duration-300 rounded-button whitespace-nowrap cursor-pointer inline-flex items-center\">\n              <i className=\"fas fa-book mr-2\"></i>\n              View All Departments\n            </Link>\n          </div>\n        </div>\n      </section>\n    </div>\n  );\n};\n\nexport default MainPage;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OAASC,IAAI,KAAQ,kBAAkB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAExC,KAAM,CAAAC,QAAQ,CAAGC,IAAA,EAA0B,IAAzB,CAAEC,IAAI,CAAEC,UAAW,CAAC,CAAAF,IAAA,CACpC,KAAM,CAACG,SAAS,CAAEC,YAAY,CAAC,CAAGZ,QAAQ,CAAC,MAAM,CAAC,CAElDC,SAAS,CAAC,IAAM,CACd;AACA,KAAM,CAAAY,eAAe,CAAGA,CAAA,GAAM,CAC5B,KAAM,CAAAC,iBAAiB,CAAGC,QAAQ,CAACC,cAAc,CAAC,oBAAoB,CAAC,CACvE,GAAI,CAACF,iBAAiB,CAAE,OAExB,IAAK,GAAI,CAAAG,CAAC,CAAG,CAAC,CAAEA,CAAC,CAAG,EAAE,CAAEA,CAAC,EAAE,CAAE,CAC3B,KAAM,CAAAC,QAAQ,CAAGH,QAAQ,CAACI,aAAa,CAAC,KAAK,CAAC,CAC9CD,QAAQ,CAACE,SAAS,CAAG,qDAAqD,CAC1E;AACAF,QAAQ,CAACG,KAAK,CAACC,IAAI,IAAAC,MAAA,CAAMC,IAAI,CAACC,MAAM,CAAC,CAAC,CAAG,GAAG,KAAG,CAC/CP,QAAQ,CAACG,KAAK,CAACK,GAAG,IAAAH,MAAA,CAAMC,IAAI,CAACC,MAAM,CAAC,CAAC,CAAG,GAAG,KAAG,CAC9C;AACA,KAAM,CAAAE,QAAQ,CAAG,CAAC,CAAGH,IAAI,CAACC,MAAM,CAAC,CAAC,CAAG,CAAC,CACtCP,QAAQ,CAACG,KAAK,CAACO,SAAS,UAAAL,MAAA,CAAYI,QAAQ,0BAAwB,CACpET,QAAQ,CAACG,KAAK,CAACQ,cAAc,IAAAN,MAAA,CAAMC,IAAI,CAACC,MAAM,CAAC,CAAC,CAAG,CAAC,KAAG,CACvDX,iBAAiB,CAACgB,WAAW,CAACZ,QAAQ,CAAC,CACzC,CACF,CAAC,CAEDL,eAAe,CAAC,CAAC,CACnB,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAkB,eAAe,CAAIC,SAAS,EAAK,CACrC,KAAM,CAAAC,OAAO,CAAGlB,QAAQ,CAACC,cAAc,CAACgB,SAAS,CAAC,CAClD,GAAIC,OAAO,CAAE,CACXA,OAAO,CAACC,cAAc,CAAC,CAAEC,QAAQ,CAAE,QAAS,CAAC,CAAC,CAC9CvB,YAAY,CAACoB,SAAS,CAAC,CACzB,CACF,CAAC,CAED,mBACE1B,KAAA,QAAKc,SAAS,CAAC,+CAA+C,CAAAgB,QAAA,eAE5DhC,IAAA,QAAKiC,EAAE,CAAC,oBAAoB,CAACjB,SAAS,CAAC,uCAAuC,CAAM,CAAC,cAGrFd,KAAA,YAAS+B,EAAE,CAAC,MAAM,CAACjB,SAAS,CAAC,+CAA+C,CAAAgB,QAAA,eAC1E9B,KAAA,QAAKc,SAAS,CAAC,sCAAsC,CAAAgB,QAAA,eACnDhC,IAAA,QACEkC,GAAG,CAAC,ioBAAioB,CACroBC,GAAG,CAAC,gBAAgB,CACpBnB,SAAS,CAAC,8DAA8D,CACzE,CAAC,cACFhB,IAAA,QAAKgB,SAAS,CAAC,oFAAoF,CAAM,CAAC,cAC1Gd,KAAA,QAAKc,SAAS,CAAC,kBAAkB,CAAAgB,QAAA,eAC/BhC,IAAA,QAAKgB,SAAS,CAAC,yFAAyF,CAAM,CAAC,CAC9GoB,KAAK,CAACC,IAAI,CAAC,CAAEC,MAAM,CAAE,CAAE,CAAC,CAAC,CAACC,GAAG,CAAC,CAACC,CAAC,CAAE3B,CAAC,gBAClCb,IAAA,QAEEgB,SAAS,CAAC,iCAAiC,CAC3CC,KAAK,CAAE,CACLC,IAAI,IAAAC,MAAA,CAAKC,IAAI,CAACC,MAAM,CAAC,CAAC,CAAG,GAAG,KAAG,CAC/BC,GAAG,IAAAH,MAAA,CAAKC,IAAI,CAACC,MAAM,CAAC,CAAC,CAAG,GAAG,KAAG,CAC9BoB,UAAU,CAAE,gFAAgF,CAC5FjB,SAAS,UAAAL,MAAA,CAAW,CAAC,CAAGC,IAAI,CAACC,MAAM,CAAC,CAAC,CAAG,CAAC,cAAY,CACrDqB,SAAS,CAAE,uBACb,CAAE,EARG7B,CASN,CACF,CAAC,EACC,CAAC,EACH,CAAC,cAENX,KAAA,QAAKc,SAAS,CAAC,6EAA6E,CAAAgB,QAAA,eAC1F9B,KAAA,QAAKc,SAAS,CAAC,0CAA0C,CAAAgB,QAAA,eACvD9B,KAAA,OAAIc,SAAS,CAAC,4DAA4D,CAAAgB,QAAA,eACxEhC,IAAA,SAAMgB,SAAS,CAAC,gBAAgB,CAAAgB,QAAA,CAAC,aAAW,CAAM,CAAC,eACrD,EAAI,CAAC,cACLhC,IAAA,MAAGgB,SAAS,CAAC,uCAAuC,CAAAgB,QAAA,CAAC,wLAErD,CAAG,CAAC,cACJ9B,KAAA,QAAKc,SAAS,CAAC,+DAA+D,CAAAgB,QAAA,eAC5E9B,KAAA,CAACJ,IAAI,EACH6C,EAAE,CAAC,UAAU,CACb3B,SAAS,CAAC,gLAAgL,CAAAgB,QAAA,eAE1LhC,IAAA,MAAGgB,SAAS,CAAC,uBAAuB,CAAI,CAAC,iBAE3C,EAAM,CAAC,cACPd,KAAA,CAACJ,IAAI,EACH6C,EAAE,CAAC,YAAY,CACf3B,SAAS,CAAC,+MAA+M,CAAAgB,QAAA,eAEzNhC,IAAA,MAAGgB,SAAS,CAAC,wBAAwB,CAAI,CAAC,gBAE5C,EAAM,CAAC,EACJ,CAAC,EACH,CAAC,cAENhB,IAAA,QAAKgB,SAAS,CAAC,6BAA6B,CAAAgB,QAAA,cAC1ChC,IAAA,WACE4C,OAAO,CAAEA,CAAA,GAAM,CACb,KAAM,CAAAC,SAAS,CAAGlC,QAAQ,CAACmC,aAAa,CAAC,sBAAsB,CAAC,CAChE,GAAID,SAAS,CAAE,CACbA,SAAS,CAAC5B,KAAK,CAAC8B,kBAAkB,CAAGF,SAAS,CAAC5B,KAAK,CAAC8B,kBAAkB,GAAK,QAAQ,CAAG,SAAS,CAAG,QAAQ,CAC7G,CACF,CAAE,CACF/B,SAAS,CAAC,sMAAsM,CAAAgB,QAAA,cAEhNhC,IAAA,QAAKgB,SAAS,CAAC,uEAAuE,CAAAgB,QAAA,cACpFhC,IAAA,MAAGgB,SAAS,CAAC,gCAAgC,CAAI,CAAC,CAC/C,CAAC,CACA,CAAC,CACN,CAAC,cAENhB,IAAA,QAAKgB,SAAS,CAAC,iGAAiG,CAAAgB,QAAA,cAC9G9B,KAAA,MAAGc,SAAS,CAAC,qBAAqB,CAAAgB,QAAA,eAChChC,IAAA,MAAGgB,SAAS,CAAC,kBAAkB,CAAI,CAAC,2BAEtC,EAAG,CAAC,CACD,CAAC,EACH,CAAC,cAENhB,IAAA,QAAKgB,SAAS,CAAC,kFAAkF,CAAAgB,QAAA,cAC/F9B,KAAA,WAAQ0C,OAAO,CAAEA,CAAA,GAAMjB,eAAe,CAAC,OAAO,CAAE,CAACX,SAAS,CAAC,4EAA4E,CAAAgB,QAAA,eACrIhC,IAAA,SAAMgB,SAAS,CAAC,cAAc,CAAAgB,QAAA,CAAC,oBAAkB,CAAM,CAAC,cACxDhC,IAAA,MAAGgB,SAAS,CAAC,qBAAqB,CAAI,CAAC,EACjC,CAAC,CACN,CAAC,EACC,CAAC,cAGVhB,IAAA,YAASiC,EAAE,CAAC,OAAO,CAACjB,SAAS,CAAC,oBAAoB,CAAAgB,QAAA,cAChD9B,KAAA,QAAKc,SAAS,CAAC,wBAAwB,CAAAgB,QAAA,eACrC9B,KAAA,QAAKc,SAAS,CAAC,mBAAmB,CAAAgB,QAAA,eAChC9B,KAAA,OAAIc,SAAS,CAAC,gDAAgD,CAAAgB,QAAA,eAC5DhC,IAAA,SAAMgB,SAAS,CAAC,gBAAgB,CAAAgB,QAAA,CAAC,OAAK,CAAM,CAAC,wBAC/C,EAAI,CAAC,cACLhC,IAAA,QAAKgB,SAAS,CAAC,+BAA+B,CAAM,CAAC,EAClD,CAAC,cAENd,KAAA,QAAKc,SAAS,CAAC,qDAAqD,CAAAgB,QAAA,eAClE9B,KAAA,QAAA8B,QAAA,eACEhC,IAAA,OAAIgB,SAAS,CAAC,mDAAmD,CAAAgB,QAAA,CAAC,8BAA4B,CAAI,CAAC,cACnGhC,IAAA,MAAGgB,SAAS,CAAC,qBAAqB,CAAAgB,QAAA,CAAC,iQAEnC,CAAG,CAAC,cACJhC,IAAA,MAAGgB,SAAS,CAAC,qBAAqB,CAAAgB,QAAA,CAAC,uRAEnC,CAAG,CAAC,cAEJ9B,KAAA,QAAKc,SAAS,CAAC,6BAA6B,CAAAgB,QAAA,eAC1ChC,IAAA,QAAKgB,SAAS,CAAC,sEAAsE,CAAAgB,QAAA,cACnFhC,IAAA,MAAGgB,SAAS,CAAC,kCAAkC,CAAI,CAAC,CACjD,CAAC,cACNd,KAAA,QAAA8B,QAAA,eACEhC,IAAA,OAAIgB,SAAS,CAAC,0BAA0B,CAAAgB,QAAA,CAAC,gBAAc,CAAI,CAAC,cAC5DhC,IAAA,MAAGgB,SAAS,CAAC,wBAAwB,CAAAgB,QAAA,CAAC,mCAAiC,CAAG,CAAC,EACxE,CAAC,EACH,CAAC,EACH,CAAC,cAEN9B,KAAA,QAAKc,SAAS,CAAC,UAAU,CAAAgB,QAAA,eACvBhC,IAAA,QACEkC,GAAG,CAAC,0cAA0c,CAC9cC,GAAG,CAAC,kBAAkB,CACtBnB,SAAS,CAAC,oCAAoC,CAC/C,CAAC,cACFhB,IAAA,QAAKgB,SAAS,CAAC,8DAA8D,CAAAgB,QAAA,cAC3E9B,KAAA,QAAKc,SAAS,CAAC,6BAA6B,CAAAgB,QAAA,eAC1ChC,IAAA,QAAKgB,SAAS,CAAC,sEAAsE,CAAAgB,QAAA,cACnFhC,IAAA,MAAGgB,SAAS,CAAC,yBAAyB,CAAI,CAAC,CACxC,CAAC,cACNd,KAAA,QAAA8B,QAAA,eACEhC,IAAA,OAAIgB,SAAS,CAAC,0BAA0B,CAAAgB,QAAA,CAAC,UAAQ,CAAI,CAAC,cACtDhC,IAAA,MAAGgB,SAAS,CAAC,wBAAwB,CAAAgB,QAAA,CAAC,uBAAqB,CAAG,CAAC,EAC5D,CAAC,EACH,CAAC,CACH,CAAC,EACH,CAAC,EACH,CAAC,EACH,CAAC,CACC,CAAC,cAGVhC,IAAA,YAASiC,EAAE,CAAC,aAAa,CAACjB,SAAS,CAAC,gBAAgB,CAAAgB,QAAA,cAClD9B,KAAA,QAAKc,SAAS,CAAC,wBAAwB,CAAAgB,QAAA,eACrC9B,KAAA,QAAKc,SAAS,CAAC,mBAAmB,CAAAgB,QAAA,eAChC9B,KAAA,OAAIc,SAAS,CAAC,gDAAgD,CAAAgB,QAAA,eAC5DhC,IAAA,SAAMgB,SAAS,CAAC,gBAAgB,CAAAgB,QAAA,CAAC,UAAQ,CAAM,CAAC,eAClD,EAAI,CAAC,cACLhC,IAAA,MAAGgB,SAAS,CAAC,kCAAkC,CAAAgB,QAAA,CAAC,gHAEhD,CAAG,CAAC,cACJhC,IAAA,QAAKgB,SAAS,CAAC,oCAAoC,CAAM,CAAC,EACvD,CAAC,cAENhB,IAAA,QAAKgB,SAAS,CAAC,sDAAsD,CAAAgB,QAAA,CAClE,CACC,CACEgB,KAAK,CAAE,iBAAiB,CACxBC,IAAI,CAAE,UAAU,CAChBC,KAAK,CAAE,cAAc,CACrBC,KAAK,CAAE,uaAAua,CAC9aC,WAAW,CAAE,0HACf,CAAC,CACD,CACEJ,KAAK,CAAE,kBAAkB,CACzBC,IAAI,CAAE,oBAAoB,CAC1BC,KAAK,CAAE,mBAAmB,CAC1BC,KAAK,CAAE,gbAAgb,CACvbC,WAAW,CAAE,uHACf,CAAC,CACD,CACEJ,KAAK,CAAE,eAAe,CACtBC,IAAI,CAAE,cAAc,CACpBC,KAAK,CAAE,YAAY,CACnBC,KAAK,CAAE,obAAob,CAC3bC,WAAW,CAAE,0HACf,CAAC,CACD,CACEJ,KAAK,CAAE,0BAA0B,CACjCC,IAAI,CAAE,WAAW,CACjBC,KAAK,CAAE,mBAAmB,CAC1BC,KAAK,CAAE,0bAA0b,CACjcC,WAAW,CAAE,0HACf,CAAC,CACD,CACEJ,KAAK,CAAE,iBAAiB,CACxBC,IAAI,CAAE,UAAU,CAChBC,KAAK,CAAE,qBAAqB,CAC5BC,KAAK,CAAE,8bAA8b,CACrcC,WAAW,CAAE,4JACf,CAAC,CACD,CACEJ,KAAK,CAAE,iBAAiB,CACxBC,IAAI,CAAE,oBAAoB,CAC1BC,KAAK,CAAE,oBAAoB,CAC3BC,KAAK,CAAE,qaAAqa,CAC5aC,WAAW,CAAE,iHACf,CAAC,CACF,CAACb,GAAG,CAAC,CAACc,IAAI,CAAEC,KAAK,gBAChBpD,KAAA,QAAiBc,SAAS,CAAC,gHAAgH,CAAAgB,QAAA,eACzI9B,KAAA,QAAKc,SAAS,CAAC,+BAA+B,CAAAgB,QAAA,eAC5ChC,IAAA,QACEkC,GAAG,CAAEmB,IAAI,CAACF,KAAM,CAChBhB,GAAG,CAAEkB,IAAI,CAACL,KAAM,CAChBhC,SAAS,CAAC,oFAAoF,CAC/F,CAAC,cACFhB,IAAA,QAAKgB,SAAS,CAAC,mIAAmI,CAAM,CAAC,cACzJhB,IAAA,QAAKgB,SAAS,CAAC,qCAAqC,CAAAgB,QAAA,cAClDhC,IAAA,OAAIgB,SAAS,CAAC,8BAA8B,CAAAgB,QAAA,CAAEqB,IAAI,CAACL,KAAK,CAAK,CAAC,CAC3D,CAAC,cACNhD,IAAA,QAAKgB,SAAS,CAAC,gJAAgJ,CAAAgB,QAAA,cAC7JhC,IAAA,MAAGgB,SAAS,QAAAG,MAAA,CAASkC,IAAI,CAACJ,IAAI,qEAAoE,CAAI,CAAC,CACpG,CAAC,EACH,CAAC,cACN/C,KAAA,QAAKc,SAAS,CAAC,KAAK,CAAAgB,QAAA,eAClBhC,IAAA,MAAGgB,SAAS,CAAC,qBAAqB,CAAAgB,QAAA,CAAEqB,IAAI,CAACD,WAAW,CAAI,CAAC,cACzDlD,KAAA,CAACJ,IAAI,EACH6C,EAAE,CAAEU,IAAI,CAACH,KAAM,CACflC,SAAS,CAAC,8FAA8F,CAAAgB,QAAA,eAExGhC,IAAA,SAAAgC,QAAA,CAAM,oBAAkB,CAAM,CAAC,cAC/BhC,IAAA,MAAGgB,SAAS,CAAC,qFAAqF,CAAI,CAAC,EACnG,CAAC,EACJ,CAAC,GAxBEsC,KAyBL,CACN,CAAC,CACC,CAAC,cAENtD,IAAA,QAAKgB,SAAS,CAAC,mBAAmB,CAAAgB,QAAA,cAChC9B,KAAA,CAACJ,IAAI,EAAC6C,EAAE,CAAC,cAAc,CAAC3B,SAAS,CAAC,wKAAwK,CAAAgB,QAAA,eACxMhC,IAAA,MAAGgB,SAAS,CAAC,kBAAkB,CAAI,CAAC,uBAEtC,EAAM,CAAC,CACJ,CAAC,EACH,CAAC,CACC,CAAC,EACP,CAAC,CAEV,CAAC,CAED,cAAe,CAAAb,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}