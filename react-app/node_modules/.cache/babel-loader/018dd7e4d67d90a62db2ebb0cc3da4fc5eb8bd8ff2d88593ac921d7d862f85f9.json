{"ast": null, "code": "import axios from 'axios';\nclass MoodleAPI {\n  constructor() {\n    this.baseURL = process.env.REACT_APP_MOODLE_API_URL;\n    this.token = process.env.REACT_APP_MOODLE_TOKEN;\n    this.moodleURL = process.env.REACT_APP_MOODLE_URL;\n\n    // Create axios instance with default config\n    this.api = axios.create({\n      baseURL: this.baseURL,\n      timeout: 10000,\n      headers: {\n        'Content-Type': 'application/x-www-form-urlencoded'\n      }\n    });\n  }\n\n  // Helper method to make API calls\n  async makeRequest(wsfunction, params = {}) {\n    try {\n      const data = new URLSearchParams({\n        wstoken: this.token,\n        wsfunction: wsfunction,\n        moodlewsrestformat: 'json',\n        ...params\n      });\n      const response = await this.api.post('', data);\n      if (response.data.exception) {\n        throw new Error(response.data.message || 'Moodle API Error');\n      }\n      return response.data;\n    } catch (error) {\n      if (error.response) {\n        throw new Error(`API Error: ${error.response.status} - ${error.response.statusText}`);\n      } else if (error.request) {\n        throw new Error('Network Error: Unable to connect to Moodle');\n      } else {\n        throw new Error(error.message);\n      }\n    }\n  }\n\n  // Get site information\n  async getSiteInfo() {\n    return await this.makeRequest('core_webservice_get_site_info');\n  }\n\n  // Get current user information\n  async getCurrentUser() {\n    const siteInfo = await this.getSiteInfo();\n    return {\n      id: siteInfo.userid,\n      username: siteInfo.username,\n      firstname: siteInfo.firstname,\n      lastname: siteInfo.lastname,\n      email: siteInfo.useremail,\n      lang: siteInfo.lang\n    };\n  }\n\n  // Get user's courses\n  async getCourses(userid = null) {\n    const params = userid ? {\n      userid\n    } : {};\n    return await this.makeRequest('core_enrol_get_users_courses', params);\n  }\n\n  // Get course contents\n  async getCourseContents(courseid) {\n    return await this.makeRequest('core_course_get_contents', {\n      courseid\n    });\n  }\n\n  // Get user's grades for a course\n  async getCourseGrades(courseid, userid = null) {\n    const params = {\n      courseid\n    };\n    if (userid) params.userid = userid;\n    return await this.makeRequest('gradereport_user_get_grade_items', params);\n  }\n\n  // Get user's assignments\n  async getAssignments(courseids = []) {\n    const params = courseids.length > 0 ? {\n      courseids\n    } : {};\n    return await this.makeRequest('mod_assign_get_assignments', params);\n  }\n\n  // Get user's calendar events\n  async getCalendarEvents(options = {}) {\n    const defaultOptions = {\n      events: {\n        eventids: [],\n        courseids: [],\n        groupids: [],\n        categoryids: []\n      },\n      options: {\n        userevents: true,\n        siteevents: true,\n        timestart: Math.floor(Date.now() / 1000),\n        timeend: Math.floor(Date.now() / 1000) + 30 * 24 * 60 * 60 // 30 days\n      }\n    };\n    const mergedOptions = {\n      ...defaultOptions,\n      ...options\n    };\n    return await this.makeRequest('core_calendar_get_calendar_events', mergedOptions);\n  }\n\n  // Get user's notifications\n  async getNotifications(useridto = null, limit = 20) {\n    const params = {\n      limit\n    };\n    if (useridto) params.useridto = useridto;\n    return await this.makeRequest('message_popup_get_popup_notifications', params);\n  }\n\n  // Get course participants\n  async getCourseParticipants(courseid) {\n    return await this.makeRequest('core_enrol_get_enrolled_users', {\n      courseid\n    });\n  }\n\n  // Get user's recent activity\n  async getRecentActivity(courseid, since = null) {\n    const params = {\n      courseid\n    };\n    if (since) params.since = since;\n    return await this.makeRequest('core_course_get_recent_courses', params);\n  }\n\n  // Search courses\n  async searchCourses(criterianame, criteriavalue, page = 0, perpage = 20) {\n    return await this.makeRequest('core_course_search_courses', {\n      criterianame,\n      criteriavalue,\n      page,\n      perpage\n    });\n  }\n\n  // Get user preferences\n  async getUserPreferences(name = null) {\n    const params = name ? {\n      name\n    } : {};\n    return await this.makeRequest('core_user_get_user_preferences', params);\n  }\n\n  // Set user preferences\n  async setUserPreferences(preferences) {\n    return await this.makeRequest('core_user_set_user_preferences', {\n      preferences\n    });\n  }\n\n  // Get course categories\n  async getCourseCategories(criteria = []) {\n    return await this.makeRequest('core_course_get_categories', {\n      criteria\n    });\n  }\n\n  // Get user's dashboard blocks\n  async getDashboardBlocks() {\n    return await this.makeRequest('core_block_get_dashboard_blocks');\n  }\n\n  // Get user's completion status for courses\n  async getCourseCompletionStatus(courseid, userid = null) {\n    const params = {\n      courseid\n    };\n    if (userid) params.userid = userid;\n    return await this.makeRequest('core_completion_get_course_completion_status', params);\n  }\n\n  // Helper method to construct Moodle URLs\n  getMoodleURL(path = '') {\n    return `${this.moodleURL}${path}`;\n  }\n\n  // Helper method to get course URL\n  getCourseURL(courseid) {\n    return this.getMoodleURL(`/course/view.php?id=${courseid}`);\n  }\n\n  // Helper method to get user profile URL\n  getUserProfileURL(userid) {\n    return this.getMoodleURL(`/user/profile.php?id=${userid}`);\n  }\n\n  // Helper method to check if API is configured\n  isConfigured() {\n    return !!(this.baseURL && this.token && this.moodleURL);\n  }\n\n  // Helper method to test connection\n  async testConnection() {\n    try {\n      await this.getSiteInfo();\n      return {\n        success: true,\n        message: 'Connection successful'\n      };\n    } catch (error) {\n      return {\n        success: false,\n        message: error.message\n      };\n    }\n  }\n\n  // Get user's progress data for dashboard\n  async getUserProgress(userid = null) {\n    try {\n      const [courses, siteInfo] = await Promise.all([this.getCourses(userid), this.getSiteInfo()]);\n      const progressData = {\n        totalCourses: courses.length,\n        completedCourses: 0,\n        inProgressCourses: 0,\n        overallProgress: 0,\n        courses: []\n      };\n\n      // Calculate progress for each course\n      for (const course of courses) {\n        try {\n          const completion = await this.getCourseCompletionStatus(course.id, userid);\n          const progress = completion.completionpercentage || 0;\n          progressData.courses.push({\n            ...course,\n            progress,\n            isCompleted: progress === 100\n          });\n          if (progress === 100) {\n            progressData.completedCourses++;\n          } else if (progress > 0) {\n            progressData.inProgressCourses++;\n          }\n        } catch (error) {\n          // If completion data is not available, add course with 0 progress\n          progressData.courses.push({\n            ...course,\n            progress: 0,\n            isCompleted: false\n          });\n        }\n      }\n\n      // Calculate overall progress\n      if (progressData.courses.length > 0) {\n        const totalProgress = progressData.courses.reduce((sum, course) => sum + course.progress, 0);\n        progressData.overallProgress = Math.round(totalProgress / progressData.courses.length);\n      }\n      return progressData;\n    } catch (error) {\n      throw new Error(`Failed to get user progress: ${error.message}`);\n    }\n  }\n}\n\n// Create and export a singleton instance\nconst moodleAPI = new MoodleAPI();\nexport default moodleAPI;", "map": {"version": 3, "names": ["axios", "MoodleAPI", "constructor", "baseURL", "process", "env", "REACT_APP_MOODLE_API_URL", "token", "REACT_APP_MOODLE_TOKEN", "moodleURL", "REACT_APP_MOODLE_URL", "api", "create", "timeout", "headers", "makeRequest", "wsfunction", "params", "data", "URLSearchParams", "wstoken", "moodlewsrestformat", "response", "post", "exception", "Error", "message", "error", "status", "statusText", "request", "getSiteInfo", "getCurrentUser", "siteInfo", "id", "userid", "username", "firstname", "lastname", "email", "useremail", "lang", "getCourses", "getCourseContents", "courseid", "getCourseGrades", "getAssignments", "courseids", "length", "getCalendarEvents", "options", "defaultOptions", "events", "eventids", "groupids", "categoryids", "userevents", "siteevents", "timestart", "Math", "floor", "Date", "now", "timeend", "mergedOptions", "getNotifications", "<PERSON><PERSON><PERSON>", "limit", "getCourseParticipants", "getRecentActivity", "since", "searchCourses", "criterianame", "criteriavalue", "page", "perpage", "getUserPreferences", "name", "setUserPreferences", "preferences", "getCourseCategories", "criteria", "getDashboardBlocks", "getCourseCompletionStatus", "getMoodleURL", "path", "getCourseURL", "getUserProfileURL", "isConfigured", "testConnection", "success", "getUserProgress", "courses", "Promise", "all", "progressData", "totalCourses", "completedCourses", "inProgressCourses", "overallProgress", "course", "completion", "progress", "completionpercentage", "push", "isCompleted", "totalProgress", "reduce", "sum", "round", "moodleAPI"], "sources": ["/Users/<USER>/Documents/FatbeamU/react-app/src/services/moodleAPI.js"], "sourcesContent": ["import axios from 'axios';\n\nclass MoodleAPI {\n  constructor() {\n    this.baseURL = process.env.REACT_APP_MOODLE_API_URL;\n    this.token = process.env.REACT_APP_MOODLE_TOKEN;\n    this.moodleURL = process.env.REACT_APP_MOODLE_URL;\n    \n    // Create axios instance with default config\n    this.api = axios.create({\n      baseURL: this.baseURL,\n      timeout: 10000,\n      headers: {\n        'Content-Type': 'application/x-www-form-urlencoded',\n      },\n    });\n  }\n\n  // Helper method to make API calls\n  async makeRequest(wsfunction, params = {}) {\n    try {\n      const data = new URLSearchParams({\n        wstoken: this.token,\n        wsfunction: wsfunction,\n        moodlewsrestformat: 'json',\n        ...params\n      });\n\n      const response = await this.api.post('', data);\n      \n      if (response.data.exception) {\n        throw new Error(response.data.message || 'Moodle API Error');\n      }\n      \n      return response.data;\n    } catch (error) {\n      if (error.response) {\n        throw new Error(`API Error: ${error.response.status} - ${error.response.statusText}`);\n      } else if (error.request) {\n        throw new Error('Network Error: Unable to connect to Moodle');\n      } else {\n        throw new Error(error.message);\n      }\n    }\n  }\n\n  // Get site information\n  async getSiteInfo() {\n    return await this.makeRequest('core_webservice_get_site_info');\n  }\n\n  // Get current user information\n  async getCurrentUser() {\n    const siteInfo = await this.getSiteInfo();\n    return {\n      id: siteInfo.userid,\n      username: siteInfo.username,\n      firstname: siteInfo.firstname,\n      lastname: siteInfo.lastname,\n      email: siteInfo.useremail,\n      lang: siteInfo.lang\n    };\n  }\n\n  // Get user's courses\n  async getCourses(userid = null) {\n    const params = userid ? { userid } : {};\n    return await this.makeRequest('core_enrol_get_users_courses', params);\n  }\n\n  // Get course contents\n  async getCourseContents(courseid) {\n    return await this.makeRequest('core_course_get_contents', { courseid });\n  }\n\n  // Get user's grades for a course\n  async getCourseGrades(courseid, userid = null) {\n    const params = { courseid };\n    if (userid) params.userid = userid;\n    return await this.makeRequest('gradereport_user_get_grade_items', params);\n  }\n\n  // Get user's assignments\n  async getAssignments(courseids = []) {\n    const params = courseids.length > 0 ? { courseids } : {};\n    return await this.makeRequest('mod_assign_get_assignments', params);\n  }\n\n  // Get user's calendar events\n  async getCalendarEvents(options = {}) {\n    const defaultOptions = {\n      events: {\n        eventids: [],\n        courseids: [],\n        groupids: [],\n        categoryids: []\n      },\n      options: {\n        userevents: true,\n        siteevents: true,\n        timestart: Math.floor(Date.now() / 1000),\n        timeend: Math.floor(Date.now() / 1000) + (30 * 24 * 60 * 60) // 30 days\n      }\n    };\n    \n    const mergedOptions = { ...defaultOptions, ...options };\n    return await this.makeRequest('core_calendar_get_calendar_events', mergedOptions);\n  }\n\n  // Get user's notifications\n  async getNotifications(useridto = null, limit = 20) {\n    const params = { limit };\n    if (useridto) params.useridto = useridto;\n    return await this.makeRequest('message_popup_get_popup_notifications', params);\n  }\n\n  // Get course participants\n  async getCourseParticipants(courseid) {\n    return await this.makeRequest('core_enrol_get_enrolled_users', { courseid });\n  }\n\n  // Get user's recent activity\n  async getRecentActivity(courseid, since = null) {\n    const params = { courseid };\n    if (since) params.since = since;\n    return await this.makeRequest('core_course_get_recent_courses', params);\n  }\n\n  // Search courses\n  async searchCourses(criterianame, criteriavalue, page = 0, perpage = 20) {\n    return await this.makeRequest('core_course_search_courses', {\n      criterianame,\n      criteriavalue,\n      page,\n      perpage\n    });\n  }\n\n  // Get user preferences\n  async getUserPreferences(name = null) {\n    const params = name ? { name } : {};\n    return await this.makeRequest('core_user_get_user_preferences', params);\n  }\n\n  // Set user preferences\n  async setUserPreferences(preferences) {\n    return await this.makeRequest('core_user_set_user_preferences', { preferences });\n  }\n\n  // Get course categories\n  async getCourseCategories(criteria = []) {\n    return await this.makeRequest('core_course_get_categories', { criteria });\n  }\n\n  // Get user's dashboard blocks\n  async getDashboardBlocks() {\n    return await this.makeRequest('core_block_get_dashboard_blocks');\n  }\n\n  // Get user's completion status for courses\n  async getCourseCompletionStatus(courseid, userid = null) {\n    const params = { courseid };\n    if (userid) params.userid = userid;\n    return await this.makeRequest('core_completion_get_course_completion_status', params);\n  }\n\n  // Helper method to construct Moodle URLs\n  getMoodleURL(path = '') {\n    return `${this.moodleURL}${path}`;\n  }\n\n  // Helper method to get course URL\n  getCourseURL(courseid) {\n    return this.getMoodleURL(`/course/view.php?id=${courseid}`);\n  }\n\n  // Helper method to get user profile URL\n  getUserProfileURL(userid) {\n    return this.getMoodleURL(`/user/profile.php?id=${userid}`);\n  }\n\n  // Helper method to check if API is configured\n  isConfigured() {\n    return !!(this.baseURL && this.token && this.moodleURL);\n  }\n\n  // Helper method to test connection\n  async testConnection() {\n    try {\n      await this.getSiteInfo();\n      return { success: true, message: 'Connection successful' };\n    } catch (error) {\n      return { success: false, message: error.message };\n    }\n  }\n\n  // Get user's progress data for dashboard\n  async getUserProgress(userid = null) {\n    try {\n      const [courses, siteInfo] = await Promise.all([\n        this.getCourses(userid),\n        this.getSiteInfo()\n      ]);\n\n      const progressData = {\n        totalCourses: courses.length,\n        completedCourses: 0,\n        inProgressCourses: 0,\n        overallProgress: 0,\n        courses: []\n      };\n\n      // Calculate progress for each course\n      for (const course of courses) {\n        try {\n          const completion = await this.getCourseCompletionStatus(course.id, userid);\n          const progress = completion.completionpercentage || 0;\n          \n          progressData.courses.push({\n            ...course,\n            progress,\n            isCompleted: progress === 100\n          });\n\n          if (progress === 100) {\n            progressData.completedCourses++;\n          } else if (progress > 0) {\n            progressData.inProgressCourses++;\n          }\n        } catch (error) {\n          // If completion data is not available, add course with 0 progress\n          progressData.courses.push({\n            ...course,\n            progress: 0,\n            isCompleted: false\n          });\n        }\n      }\n\n      // Calculate overall progress\n      if (progressData.courses.length > 0) {\n        const totalProgress = progressData.courses.reduce((sum, course) => sum + course.progress, 0);\n        progressData.overallProgress = Math.round(totalProgress / progressData.courses.length);\n      }\n\n      return progressData;\n    } catch (error) {\n      throw new Error(`Failed to get user progress: ${error.message}`);\n    }\n  }\n}\n\n// Create and export a singleton instance\nconst moodleAPI = new MoodleAPI();\nexport default moodleAPI;\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AAEzB,MAAMC,SAAS,CAAC;EACdC,WAAWA,CAAA,EAAG;IACZ,IAAI,CAACC,OAAO,GAAGC,OAAO,CAACC,GAAG,CAACC,wBAAwB;IACnD,IAAI,CAACC,KAAK,GAAGH,OAAO,CAACC,GAAG,CAACG,sBAAsB;IAC/C,IAAI,CAACC,SAAS,GAAGL,OAAO,CAACC,GAAG,CAACK,oBAAoB;;IAEjD;IACA,IAAI,CAACC,GAAG,GAAGX,KAAK,CAACY,MAAM,CAAC;MACtBT,OAAO,EAAE,IAAI,CAACA,OAAO;MACrBU,OAAO,EAAE,KAAK;MACdC,OAAO,EAAE;QACP,cAAc,EAAE;MAClB;IACF,CAAC,CAAC;EACJ;;EAEA;EACA,MAAMC,WAAWA,CAACC,UAAU,EAAEC,MAAM,GAAG,CAAC,CAAC,EAAE;IACzC,IAAI;MACF,MAAMC,IAAI,GAAG,IAAIC,eAAe,CAAC;QAC/BC,OAAO,EAAE,IAAI,CAACb,KAAK;QACnBS,UAAU,EAAEA,UAAU;QACtBK,kBAAkB,EAAE,MAAM;QAC1B,GAAGJ;MACL,CAAC,CAAC;MAEF,MAAMK,QAAQ,GAAG,MAAM,IAAI,CAACX,GAAG,CAACY,IAAI,CAAC,EAAE,EAAEL,IAAI,CAAC;MAE9C,IAAII,QAAQ,CAACJ,IAAI,CAACM,SAAS,EAAE;QAC3B,MAAM,IAAIC,KAAK,CAACH,QAAQ,CAACJ,IAAI,CAACQ,OAAO,IAAI,kBAAkB,CAAC;MAC9D;MAEA,OAAOJ,QAAQ,CAACJ,IAAI;IACtB,CAAC,CAAC,OAAOS,KAAK,EAAE;MACd,IAAIA,KAAK,CAACL,QAAQ,EAAE;QAClB,MAAM,IAAIG,KAAK,CAAC,cAAcE,KAAK,CAACL,QAAQ,CAACM,MAAM,MAAMD,KAAK,CAACL,QAAQ,CAACO,UAAU,EAAE,CAAC;MACvF,CAAC,MAAM,IAAIF,KAAK,CAACG,OAAO,EAAE;QACxB,MAAM,IAAIL,KAAK,CAAC,4CAA4C,CAAC;MAC/D,CAAC,MAAM;QACL,MAAM,IAAIA,KAAK,CAACE,KAAK,CAACD,OAAO,CAAC;MAChC;IACF;EACF;;EAEA;EACA,MAAMK,WAAWA,CAAA,EAAG;IAClB,OAAO,MAAM,IAAI,CAAChB,WAAW,CAAC,+BAA+B,CAAC;EAChE;;EAEA;EACA,MAAMiB,cAAcA,CAAA,EAAG;IACrB,MAAMC,QAAQ,GAAG,MAAM,IAAI,CAACF,WAAW,CAAC,CAAC;IACzC,OAAO;MACLG,EAAE,EAAED,QAAQ,CAACE,MAAM;MACnBC,QAAQ,EAAEH,QAAQ,CAACG,QAAQ;MAC3BC,SAAS,EAAEJ,QAAQ,CAACI,SAAS;MAC7BC,QAAQ,EAAEL,QAAQ,CAACK,QAAQ;MAC3BC,KAAK,EAAEN,QAAQ,CAACO,SAAS;MACzBC,IAAI,EAAER,QAAQ,CAACQ;IACjB,CAAC;EACH;;EAEA;EACA,MAAMC,UAAUA,CAACP,MAAM,GAAG,IAAI,EAAE;IAC9B,MAAMlB,MAAM,GAAGkB,MAAM,GAAG;MAAEA;IAAO,CAAC,GAAG,CAAC,CAAC;IACvC,OAAO,MAAM,IAAI,CAACpB,WAAW,CAAC,8BAA8B,EAAEE,MAAM,CAAC;EACvE;;EAEA;EACA,MAAM0B,iBAAiBA,CAACC,QAAQ,EAAE;IAChC,OAAO,MAAM,IAAI,CAAC7B,WAAW,CAAC,0BAA0B,EAAE;MAAE6B;IAAS,CAAC,CAAC;EACzE;;EAEA;EACA,MAAMC,eAAeA,CAACD,QAAQ,EAAET,MAAM,GAAG,IAAI,EAAE;IAC7C,MAAMlB,MAAM,GAAG;MAAE2B;IAAS,CAAC;IAC3B,IAAIT,MAAM,EAAElB,MAAM,CAACkB,MAAM,GAAGA,MAAM;IAClC,OAAO,MAAM,IAAI,CAACpB,WAAW,CAAC,kCAAkC,EAAEE,MAAM,CAAC;EAC3E;;EAEA;EACA,MAAM6B,cAAcA,CAACC,SAAS,GAAG,EAAE,EAAE;IACnC,MAAM9B,MAAM,GAAG8B,SAAS,CAACC,MAAM,GAAG,CAAC,GAAG;MAAED;IAAU,CAAC,GAAG,CAAC,CAAC;IACxD,OAAO,MAAM,IAAI,CAAChC,WAAW,CAAC,4BAA4B,EAAEE,MAAM,CAAC;EACrE;;EAEA;EACA,MAAMgC,iBAAiBA,CAACC,OAAO,GAAG,CAAC,CAAC,EAAE;IACpC,MAAMC,cAAc,GAAG;MACrBC,MAAM,EAAE;QACNC,QAAQ,EAAE,EAAE;QACZN,SAAS,EAAE,EAAE;QACbO,QAAQ,EAAE,EAAE;QACZC,WAAW,EAAE;MACf,CAAC;MACDL,OAAO,EAAE;QACPM,UAAU,EAAE,IAAI;QAChBC,UAAU,EAAE,IAAI;QAChBC,SAAS,EAAEC,IAAI,CAACC,KAAK,CAACC,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC;QACxCC,OAAO,EAAEJ,IAAI,CAACC,KAAK,CAACC,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,GAAI,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAG,CAAC;MAC/D;IACF,CAAC;IAED,MAAME,aAAa,GAAG;MAAE,GAAGb,cAAc;MAAE,GAAGD;IAAQ,CAAC;IACvD,OAAO,MAAM,IAAI,CAACnC,WAAW,CAAC,mCAAmC,EAAEiD,aAAa,CAAC;EACnF;;EAEA;EACA,MAAMC,gBAAgBA,CAACC,QAAQ,GAAG,IAAI,EAAEC,KAAK,GAAG,EAAE,EAAE;IAClD,MAAMlD,MAAM,GAAG;MAAEkD;IAAM,CAAC;IACxB,IAAID,QAAQ,EAAEjD,MAAM,CAACiD,QAAQ,GAAGA,QAAQ;IACxC,OAAO,MAAM,IAAI,CAACnD,WAAW,CAAC,uCAAuC,EAAEE,MAAM,CAAC;EAChF;;EAEA;EACA,MAAMmD,qBAAqBA,CAACxB,QAAQ,EAAE;IACpC,OAAO,MAAM,IAAI,CAAC7B,WAAW,CAAC,+BAA+B,EAAE;MAAE6B;IAAS,CAAC,CAAC;EAC9E;;EAEA;EACA,MAAMyB,iBAAiBA,CAACzB,QAAQ,EAAE0B,KAAK,GAAG,IAAI,EAAE;IAC9C,MAAMrD,MAAM,GAAG;MAAE2B;IAAS,CAAC;IAC3B,IAAI0B,KAAK,EAAErD,MAAM,CAACqD,KAAK,GAAGA,KAAK;IAC/B,OAAO,MAAM,IAAI,CAACvD,WAAW,CAAC,gCAAgC,EAAEE,MAAM,CAAC;EACzE;;EAEA;EACA,MAAMsD,aAAaA,CAACC,YAAY,EAAEC,aAAa,EAAEC,IAAI,GAAG,CAAC,EAAEC,OAAO,GAAG,EAAE,EAAE;IACvE,OAAO,MAAM,IAAI,CAAC5D,WAAW,CAAC,4BAA4B,EAAE;MAC1DyD,YAAY;MACZC,aAAa;MACbC,IAAI;MACJC;IACF,CAAC,CAAC;EACJ;;EAEA;EACA,MAAMC,kBAAkBA,CAACC,IAAI,GAAG,IAAI,EAAE;IACpC,MAAM5D,MAAM,GAAG4D,IAAI,GAAG;MAAEA;IAAK,CAAC,GAAG,CAAC,CAAC;IACnC,OAAO,MAAM,IAAI,CAAC9D,WAAW,CAAC,gCAAgC,EAAEE,MAAM,CAAC;EACzE;;EAEA;EACA,MAAM6D,kBAAkBA,CAACC,WAAW,EAAE;IACpC,OAAO,MAAM,IAAI,CAAChE,WAAW,CAAC,gCAAgC,EAAE;MAAEgE;IAAY,CAAC,CAAC;EAClF;;EAEA;EACA,MAAMC,mBAAmBA,CAACC,QAAQ,GAAG,EAAE,EAAE;IACvC,OAAO,MAAM,IAAI,CAAClE,WAAW,CAAC,4BAA4B,EAAE;MAAEkE;IAAS,CAAC,CAAC;EAC3E;;EAEA;EACA,MAAMC,kBAAkBA,CAAA,EAAG;IACzB,OAAO,MAAM,IAAI,CAACnE,WAAW,CAAC,iCAAiC,CAAC;EAClE;;EAEA;EACA,MAAMoE,yBAAyBA,CAACvC,QAAQ,EAAET,MAAM,GAAG,IAAI,EAAE;IACvD,MAAMlB,MAAM,GAAG;MAAE2B;IAAS,CAAC;IAC3B,IAAIT,MAAM,EAAElB,MAAM,CAACkB,MAAM,GAAGA,MAAM;IAClC,OAAO,MAAM,IAAI,CAACpB,WAAW,CAAC,8CAA8C,EAAEE,MAAM,CAAC;EACvF;;EAEA;EACAmE,YAAYA,CAACC,IAAI,GAAG,EAAE,EAAE;IACtB,OAAO,GAAG,IAAI,CAAC5E,SAAS,GAAG4E,IAAI,EAAE;EACnC;;EAEA;EACAC,YAAYA,CAAC1C,QAAQ,EAAE;IACrB,OAAO,IAAI,CAACwC,YAAY,CAAC,uBAAuBxC,QAAQ,EAAE,CAAC;EAC7D;;EAEA;EACA2C,iBAAiBA,CAACpD,MAAM,EAAE;IACxB,OAAO,IAAI,CAACiD,YAAY,CAAC,wBAAwBjD,MAAM,EAAE,CAAC;EAC5D;;EAEA;EACAqD,YAAYA,CAAA,EAAG;IACb,OAAO,CAAC,EAAE,IAAI,CAACrF,OAAO,IAAI,IAAI,CAACI,KAAK,IAAI,IAAI,CAACE,SAAS,CAAC;EACzD;;EAEA;EACA,MAAMgF,cAAcA,CAAA,EAAG;IACrB,IAAI;MACF,MAAM,IAAI,CAAC1D,WAAW,CAAC,CAAC;MACxB,OAAO;QAAE2D,OAAO,EAAE,IAAI;QAAEhE,OAAO,EAAE;MAAwB,CAAC;IAC5D,CAAC,CAAC,OAAOC,KAAK,EAAE;MACd,OAAO;QAAE+D,OAAO,EAAE,KAAK;QAAEhE,OAAO,EAAEC,KAAK,CAACD;MAAQ,CAAC;IACnD;EACF;;EAEA;EACA,MAAMiE,eAAeA,CAACxD,MAAM,GAAG,IAAI,EAAE;IACnC,IAAI;MACF,MAAM,CAACyD,OAAO,EAAE3D,QAAQ,CAAC,GAAG,MAAM4D,OAAO,CAACC,GAAG,CAAC,CAC5C,IAAI,CAACpD,UAAU,CAACP,MAAM,CAAC,EACvB,IAAI,CAACJ,WAAW,CAAC,CAAC,CACnB,CAAC;MAEF,MAAMgE,YAAY,GAAG;QACnBC,YAAY,EAAEJ,OAAO,CAAC5C,MAAM;QAC5BiD,gBAAgB,EAAE,CAAC;QACnBC,iBAAiB,EAAE,CAAC;QACpBC,eAAe,EAAE,CAAC;QAClBP,OAAO,EAAE;MACX,CAAC;;MAED;MACA,KAAK,MAAMQ,MAAM,IAAIR,OAAO,EAAE;QAC5B,IAAI;UACF,MAAMS,UAAU,GAAG,MAAM,IAAI,CAAClB,yBAAyB,CAACiB,MAAM,CAAClE,EAAE,EAAEC,MAAM,CAAC;UAC1E,MAAMmE,QAAQ,GAAGD,UAAU,CAACE,oBAAoB,IAAI,CAAC;UAErDR,YAAY,CAACH,OAAO,CAACY,IAAI,CAAC;YACxB,GAAGJ,MAAM;YACTE,QAAQ;YACRG,WAAW,EAAEH,QAAQ,KAAK;UAC5B,CAAC,CAAC;UAEF,IAAIA,QAAQ,KAAK,GAAG,EAAE;YACpBP,YAAY,CAACE,gBAAgB,EAAE;UACjC,CAAC,MAAM,IAAIK,QAAQ,GAAG,CAAC,EAAE;YACvBP,YAAY,CAACG,iBAAiB,EAAE;UAClC;QACF,CAAC,CAAC,OAAOvE,KAAK,EAAE;UACd;UACAoE,YAAY,CAACH,OAAO,CAACY,IAAI,CAAC;YACxB,GAAGJ,MAAM;YACTE,QAAQ,EAAE,CAAC;YACXG,WAAW,EAAE;UACf,CAAC,CAAC;QACJ;MACF;;MAEA;MACA,IAAIV,YAAY,CAACH,OAAO,CAAC5C,MAAM,GAAG,CAAC,EAAE;QACnC,MAAM0D,aAAa,GAAGX,YAAY,CAACH,OAAO,CAACe,MAAM,CAAC,CAACC,GAAG,EAAER,MAAM,KAAKQ,GAAG,GAAGR,MAAM,CAACE,QAAQ,EAAE,CAAC,CAAC;QAC5FP,YAAY,CAACI,eAAe,GAAGxC,IAAI,CAACkD,KAAK,CAACH,aAAa,GAAGX,YAAY,CAACH,OAAO,CAAC5C,MAAM,CAAC;MACxF;MAEA,OAAO+C,YAAY;IACrB,CAAC,CAAC,OAAOpE,KAAK,EAAE;MACd,MAAM,IAAIF,KAAK,CAAC,gCAAgCE,KAAK,CAACD,OAAO,EAAE,CAAC;IAClE;EACF;AACF;;AAEA;AACA,MAAMoF,SAAS,GAAG,IAAI7G,SAAS,CAAC,CAAC;AACjC,eAAe6G,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}