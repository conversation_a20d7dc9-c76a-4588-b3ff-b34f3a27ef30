{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/FatbeamU/react-app/src/components/UnifiedDashboard.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Link } from 'react-router-dom';\nimport MoodleIntegration from './MoodleIntegration';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst UnifiedDashboard = ({\n  user\n}) => {\n  _s();\n  var _moodleData$courses, _moodleData$notificat, _moodleData$recentAct, _moodleData$notificat2, _moodleData$courses2;\n  const [moodleData, setMoodleData] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [activeSection, setActiveSection] = useState('overview');\n  useEffect(() => {\n    // Fetch Moodle data when component mounts\n    const fetchMoodleData = async () => {\n      try {\n        setLoading(true);\n        // This will be implemented with actual Moodle API calls\n        // For now, using mock data\n        const mockData = {\n          courses: [{\n            id: 1,\n            name: 'Fundamentals of Magical Networking',\n            progress: 85\n          }, {\n            id: 2,\n            name: 'Fiber Optic Enchantments',\n            progress: 60\n          }, {\n            id: 3,\n            name: 'Network Security Wards',\n            progress: 30\n          }],\n          recentActivity: [{\n            type: 'course_completion',\n            course: 'Basic Network Spells',\n            timestamp: '2024-01-15'\n          }, {\n            type: 'assignment_submission',\n            course: 'Fiber Optics 101',\n            timestamp: '2024-01-14'\n          }],\n          notifications: [{\n            type: 'assignment_due',\n            message: 'Assignment due in 3 days',\n            course: 'Network Security'\n          }, {\n            type: 'new_course',\n            message: 'New course available: Advanced Routing',\n            course: null\n          }]\n        };\n        setMoodleData(mockData);\n      } catch (error) {\n        console.error('Error fetching Moodle data:', error);\n      } finally {\n        setLoading(false);\n      }\n    };\n    fetchMoodleData();\n  }, []);\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gradient-to-br from-[#0a0500] via-[#1a0f00] to-[#2a1a05] text-white pt-20\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container mx-auto px-6 py-12\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-center h-64\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fas fa-spinner fa-spin text-4xl text-[#15a7dd] mb-4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 49,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-xl\",\n              children: \"Loading your magical dashboard...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 50,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 48,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 47,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 46,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 45,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gradient-to-br from-[#0a0500] via-[#1a0f00] to-[#2a1a05] text-white pt-20\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container mx-auto px-6 py-12\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-12\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-4xl md:text-5xl font-serif font-bold mb-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-[#15a7dd]\",\n            children: \"Unified\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 64,\n            columnNumber: 13\n          }, this), \" Learning Dashboard\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 63,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-xl text-gray-300 max-w-3xl\",\n          children: \"Your central command center for all learning activities across the React frontend and Moodle backend.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 66,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 62,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-wrap border-b border-gray-700 mb-8\",\n        children: ['overview', 'courses', 'progress', 'integration'].map(section => /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setActiveSection(section),\n          className: `px-6 py-3 font-medium text-sm transition-colors duration-300 rounded-button whitespace-nowrap cursor-pointer ${activeSection === section ? 'text-[#15a7dd] border-b-2 border-[#15a7dd]' : 'text-gray-400 hover:text-[#15a7dd]'}`,\n          children: section.charAt(0).toUpperCase() + section.slice(1)\n        }, section, false, {\n          fileName: _jsxFileName,\n          lineNumber: 74,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 72,\n        columnNumber: 9\n      }, this), activeSection === 'overview' && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 lg:grid-cols-3 gap-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"lg:col-span-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-2xl font-serif font-bold mb-6\",\n            children: \"Learning Overview\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 92,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-gradient-to-br from-[#15a7dd]/20 to-[#15a7dd]/10 rounded-lg p-6 border border-[#15a7dd]/30\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-lg font-semibold mb-2\",\n                children: \"Active Courses\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 97,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-3xl font-bold text-[#15a7dd]\",\n                children: (moodleData === null || moodleData === void 0 ? void 0 : (_moodleData$courses = moodleData.courses) === null || _moodleData$courses === void 0 ? void 0 : _moodleData$courses.length) || 0\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 98,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 96,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-gradient-to-br from-[#6a3293]/20 to-[#6a3293]/10 rounded-lg p-6 border border-[#6a3293]/30\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-lg font-semibold mb-2\",\n                children: \"Avg Progress\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 102,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-3xl font-bold text-[#6a3293]\",\n                children: [moodleData !== null && moodleData !== void 0 && moodleData.courses ? Math.round(moodleData.courses.reduce((acc, course) => acc + course.progress, 0) / moodleData.courses.length) : 0, \"%\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 103,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 101,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-gradient-to-br from-yellow-500/20 to-yellow-500/10 rounded-lg p-6 border border-yellow-500/30\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-lg font-semibold mb-2\",\n                children: \"Notifications\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 109,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-3xl font-bold text-yellow-500\",\n                children: (moodleData === null || moodleData === void 0 ? void 0 : (_moodleData$notificat = moodleData.notifications) === null || _moodleData$notificat === void 0 ? void 0 : _moodleData$notificat.length) || 0\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 110,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 108,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 95,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-[#1a0f00] rounded-lg p-6 border border-gray-700\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-xl font-semibold mb-4\",\n              children: \"Recent Activity\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 116,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-4\",\n              children: (moodleData === null || moodleData === void 0 ? void 0 : (_moodleData$recentAct = moodleData.recentActivity) === null || _moodleData$recentAct === void 0 ? void 0 : _moodleData$recentAct.map((activity, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between p-3 bg-[#2a1a05] rounded\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                    className: `fas ${activity.type === 'course_completion' ? 'fa-check-circle text-green-500' : 'fa-upload text-blue-500'} mr-3`\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 121,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"font-medium\",\n                      children: activity.type.replace('_', ' ').toUpperCase()\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 123,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm text-gray-400\",\n                      children: activity.course\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 124,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 122,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 120,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-sm text-gray-400\",\n                  children: activity.timestamp\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 127,\n                  columnNumber: 23\n                }, this)]\n              }, index, true, {\n                fileName: _jsxFileName,\n                lineNumber: 119,\n                columnNumber: 21\n              }, this))) || /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-400 text-center py-4\",\n                children: \"No recent activity\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 130,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 117,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 115,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 91,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-2xl font-serif font-bold mb-6\",\n            children: \"Quick Actions\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 137,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-4\",\n            children: [/*#__PURE__*/_jsxDEV(Link, {\n              to: \"/classroom\",\n              className: \"block w-full p-4 bg-[#15a7dd] rounded-lg hover:bg-[#1397c7] transition-colors duration-300\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"fas fa-chalkboard-teacher mr-3 text-xl\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 141,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                    className: \"font-semibold\",\n                    children: \"My Classroom\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 143,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm opacity-90\",\n                    children: \"Personal learning space\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 144,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 142,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 140,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 139,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n              href: \"/learning\",\n              className: \"block w-full p-4 bg-[#6a3293] rounded-lg hover:bg-[#5a2283] transition-colors duration-300\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"fas fa-graduation-cap mr-3 text-xl\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 151,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                    className: \"font-semibold\",\n                    children: \"Moodle LMS\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 153,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm opacity-90\",\n                    children: \"Access full course system\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 154,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 152,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 150,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 149,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/library\",\n              className: \"block w-full p-4 bg-yellow-600 rounded-lg hover:bg-yellow-700 transition-colors duration-300\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"fas fa-book mr-3 text-xl\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 161,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                    className: \"font-semibold\",\n                    children: \"Library\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 163,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm opacity-90\",\n                    children: \"Browse resources\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 164,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 162,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 160,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 159,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/departments\",\n              className: \"block w-full p-4 bg-green-600 rounded-lg hover:bg-green-700 transition-colors duration-300\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"fas fa-building mr-3 text-xl\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 171,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                    className: \"font-semibold\",\n                    children: \"Departments\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 173,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm opacity-90\",\n                    children: \"Explore specializations\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 174,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 172,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 170,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 169,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 138,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mt-8 bg-[#1a0f00] rounded-lg p-6 border border-gray-700\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-xl font-semibold mb-4\",\n              children: \"Notifications\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 182,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-3\",\n              children: (moodleData === null || moodleData === void 0 ? void 0 : (_moodleData$notificat2 = moodleData.notifications) === null || _moodleData$notificat2 === void 0 ? void 0 : _moodleData$notificat2.map((notification, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"p-3 bg-[#2a1a05] rounded border-l-4 border-yellow-500\",\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm font-medium\",\n                  children: notification.message\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 186,\n                  columnNumber: 23\n                }, this), notification.course && /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-xs text-gray-400 mt-1\",\n                  children: notification.course\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 188,\n                  columnNumber: 25\n                }, this)]\n              }, index, true, {\n                fileName: _jsxFileName,\n                lineNumber: 185,\n                columnNumber: 21\n              }, this))) || /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-400 text-center py-4\",\n                children: \"No notifications\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 192,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 183,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 181,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 136,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 90,\n        columnNumber: 11\n      }, this), activeSection === 'courses' && /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-2xl font-serif font-bold mb-6\",\n          children: \"Course Management\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 203,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n          children: (moodleData === null || moodleData === void 0 ? void 0 : (_moodleData$courses2 = moodleData.courses) === null || _moodleData$courses2 === void 0 ? void 0 : _moodleData$courses2.map(course => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-[#1a0f00] rounded-lg p-6 border border-gray-700\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-xl font-semibold mb-4\",\n              children: course.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 207,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex justify-between text-sm mb-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"Progress\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 210,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: [course.progress, \"%\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 211,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 209,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-full bg-gray-700 rounded-full h-2\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-[#15a7dd] h-2 rounded-full\",\n                  style: {\n                    width: `${course.progress}%`\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 214,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 213,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 208,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n              href: \"/learning\",\n              className: \"block w-full text-center py-2 bg-[#15a7dd] rounded hover:bg-[#1397c7] transition-colors duration-300\",\n              children: \"Open in Moodle\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 217,\n              columnNumber: 19\n            }, this)]\n          }, course.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 206,\n            columnNumber: 17\n          }, this))) || /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-span-full text-center py-12\",\n            children: /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-400\",\n              children: \"No courses found\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 223,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 222,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 204,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 202,\n        columnNumber: 11\n      }, this), activeSection === 'progress' && /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-2xl font-serif font-bold mb-6\",\n          children: \"Learning Progress\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 233,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-[#1a0f00] rounded-lg p-6 border border-gray-700\",\n          children: /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-center text-gray-400 py-12\",\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fas fa-chart-line text-4xl mb-4 block\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 236,\n              columnNumber: 17\n            }, this), \"Detailed progress analytics coming soon!\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 235,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 234,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 232,\n        columnNumber: 11\n      }, this), activeSection === 'integration' && /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-2xl font-serif font-bold mb-6\",\n          children: \"React-Moodle Integration\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 246,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(MoodleIntegration, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 247,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 245,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 60,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 59,\n    columnNumber: 5\n  }, this);\n};\n_s(UnifiedDashboard, \"e2jdsNhBM649iQs4MTB4DfWy/0I=\");\n_c = UnifiedDashboard;\nexport default UnifiedDashboard;\nvar _c;\n$RefreshReg$(_c, \"UnifiedDashboard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Link", "MoodleIntegration", "jsxDEV", "_jsxDEV", "UnifiedDashboard", "user", "_s", "_moodleData$courses", "_moodleData$notificat", "_moodleData$recentAct", "_moodleData$notificat2", "_moodleData$courses2", "moodleData", "setMoodleData", "loading", "setLoading", "activeSection", "setActiveSection", "fetchMoodleData", "mockData", "courses", "id", "name", "progress", "recentActivity", "type", "course", "timestamp", "notifications", "message", "error", "console", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "section", "onClick", "char<PERSON>t", "toUpperCase", "slice", "length", "Math", "round", "reduce", "acc", "activity", "index", "replace", "to", "href", "notification", "style", "width", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/FatbeamU/react-app/src/components/UnifiedDashboard.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Link } from 'react-router-dom';\nimport MoodleIntegration from './MoodleIntegration';\n\nconst UnifiedDashboard = ({ user }) => {\n  const [moodleData, setMoodleData] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [activeSection, setActiveSection] = useState('overview');\n\n  useEffect(() => {\n    // Fetch Moodle data when component mounts\n    const fetchMoodleData = async () => {\n      try {\n        setLoading(true);\n        // This will be implemented with actual Moodle API calls\n        // For now, using mock data\n        const mockData = {\n          courses: [\n            { id: 1, name: 'Fundamentals of Magical Networking', progress: 85 },\n            { id: 2, name: 'Fiber Optic Enchantments', progress: 60 },\n            { id: 3, name: 'Network Security Wards', progress: 30 }\n          ],\n          recentActivity: [\n            { type: 'course_completion', course: 'Basic Network Spells', timestamp: '2024-01-15' },\n            { type: 'assignment_submission', course: 'Fiber Optics 101', timestamp: '2024-01-14' }\n          ],\n          notifications: [\n            { type: 'assignment_due', message: 'Assignment due in 3 days', course: 'Network Security' },\n            { type: 'new_course', message: 'New course available: Advanced Routing', course: null }\n          ]\n        };\n        setMoodleData(mockData);\n      } catch (error) {\n        console.error('Error fetching Moodle data:', error);\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchMoodleData();\n  }, []);\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-[#0a0500] via-[#1a0f00] to-[#2a1a05] text-white pt-20\">\n        <div className=\"container mx-auto px-6 py-12\">\n          <div className=\"flex items-center justify-center h-64\">\n            <div className=\"text-center\">\n              <i className=\"fas fa-spinner fa-spin text-4xl text-[#15a7dd] mb-4\"></i>\n              <p className=\"text-xl\">Loading your magical dashboard...</p>\n            </div>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-[#0a0500] via-[#1a0f00] to-[#2a1a05] text-white pt-20\">\n      <div className=\"container mx-auto px-6 py-12\">\n        {/* Header */}\n        <div className=\"mb-12\">\n          <h1 className=\"text-4xl md:text-5xl font-serif font-bold mb-4\">\n            <span className=\"text-[#15a7dd]\">Unified</span> Learning Dashboard\n          </h1>\n          <p className=\"text-xl text-gray-300 max-w-3xl\">\n            Your central command center for all learning activities across the React frontend and Moodle backend.\n          </p>\n        </div>\n\n        {/* Navigation Tabs */}\n        <div className=\"flex flex-wrap border-b border-gray-700 mb-8\">\n          {['overview', 'courses', 'progress', 'integration'].map((section) => (\n            <button\n              key={section}\n              onClick={() => setActiveSection(section)}\n              className={`px-6 py-3 font-medium text-sm transition-colors duration-300 rounded-button whitespace-nowrap cursor-pointer ${\n                activeSection === section\n                  ? 'text-[#15a7dd] border-b-2 border-[#15a7dd]'\n                  : 'text-gray-400 hover:text-[#15a7dd]'\n              }`}\n            >\n              {section.charAt(0).toUpperCase() + section.slice(1)}\n            </button>\n          ))}\n        </div>\n\n        {/* Overview Section */}\n        {activeSection === 'overview' && (\n          <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-8\">\n            <div className=\"lg:col-span-2\">\n              <h2 className=\"text-2xl font-serif font-bold mb-6\">Learning Overview</h2>\n              \n              {/* Quick Stats */}\n              <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8\">\n                <div className=\"bg-gradient-to-br from-[#15a7dd]/20 to-[#15a7dd]/10 rounded-lg p-6 border border-[#15a7dd]/30\">\n                  <h3 className=\"text-lg font-semibold mb-2\">Active Courses</h3>\n                  <div className=\"text-3xl font-bold text-[#15a7dd]\">{moodleData?.courses?.length || 0}</div>\n                </div>\n                \n                <div className=\"bg-gradient-to-br from-[#6a3293]/20 to-[#6a3293]/10 rounded-lg p-6 border border-[#6a3293]/30\">\n                  <h3 className=\"text-lg font-semibold mb-2\">Avg Progress</h3>\n                  <div className=\"text-3xl font-bold text-[#6a3293]\">\n                    {moodleData?.courses ? Math.round(moodleData.courses.reduce((acc, course) => acc + course.progress, 0) / moodleData.courses.length) : 0}%\n                  </div>\n                </div>\n                \n                <div className=\"bg-gradient-to-br from-yellow-500/20 to-yellow-500/10 rounded-lg p-6 border border-yellow-500/30\">\n                  <h3 className=\"text-lg font-semibold mb-2\">Notifications</h3>\n                  <div className=\"text-3xl font-bold text-yellow-500\">{moodleData?.notifications?.length || 0}</div>\n                </div>\n              </div>\n\n              {/* Recent Activity */}\n              <div className=\"bg-[#1a0f00] rounded-lg p-6 border border-gray-700\">\n                <h3 className=\"text-xl font-semibold mb-4\">Recent Activity</h3>\n                <div className=\"space-y-4\">\n                  {moodleData?.recentActivity?.map((activity, index) => (\n                    <div key={index} className=\"flex items-center justify-between p-3 bg-[#2a1a05] rounded\">\n                      <div className=\"flex items-center\">\n                        <i className={`fas ${activity.type === 'course_completion' ? 'fa-check-circle text-green-500' : 'fa-upload text-blue-500'} mr-3`}></i>\n                        <div>\n                          <p className=\"font-medium\">{activity.type.replace('_', ' ').toUpperCase()}</p>\n                          <p className=\"text-sm text-gray-400\">{activity.course}</p>\n                        </div>\n                      </div>\n                      <span className=\"text-sm text-gray-400\">{activity.timestamp}</span>\n                    </div>\n                  )) || (\n                    <p className=\"text-gray-400 text-center py-4\">No recent activity</p>\n                  )}\n                </div>\n              </div>\n            </div>\n\n            <div>\n              <h2 className=\"text-2xl font-serif font-bold mb-6\">Quick Actions</h2>\n              <div className=\"space-y-4\">\n                <Link to=\"/classroom\" className=\"block w-full p-4 bg-[#15a7dd] rounded-lg hover:bg-[#1397c7] transition-colors duration-300\">\n                  <div className=\"flex items-center\">\n                    <i className=\"fas fa-chalkboard-teacher mr-3 text-xl\"></i>\n                    <div>\n                      <h3 className=\"font-semibold\">My Classroom</h3>\n                      <p className=\"text-sm opacity-90\">Personal learning space</p>\n                    </div>\n                  </div>\n                </Link>\n\n                <a href=\"/learning\" className=\"block w-full p-4 bg-[#6a3293] rounded-lg hover:bg-[#5a2283] transition-colors duration-300\">\n                  <div className=\"flex items-center\">\n                    <i className=\"fas fa-graduation-cap mr-3 text-xl\"></i>\n                    <div>\n                      <h3 className=\"font-semibold\">Moodle LMS</h3>\n                      <p className=\"text-sm opacity-90\">Access full course system</p>\n                    </div>\n                  </div>\n                </a>\n\n                <Link to=\"/library\" className=\"block w-full p-4 bg-yellow-600 rounded-lg hover:bg-yellow-700 transition-colors duration-300\">\n                  <div className=\"flex items-center\">\n                    <i className=\"fas fa-book mr-3 text-xl\"></i>\n                    <div>\n                      <h3 className=\"font-semibold\">Library</h3>\n                      <p className=\"text-sm opacity-90\">Browse resources</p>\n                    </div>\n                  </div>\n                </Link>\n\n                <Link to=\"/departments\" className=\"block w-full p-4 bg-green-600 rounded-lg hover:bg-green-700 transition-colors duration-300\">\n                  <div className=\"flex items-center\">\n                    <i className=\"fas fa-building mr-3 text-xl\"></i>\n                    <div>\n                      <h3 className=\"font-semibold\">Departments</h3>\n                      <p className=\"text-sm opacity-90\">Explore specializations</p>\n                    </div>\n                  </div>\n                </Link>\n              </div>\n\n              {/* Notifications */}\n              <div className=\"mt-8 bg-[#1a0f00] rounded-lg p-6 border border-gray-700\">\n                <h3 className=\"text-xl font-semibold mb-4\">Notifications</h3>\n                <div className=\"space-y-3\">\n                  {moodleData?.notifications?.map((notification, index) => (\n                    <div key={index} className=\"p-3 bg-[#2a1a05] rounded border-l-4 border-yellow-500\">\n                      <p className=\"text-sm font-medium\">{notification.message}</p>\n                      {notification.course && (\n                        <p className=\"text-xs text-gray-400 mt-1\">{notification.course}</p>\n                      )}\n                    </div>\n                  )) || (\n                    <p className=\"text-gray-400 text-center py-4\">No notifications</p>\n                  )}\n                </div>\n              </div>\n            </div>\n          </div>\n        )}\n\n        {/* Courses Section */}\n        {activeSection === 'courses' && (\n          <div>\n            <h2 className=\"text-2xl font-serif font-bold mb-6\">Course Management</h2>\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n              {moodleData?.courses?.map((course) => (\n                <div key={course.id} className=\"bg-[#1a0f00] rounded-lg p-6 border border-gray-700\">\n                  <h3 className=\"text-xl font-semibold mb-4\">{course.name}</h3>\n                  <div className=\"mb-4\">\n                    <div className=\"flex justify-between text-sm mb-2\">\n                      <span>Progress</span>\n                      <span>{course.progress}%</span>\n                    </div>\n                    <div className=\"w-full bg-gray-700 rounded-full h-2\">\n                      <div className=\"bg-[#15a7dd] h-2 rounded-full\" style={{ width: `${course.progress}%` }}></div>\n                    </div>\n                  </div>\n                  <a href=\"/learning\" className=\"block w-full text-center py-2 bg-[#15a7dd] rounded hover:bg-[#1397c7] transition-colors duration-300\">\n                    Open in Moodle\n                  </a>\n                </div>\n              )) || (\n                <div className=\"col-span-full text-center py-12\">\n                  <p className=\"text-gray-400\">No courses found</p>\n                </div>\n              )}\n            </div>\n          </div>\n        )}\n\n        {/* Progress Section */}\n        {activeSection === 'progress' && (\n          <div>\n            <h2 className=\"text-2xl font-serif font-bold mb-6\">Learning Progress</h2>\n            <div className=\"bg-[#1a0f00] rounded-lg p-6 border border-gray-700\">\n              <p className=\"text-center text-gray-400 py-12\">\n                <i className=\"fas fa-chart-line text-4xl mb-4 block\"></i>\n                Detailed progress analytics coming soon!\n              </p>\n            </div>\n          </div>\n        )}\n\n        {/* Integration Section */}\n        {activeSection === 'integration' && (\n          <div>\n            <h2 className=\"text-2xl font-serif font-bold mb-6\">React-Moodle Integration</h2>\n            <MoodleIntegration />\n          </div>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default UnifiedDashboard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,IAAI,QAAQ,kBAAkB;AACvC,OAAOC,iBAAiB,MAAM,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpD,MAAMC,gBAAgB,GAAGA,CAAC;EAAEC;AAAK,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,mBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,oBAAA;EACrC,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGf,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAM,CAACgB,OAAO,EAAEC,UAAU,CAAC,GAAGjB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACkB,aAAa,EAAEC,gBAAgB,CAAC,GAAGnB,QAAQ,CAAC,UAAU,CAAC;EAE9DC,SAAS,CAAC,MAAM;IACd;IACA,MAAMmB,eAAe,GAAG,MAAAA,CAAA,KAAY;MAClC,IAAI;QACFH,UAAU,CAAC,IAAI,CAAC;QAChB;QACA;QACA,MAAMI,QAAQ,GAAG;UACfC,OAAO,EAAE,CACP;YAAEC,EAAE,EAAE,CAAC;YAAEC,IAAI,EAAE,oCAAoC;YAAEC,QAAQ,EAAE;UAAG,CAAC,EACnE;YAAEF,EAAE,EAAE,CAAC;YAAEC,IAAI,EAAE,0BAA0B;YAAEC,QAAQ,EAAE;UAAG,CAAC,EACzD;YAAEF,EAAE,EAAE,CAAC;YAAEC,IAAI,EAAE,wBAAwB;YAAEC,QAAQ,EAAE;UAAG,CAAC,CACxD;UACDC,cAAc,EAAE,CACd;YAAEC,IAAI,EAAE,mBAAmB;YAAEC,MAAM,EAAE,sBAAsB;YAAEC,SAAS,EAAE;UAAa,CAAC,EACtF;YAAEF,IAAI,EAAE,uBAAuB;YAAEC,MAAM,EAAE,kBAAkB;YAAEC,SAAS,EAAE;UAAa,CAAC,CACvF;UACDC,aAAa,EAAE,CACb;YAAEH,IAAI,EAAE,gBAAgB;YAAEI,OAAO,EAAE,0BAA0B;YAAEH,MAAM,EAAE;UAAmB,CAAC,EAC3F;YAAED,IAAI,EAAE,YAAY;YAAEI,OAAO,EAAE,wCAAwC;YAAEH,MAAM,EAAE;UAAK,CAAC;QAE3F,CAAC;QACDb,aAAa,CAACM,QAAQ,CAAC;MACzB,CAAC,CAAC,OAAOW,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACrD,CAAC,SAAS;QACRf,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDG,eAAe,CAAC,CAAC;EACnB,CAAC,EAAE,EAAE,CAAC;EAEN,IAAIJ,OAAO,EAAE;IACX,oBACEX,OAAA;MAAK6B,SAAS,EAAC,2FAA2F;MAAAC,QAAA,eACxG9B,OAAA;QAAK6B,SAAS,EAAC,8BAA8B;QAAAC,QAAA,eAC3C9B,OAAA;UAAK6B,SAAS,EAAC,uCAAuC;UAAAC,QAAA,eACpD9B,OAAA;YAAK6B,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1B9B,OAAA;cAAG6B,SAAS,EAAC;YAAqD;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACvElC,OAAA;cAAG6B,SAAS,EAAC,SAAS;cAAAC,QAAA,EAAC;YAAiC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACElC,OAAA;IAAK6B,SAAS,EAAC,2FAA2F;IAAAC,QAAA,eACxG9B,OAAA;MAAK6B,SAAS,EAAC,8BAA8B;MAAAC,QAAA,gBAE3C9B,OAAA;QAAK6B,SAAS,EAAC,OAAO;QAAAC,QAAA,gBACpB9B,OAAA;UAAI6B,SAAS,EAAC,gDAAgD;UAAAC,QAAA,gBAC5D9B,OAAA;YAAM6B,SAAS,EAAC,gBAAgB;YAAAC,QAAA,EAAC;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,uBACjD;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLlC,OAAA;UAAG6B,SAAS,EAAC,iCAAiC;UAAAC,QAAA,EAAC;QAE/C;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAGNlC,OAAA;QAAK6B,SAAS,EAAC,8CAA8C;QAAAC,QAAA,EAC1D,CAAC,UAAU,EAAE,SAAS,EAAE,UAAU,EAAE,aAAa,CAAC,CAACK,GAAG,CAAEC,OAAO,iBAC9DpC,OAAA;UAEEqC,OAAO,EAAEA,CAAA,KAAMvB,gBAAgB,CAACsB,OAAO,CAAE;UACzCP,SAAS,EAAE,gHACThB,aAAa,KAAKuB,OAAO,GACrB,4CAA4C,GAC5C,oCAAoC,EACvC;UAAAN,QAAA,EAEFM,OAAO,CAACE,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAGH,OAAO,CAACI,KAAK,CAAC,CAAC;QAAC,GAR9CJ,OAAO;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OASN,CACT;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,EAGLrB,aAAa,KAAK,UAAU,iBAC3Bb,OAAA;QAAK6B,SAAS,EAAC,uCAAuC;QAAAC,QAAA,gBACpD9B,OAAA;UAAK6B,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC5B9B,OAAA;YAAI6B,SAAS,EAAC,oCAAoC;YAAAC,QAAA,EAAC;UAAiB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAGzElC,OAAA;YAAK6B,SAAS,EAAC,4CAA4C;YAAAC,QAAA,gBACzD9B,OAAA;cAAK6B,SAAS,EAAC,+FAA+F;cAAAC,QAAA,gBAC5G9B,OAAA;gBAAI6B,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,EAAC;cAAc;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC9DlC,OAAA;gBAAK6B,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,EAAE,CAAArB,UAAU,aAAVA,UAAU,wBAAAL,mBAAA,GAAVK,UAAU,CAAEQ,OAAO,cAAAb,mBAAA,uBAAnBA,mBAAA,CAAqBqC,MAAM,KAAI;cAAC;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxF,CAAC,eAENlC,OAAA;cAAK6B,SAAS,EAAC,+FAA+F;cAAAC,QAAA,gBAC5G9B,OAAA;gBAAI6B,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,EAAC;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC5DlC,OAAA;gBAAK6B,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,GAC/CrB,UAAU,aAAVA,UAAU,eAAVA,UAAU,CAAEQ,OAAO,GAAGyB,IAAI,CAACC,KAAK,CAAClC,UAAU,CAACQ,OAAO,CAAC2B,MAAM,CAAC,CAACC,GAAG,EAAEtB,MAAM,KAAKsB,GAAG,GAAGtB,MAAM,CAACH,QAAQ,EAAE,CAAC,CAAC,GAAGX,UAAU,CAACQ,OAAO,CAACwB,MAAM,CAAC,GAAG,CAAC,EAAC,GAC1I;cAAA;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENlC,OAAA;cAAK6B,SAAS,EAAC,kGAAkG;cAAAC,QAAA,gBAC/G9B,OAAA;gBAAI6B,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,EAAC;cAAa;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC7DlC,OAAA;gBAAK6B,SAAS,EAAC,oCAAoC;gBAAAC,QAAA,EAAE,CAAArB,UAAU,aAAVA,UAAU,wBAAAJ,qBAAA,GAAVI,UAAU,CAAEgB,aAAa,cAAApB,qBAAA,uBAAzBA,qBAAA,CAA2BoC,MAAM,KAAI;cAAC;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/F,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNlC,OAAA;YAAK6B,SAAS,EAAC,oDAAoD;YAAAC,QAAA,gBACjE9B,OAAA;cAAI6B,SAAS,EAAC,4BAA4B;cAAAC,QAAA,EAAC;YAAe;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC/DlC,OAAA;cAAK6B,SAAS,EAAC,WAAW;cAAAC,QAAA,EACvB,CAAArB,UAAU,aAAVA,UAAU,wBAAAH,qBAAA,GAAVG,UAAU,CAAEY,cAAc,cAAAf,qBAAA,uBAA1BA,qBAAA,CAA4B6B,GAAG,CAAC,CAACW,QAAQ,EAAEC,KAAK,kBAC/C/C,OAAA;gBAAiB6B,SAAS,EAAC,4DAA4D;gBAAAC,QAAA,gBACrF9B,OAAA;kBAAK6B,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAChC9B,OAAA;oBAAG6B,SAAS,EAAE,OAAOiB,QAAQ,CAACxB,IAAI,KAAK,mBAAmB,GAAG,gCAAgC,GAAG,yBAAyB;kBAAQ;oBAAAS,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACtIlC,OAAA;oBAAA8B,QAAA,gBACE9B,OAAA;sBAAG6B,SAAS,EAAC,aAAa;sBAAAC,QAAA,EAAEgB,QAAQ,CAACxB,IAAI,CAAC0B,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,CAACT,WAAW,CAAC;oBAAC;sBAAAR,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAC9ElC,OAAA;sBAAG6B,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,EAAEgB,QAAQ,CAACvB;oBAAM;sBAAAQ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNlC,OAAA;kBAAM6B,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAEgB,QAAQ,CAACtB;gBAAS;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA,GAR3Da,KAAK;gBAAAhB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OASV,CACN,CAAC,kBACAlC,OAAA;gBAAG6B,SAAS,EAAC,gCAAgC;gBAAAC,QAAA,EAAC;cAAkB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG;YACpE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENlC,OAAA;UAAA8B,QAAA,gBACE9B,OAAA;YAAI6B,SAAS,EAAC,oCAAoC;YAAAC,QAAA,EAAC;UAAa;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACrElC,OAAA;YAAK6B,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxB9B,OAAA,CAACH,IAAI;cAACoD,EAAE,EAAC,YAAY;cAACpB,SAAS,EAAC,4FAA4F;cAAAC,QAAA,eAC1H9B,OAAA;gBAAK6B,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,gBAChC9B,OAAA;kBAAG6B,SAAS,EAAC;gBAAwC;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC1DlC,OAAA;kBAAA8B,QAAA,gBACE9B,OAAA;oBAAI6B,SAAS,EAAC,eAAe;oBAAAC,QAAA,EAAC;kBAAY;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC/ClC,OAAA;oBAAG6B,SAAS,EAAC,oBAAoB;oBAAAC,QAAA,EAAC;kBAAuB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1D,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eAEPlC,OAAA;cAAGkD,IAAI,EAAC,WAAW;cAACrB,SAAS,EAAC,4FAA4F;cAAAC,QAAA,eACxH9B,OAAA;gBAAK6B,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,gBAChC9B,OAAA;kBAAG6B,SAAS,EAAC;gBAAoC;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACtDlC,OAAA;kBAAA8B,QAAA,gBACE9B,OAAA;oBAAI6B,SAAS,EAAC,eAAe;oBAAAC,QAAA,EAAC;kBAAU;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC7ClC,OAAA;oBAAG6B,SAAS,EAAC,oBAAoB;oBAAAC,QAAA,EAAC;kBAAyB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5D,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eAEJlC,OAAA,CAACH,IAAI;cAACoD,EAAE,EAAC,UAAU;cAACpB,SAAS,EAAC,8FAA8F;cAAAC,QAAA,eAC1H9B,OAAA;gBAAK6B,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,gBAChC9B,OAAA;kBAAG6B,SAAS,EAAC;gBAA0B;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC5ClC,OAAA;kBAAA8B,QAAA,gBACE9B,OAAA;oBAAI6B,SAAS,EAAC,eAAe;oBAAAC,QAAA,EAAC;kBAAO;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC1ClC,OAAA;oBAAG6B,SAAS,EAAC,oBAAoB;oBAAAC,QAAA,EAAC;kBAAgB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eAEPlC,OAAA,CAACH,IAAI;cAACoD,EAAE,EAAC,cAAc;cAACpB,SAAS,EAAC,4FAA4F;cAAAC,QAAA,eAC5H9B,OAAA;gBAAK6B,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,gBAChC9B,OAAA;kBAAG6B,SAAS,EAAC;gBAA8B;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAChDlC,OAAA;kBAAA8B,QAAA,gBACE9B,OAAA;oBAAI6B,SAAS,EAAC,eAAe;oBAAAC,QAAA,EAAC;kBAAW;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC9ClC,OAAA;oBAAG6B,SAAS,EAAC,oBAAoB;oBAAAC,QAAA,EAAC;kBAAuB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1D,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eAGNlC,OAAA;YAAK6B,SAAS,EAAC,yDAAyD;YAAAC,QAAA,gBACtE9B,OAAA;cAAI6B,SAAS,EAAC,4BAA4B;cAAAC,QAAA,EAAC;YAAa;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC7DlC,OAAA;cAAK6B,SAAS,EAAC,WAAW;cAAAC,QAAA,EACvB,CAAArB,UAAU,aAAVA,UAAU,wBAAAF,sBAAA,GAAVE,UAAU,CAAEgB,aAAa,cAAAlB,sBAAA,uBAAzBA,sBAAA,CAA2B4B,GAAG,CAAC,CAACgB,YAAY,EAAEJ,KAAK,kBAClD/C,OAAA;gBAAiB6B,SAAS,EAAC,uDAAuD;gBAAAC,QAAA,gBAChF9B,OAAA;kBAAG6B,SAAS,EAAC,qBAAqB;kBAAAC,QAAA,EAAEqB,YAAY,CAACzB;gBAAO;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,EAC5DiB,YAAY,CAAC5B,MAAM,iBAClBvB,OAAA;kBAAG6B,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,EAAEqB,YAAY,CAAC5B;gBAAM;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CACnE;cAAA,GAJOa,KAAK;gBAAAhB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAKV,CACN,CAAC,kBACAlC,OAAA;gBAAG6B,SAAS,EAAC,gCAAgC;gBAAAC,QAAA,EAAC;cAAgB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG;YAClE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAGArB,aAAa,KAAK,SAAS,iBAC1Bb,OAAA;QAAA8B,QAAA,gBACE9B,OAAA;UAAI6B,SAAS,EAAC,oCAAoC;UAAAC,QAAA,EAAC;QAAiB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACzElC,OAAA;UAAK6B,SAAS,EAAC,sDAAsD;UAAAC,QAAA,EAClE,CAAArB,UAAU,aAAVA,UAAU,wBAAAD,oBAAA,GAAVC,UAAU,CAAEQ,OAAO,cAAAT,oBAAA,uBAAnBA,oBAAA,CAAqB2B,GAAG,CAAEZ,MAAM,iBAC/BvB,OAAA;YAAqB6B,SAAS,EAAC,oDAAoD;YAAAC,QAAA,gBACjF9B,OAAA;cAAI6B,SAAS,EAAC,4BAA4B;cAAAC,QAAA,EAAEP,MAAM,CAACJ;YAAI;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC7DlC,OAAA;cAAK6B,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnB9B,OAAA;gBAAK6B,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,gBAChD9B,OAAA;kBAAA8B,QAAA,EAAM;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACrBlC,OAAA;kBAAA8B,QAAA,GAAOP,MAAM,CAACH,QAAQ,EAAC,GAAC;gBAAA;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B,CAAC,eACNlC,OAAA;gBAAK6B,SAAS,EAAC,qCAAqC;gBAAAC,QAAA,eAClD9B,OAAA;kBAAK6B,SAAS,EAAC,+BAA+B;kBAACuB,KAAK,EAAE;oBAAEC,KAAK,EAAE,GAAG9B,MAAM,CAACH,QAAQ;kBAAI;gBAAE;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3F,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNlC,OAAA;cAAGkD,IAAI,EAAC,WAAW;cAACrB,SAAS,EAAC,sGAAsG;cAAAC,QAAA,EAAC;YAErI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA,GAbIX,MAAM,CAACL,EAAE;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAcd,CACN,CAAC,kBACAlC,OAAA;YAAK6B,SAAS,EAAC,iCAAiC;YAAAC,QAAA,eAC9C9B,OAAA;cAAG6B,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAgB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAGArB,aAAa,KAAK,UAAU,iBAC3Bb,OAAA;QAAA8B,QAAA,gBACE9B,OAAA;UAAI6B,SAAS,EAAC,oCAAoC;UAAAC,QAAA,EAAC;QAAiB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACzElC,OAAA;UAAK6B,SAAS,EAAC,oDAAoD;UAAAC,QAAA,eACjE9B,OAAA;YAAG6B,SAAS,EAAC,iCAAiC;YAAAC,QAAA,gBAC5C9B,OAAA;cAAG6B,SAAS,EAAC;YAAuC;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,4CAE3D;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAGArB,aAAa,KAAK,aAAa,iBAC9Bb,OAAA;QAAA8B,QAAA,gBACE9B,OAAA;UAAI6B,SAAS,EAAC,oCAAoC;UAAAC,QAAA,EAAC;QAAwB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAChFlC,OAAA,CAACF,iBAAiB;UAAAiC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClB,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC/B,EAAA,CAxPIF,gBAAgB;AAAAqD,EAAA,GAAhBrD,gBAAgB;AA0PtB,eAAeA,gBAAgB;AAAC,IAAAqD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}