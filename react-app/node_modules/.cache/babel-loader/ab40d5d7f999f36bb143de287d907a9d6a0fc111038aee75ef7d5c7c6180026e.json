{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/FatbeamU/react-app/src/pages/LibraryPages.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst LibraryPages = () => {\n  _s();\n  const [bookmarkedItems, setBookmarkedItems] = useState([]);\n  const [checkedOutItems, setCheckedOutItems] = useState([]);\n  const [selectedCategory, setSelectedCategory] = useState('all');\n  const [selectedDifficulty, setSelectedDifficulty] = useState('all');\n  const [searchQuery, setSearchQuery] = useState('');\n  const [showBookDetails, setShowBookDetails] = useState(false);\n  const [selectedBook, setSelectedBook] = useState(null);\n  const [filteredBooks, setFilteredBooks] = useState([]);\n  const [hoveredBook, setHoveredBook] = useState(null);\n  useEffect(() => {\n    const handleClickOutside = () => {\n      setHoveredBook(null);\n    };\n    document.addEventListener('click', handleClickOutside);\n    return () => {\n      document.removeEventListener('click', handleClickOutside);\n    };\n  }, []);\n\n  // Add styles for book animation\n  useEffect(() => {\n    const style = document.createElement('style');\n    style.textContent = `\n    .transform-origin-left {\n      transform-origin: left;\n    }\n    `;\n    document.head.appendChild(style);\n    return () => {\n      document.head.removeChild(style);\n    };\n  }, []);\n  const allBooks = [{\n    id: 'b1',\n    title: 'Network Protocols Overview',\n    author: 'Professor Elara Waveweaver',\n    description: 'A comprehensive guide to network protocols and their implementations in enterprise networks.',\n    difficulty: 'intermediate',\n    category: 'protocols',\n    wikiLink: '/networking/protocols/overview',\n    isUnique: false,\n    spineColor: 'bg-gradient-to-r from-[#3E2723] via-[#4E342E] to-[#3E2723]',\n    textColor: 'text-[#C0C0C0]',\n    coverImage: 'https://readdy.ai/api/search-image?query=Ancient%20magical%20book%20cover%20with%20network%20patterns%20and%20ethereal%20glowing%20symbols%20floating%20on%20a%20rich%20leather%20background%2C%20professional%20photography%20with%20dramatic%20lighting&width=400&height=600&seq=book1&orientation=portrait'\n  }, {\n    id: 'b5',\n    title: 'Fatbeam Rural Network Solutions',\n    author: 'Dr. Mystic Bytecaster',\n    description: 'Unique case study on implementing high-speed networks in rural communities with Fatbeam solutions.',\n    difficulty: 'advanced',\n    category: 'protocols',\n    wikiLink: '/fatbeam/case-studies/rural-networks',\n    isUnique: true,\n    spineColor: 'bg-gradient-to-r from-[#8B7355] via-[#A0856E] to-[#8B7355]',\n    textColor: 'text-[#D4AF37]',\n    coverImage: 'https://readdy.ai/api/search-image?query=Mystical%20book%20cover%20featuring%20flowing%20data%20streams%20and%20magical%20energy%20patterns%20on%20aged%20leather%20with%20gold%20accents%2C%20professional%20studio%20lighting&width=400&height=600&seq=book5&orientation=portrait'\n  }, {\n    id: 'b6',\n    title: 'Quantum Enchantment',\n    author: 'Sage Quantumweaver',\n    description: 'Advanced concepts in quantum networking and magical entanglement.',\n    difficulty: 'advanced',\n    category: 'fundamentals',\n    spineColor: 'bg-gradient-to-r from-[#5D4037] via-[#6D4C41] to-[#5D4037]',\n    textColor: 'text-[#C0C0C0]',\n    coverImage: 'https://readdy.ai/api/search-image?query=Quantum%20physics%20inspired%20magical%20book%20cover%20with%20intricate%20geometric%20patterns%20on%20deep%20purple%20leather%2C%20professional%20photography&width=400&height=600&seq=book6&orientation=portrait'\n  }, {\n    id: 'b7',\n    title: 'Runic Network Defense',\n    author: 'Guardian Shieldmaster',\n    description: 'Essential protection spells for magical network security.',\n    difficulty: 'intermediate',\n    category: 'security',\n    spineColor: 'bg-gradient-to-r from-[#4E342E] via-[#5D4037] to-[#4E342E]',\n    textColor: 'text-[#C0C0C0]',\n    coverImage: 'https://readdy.ai/api/search-image?query=Ancient%20protective%20runes%20and%20magical%20shields%20on%20dark%20green%20leather%20book%20cover%20with%20metallic%20embellishments%2C%20professional%20studio%20lighting&width=400&height=600&seq=book7&orientation=portrait'\n  }, {\n    id: 'b8',\n    title: 'Celestial Connections',\n    author: 'Astral Networker',\n    description: 'Establishing and maintaining connections through celestial planes.',\n    difficulty: 'advanced',\n    category: 'protocols',\n    spineColor: 'bg-gradient-to-r from-[#3E2723] via-[#4E342E] to-[#3E2723]',\n    textColor: 'text-[#C0C0C0]',\n    coverImage: 'https://readdy.ai/api/search-image?query=Celestial%20map%20and%20constellation%20patterns%20on%20midnight%20blue%20leather%20book%20cover%20with%20silver%20details%2C%20professional%20photography%20with%20dramatic%20lighting&width=400&height=600&seq=book8&orientation=portrait'\n  }, {\n    id: 'b2',\n    title: 'Troubleshooting the Ethereal Web',\n    author: 'Dr. Thorne Cablemancer',\n    description: 'Learn the art of identifying and resolving network disruptions across magical connections.',\n    difficulty: 'intermediate',\n    category: 'troubleshooting',\n    spineColor: 'bg-[#2A1B0E]',\n    textColor: 'text-[#C0C0C0]',\n    coverImage: 'https://readdy.ai/api/search-image?query=Mystical%20book%20cover%20featuring%20intricate%20network%20troubleshooting%20diagrams%20with%20magical%20aura%2C%20ancient%20leather%20texture%20background%20with%20professional%20studio%20lighting&width=400&height=600&seq=book2&orientation=portrait'\n  }, {\n    id: 'b3',\n    title: 'Fundamentals of Magical Networking',\n    author: 'Sage Bytewarden',\n    description: 'Essential concepts and principles of magical network architecture for beginners.',\n    difficulty: 'beginner',\n    category: 'fundamentals',\n    spineColor: 'bg-[#2A1B0E]',\n    textColor: 'text-[#C0C0C0]',\n    coverImage: 'https://readdy.ai/api/search-image?query=Enchanted%20book%20cover%20with%20basic%20networking%20symbols%20and%20magical%20runes%2C%20elegant%20leather%20binding%20with%20soft%20mystical%20glow%2C%20professional%20product%20photography&width=400&height=600&seq=book3&orientation=portrait'\n  }, {\n    id: 'b4',\n    title: 'Advanced Spellbound Security',\n    author: 'Master Cryptkeeper',\n    description: 'Advanced techniques for securing magical networks against dark forces.',\n    difficulty: 'advanced',\n    category: 'security',\n    spineColor: 'bg-[#2A1B0E]',\n    textColor: 'text-[#C0C0C0]',\n    coverImage: 'https://readdy.ai/api/search-image?query=Dark%20mysterious%20book%20cover%20with%20security%20sigils%20and%20protective%20magical%20barriers%2C%20ancient%20leather%20texture%20with%20metallic%20accents%2C%20professional%20studio%20lighting&width=400&height=600&seq=book4&orientation=portrait'\n  }];\n  useEffect(() => {\n    let results = allBooks;\n    if (searchQuery) {\n      const query = searchQuery.toLowerCase();\n      results = results.filter(book => book.title.toLowerCase().includes(query) || book.author.toLowerCase().includes(query) || book.description.toLowerCase().includes(query));\n    }\n    if (selectedCategory !== 'all') {\n      results = results.filter(book => book.category === selectedCategory);\n    }\n    if (selectedDifficulty !== 'all') {\n      results = results.filter(book => book.difficulty === selectedDifficulty);\n    }\n    setFilteredBooks(results);\n  }, [searchQuery, selectedCategory, selectedDifficulty]);\n  const BookSpine = ({\n    book\n  }) => {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `h-[300px] w-[60px] ${book.spineColor} cursor-pointer transition-all duration-500 hover:scale-105 hover:z-10 relative shadow-xl overflow-hidden group`,\n      onMouseEnter: () => setHoveredBook(book),\n      onMouseLeave: () => setHoveredBook(null),\n      onClick: () => {\n        setSelectedBook(book);\n        setShowBookDetails(true);\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute inset-0 flex items-center justify-center\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute inset-0\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute top-0 left-0 right-0 h-[8px] bg-gradient-to-b from-black/50 to-transparent\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 168,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute bottom-0 left-0 right-0 h-[8px] bg-gradient-to-t from-black/50 to-transparent\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 169,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute inset-y-0 left-0 w-[4px] bg-gradient-to-r from-black/50 to-transparent\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 170,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute inset-y-0 right-0 w-[4px] bg-gradient-to-l from-black/50 to-transparent\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 171,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute inset-0 flex items-center justify-center\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: `${book.textColor} text-sm font-serif vertical-text whitespace-nowrap transform -rotate-90 px-4 z-10 group-hover:scale-105 transition-transform duration-300 drop-shadow-lg tracking-wider`,\n              children: book.title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 175,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 174,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 166,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 165,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 156,\n      columnNumber: 7\n    }, this);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-[#0a0500] pt-20\",\n    children: [/*#__PURE__*/_jsxDEV(\"header\", {\n      className: \"bg-[#1a0f00] border-b border-[#3a2a15] p-6\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container mx-auto flex justify-between items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-3xl text-amber-100 font-bold\",\n          children: \"Fatbeam University Library\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 189,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center gap-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"relative\",\n            children: [/*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              placeholder: \"Search books...\",\n              className: \"bg-[#2a1a05] text-amber-100 px-4 py-2 rounded-lg pl-10 focus:outline-none focus:ring-2 focus:ring-amber-500\",\n              value: searchQuery,\n              onChange: e => setSearchQuery(e.target.value)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 192,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-amber-500\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 199,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 191,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => window.open('/learning/mod/wiki/index.php', '_blank'),\n            className: \"bg-blue-700 text-blue-100 px-4 py-2 rounded-lg hover:bg-blue-600 transition-colors rounded-button\",\n            title: \"Access Moodle Wiki - Knowledge Base & Documentation\",\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fas fa-wiki-w mr-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 206,\n              columnNumber: 15\n            }, this), \"Wiki\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 201,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"bg-amber-700 text-amber-100 px-4 py-2 rounded-lg hover:bg-amber-600 transition-colors rounded-button\",\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fas fa-bookmark mr-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 210,\n              columnNumber: 15\n            }, this), \"Bookmarked (\", bookmarkedItems.length, \")\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 209,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 190,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 188,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 187,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n      className: \"container mx-auto p-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex gap-6 mb-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex-1\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"block text-amber-200 mb-2\",\n            children: \"Category\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 220,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"relative\",\n            children: [/*#__PURE__*/_jsxDEV(\"select\", {\n              className: \"w-full bg-[#2a1a05] text-amber-100 px-4 py-2 rounded-lg appearance-none cursor-pointer\",\n              value: selectedCategory,\n              onChange: e => setSelectedCategory(e.target.value),\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"all\",\n                children: \"All Categories\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 227,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"protocols\",\n                children: \"Protocols\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 228,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"troubleshooting\",\n                children: \"Troubleshooting\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 229,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"fundamentals\",\n                children: \"Fundamentals\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 230,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"security\",\n                children: \"Security\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 231,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 222,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fas fa-chevron-down absolute right-4 top-1/2 transform -translate-y-1/2 text-amber-500\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 233,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 221,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 219,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex-1\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"block text-amber-200 mb-2\",\n            children: \"Difficulty\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 237,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"relative\",\n            children: [/*#__PURE__*/_jsxDEV(\"select\", {\n              className: \"w-full bg-[#2a1a05] text-amber-100 px-4 py-2 rounded-lg appearance-none cursor-pointer\",\n              value: selectedDifficulty,\n              onChange: e => setSelectedDifficulty(e.target.value),\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"all\",\n                children: \"All Levels\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 244,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"beginner\",\n                children: \"Beginner\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 245,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"intermediate\",\n                children: \"Intermediate\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 246,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"advanced\",\n                children: \"Advanced\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 247,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 239,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fas fa-chevron-down absolute right-4 top-1/2 transform -translate-y-1/2 text-amber-500\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 249,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 238,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 236,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 218,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-gradient-to-r from-blue-900 to-blue-800 p-6 rounded-xl shadow-2xl mb-8 border border-blue-700\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-2xl font-bold text-blue-100 mb-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-wiki-w mr-3\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 259,\n                columnNumber: 17\n              }, this), \"Knowledge Base & Wiki\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 258,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-blue-200 mb-4\",\n              children: \"Access our comprehensive wiki for detailed documentation, procedures, and collaborative knowledge sharing. Create, edit, and maintain technical documentation with your team.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 262,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex gap-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => window.open('/learning/mod/wiki/index.php', '_blank'),\n                className: \"bg-blue-600 text-blue-100 px-6 py-3 rounded-lg hover:bg-blue-500 transition-colors font-semibold\",\n                children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"fas fa-external-link-alt mr-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 271,\n                  columnNumber: 19\n                }, this), \"Browse Wiki\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 267,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => window.open('/learning/mod/wiki/create.php?action=new', '_blank'),\n                className: \"bg-green-600 text-green-100 px-6 py-3 rounded-lg hover:bg-green-500 transition-colors font-semibold\",\n                children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"fas fa-plus mr-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 278,\n                  columnNumber: 19\n                }, this), \"Create New Page\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 274,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 266,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 257,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"hidden md:block\",\n            children: /*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fas fa-book-open text-6xl text-blue-300 opacity-50\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 284,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 283,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 256,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 255,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-[url('https://readdy.ai/api/search-image?query=Antique%20wooden%20bookshelf%20with%20intricate%20carvings%20and%20warm%20ambient%20lighting%2C%20rich%20mahogany%20texture%20with%20mystical%20aura%2C%20vintage%20library%20atmosphere&width=1440&height=900&seq=shelf&orientation=landscape')] bg-cover bg-center p-8 rounded-xl shadow-2xl min-h-[900px] flex items-center\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex gap-2 items-end\",\n          children: allBooks.map(book => /*#__PURE__*/_jsxDEV(BookSpine, {\n            book: book\n          }, book.id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 292,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 290,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 289,\n        columnNumber: 9\n      }, this), hoveredBook && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-[#1a0f00] p-6 rounded-lg shadow-xl border border-[#3a2a15] max-w-md z-50\",\n        onClick: e => e.stopPropagation(),\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex gap-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"img\", {\n            src: hoveredBook.coverImage,\n            alt: hoveredBook.title,\n            className: \"w-32 h-48 object-cover rounded-lg shadow-lg\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 303,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-xl font-bold text-amber-100 mb-2\",\n              children: hoveredBook.title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 305,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-amber-200 text-sm mb-2\",\n              children: [\"By \", hoveredBook.author]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 306,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-amber-300 text-sm mb-4\",\n              children: hoveredBook.description\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 307,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex gap-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"bg-amber-700 text-amber-100 px-4 py-2 text-sm rounded-lg hover:bg-amber-600 transition-colors rounded-button\",\n                children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"fas fa-bookmark mr-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 310,\n                  columnNumber: 21\n                }, this), \"Bookmark\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 309,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => {\n                  const overlay = document.createElement('div');\n                  overlay.className = 'fixed inset-0 bg-black/80 z-[60] flex items-center justify-center';\n                  const book = document.createElement('div');\n                  book.className = 'w-[300px] h-[400px] relative transform transition-all duration-1000';\n                  book.style.perspective = '1000px';\n                  const cover = document.createElement('div');\n                  cover.className = `absolute inset-0 bg-cover bg-center rounded-r-lg transform-origin-left transition-transform duration-1000`;\n                  cover.style.backgroundImage = `url(${hoveredBook.coverImage})`;\n                  cover.style.transformStyle = 'preserve-3d';\n                  book.appendChild(cover);\n                  overlay.appendChild(book);\n                  document.body.appendChild(overlay);\n                  setTimeout(() => {\n                    cover.style.transform = 'rotateY(-180deg)';\n                  }, 100);\n                  setTimeout(() => {\n                    window.location.href = hoveredBook.wikiLink;\n                  }, 1000);\n                },\n                className: \"bg-green-700 text-green-100 px-4 py-2 text-sm rounded-lg hover:bg-green-600 transition-colors rounded-button\",\n                children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"fas fa-book-reader mr-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 336,\n                  columnNumber: 21\n                }, this), hoveredBook.isUnique ? 'View Case Study' : 'Read Article']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 313,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 308,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 304,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 302,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 298,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 217,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 186,\n    columnNumber: 5\n  }, this);\n};\n_s(LibraryPages, \"KB5tfXqv6PTtkSepAxBje7UoY9A=\");\n_c = LibraryPages;\nexport default LibraryPages;\nvar _c;\n$RefreshReg$(_c, \"LibraryPages\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "jsxDEV", "_jsxDEV", "LibraryPages", "_s", "bookmarkedItems", "setBookmarkedItems", "checkedOutItems", "setCheckedOutItems", "selectedCate<PERSON><PERSON>", "setSelectedCategory", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "setSelectedDifficulty", "searchQuery", "setSearch<PERSON>uery", "showBookDetails", "setShowBookDetails", "selected<PERSON><PERSON>", "setSelectedBook", "filteredBooks", "setFilteredBooks", "hoveredBook", "setHoveredBook", "handleClickOutside", "document", "addEventListener", "removeEventListener", "style", "createElement", "textContent", "head", "append<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "allBooks", "id", "title", "author", "description", "difficulty", "category", "wikiLink", "isUnique", "spineColor", "textColor", "coverImage", "results", "query", "toLowerCase", "filter", "book", "includes", "BookSpine", "className", "onMouseEnter", "onMouseLeave", "onClick", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "placeholder", "value", "onChange", "e", "target", "window", "open", "length", "map", "stopPropagation", "src", "alt", "overlay", "perspective", "cover", "backgroundImage", "transformStyle", "body", "setTimeout", "transform", "location", "href", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/FatbeamU/react-app/src/pages/LibraryPages.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\n\nconst LibraryPages = () => {\n  const [bookmarkedItems, setBookmarkedItems] = useState([]);\n  const [checkedOutItems, setCheckedOutItems] = useState([]);\n  const [selectedCategory, setSelectedCategory] = useState('all');\n  const [selectedDifficulty, setSelectedDifficulty] = useState('all');\n  const [searchQuery, setSearchQuery] = useState('');\n  const [showBookDetails, setShowBookDetails] = useState(false);\n  const [selectedBook, setSelectedBook] = useState(null);\n  const [filteredBooks, setFilteredBooks] = useState([]);\n  const [hoveredBook, setHoveredBook] = useState(null);\n\n  useEffect(() => {\n    const handleClickOutside = () => {\n      setHoveredBook(null);\n    };\n\n    document.addEventListener('click', handleClickOutside);\n\n    return () => {\n      document.removeEventListener('click', handleClickOutside);\n    };\n  }, []);\n\n  // Add styles for book animation\n  useEffect(() => {\n    const style = document.createElement('style');\n    style.textContent = `\n    .transform-origin-left {\n      transform-origin: left;\n    }\n    `;\n    document.head.appendChild(style);\n    return () => {\n      document.head.removeChild(style);\n    };\n  }, []);\n\n  const allBooks = [\n    {\n      id: 'b1',\n      title: 'Network Protocols Overview',\n      author: 'Professor Elara Waveweaver',\n      description: 'A comprehensive guide to network protocols and their implementations in enterprise networks.',\n      difficulty: 'intermediate',\n      category: 'protocols',\n      wikiLink: '/networking/protocols/overview',\n      isUnique: false,\n      spineColor: 'bg-gradient-to-r from-[#3E2723] via-[#4E342E] to-[#3E2723]',\n      textColor: 'text-[#C0C0C0]',\n      coverImage: 'https://readdy.ai/api/search-image?query=Ancient%20magical%20book%20cover%20with%20network%20patterns%20and%20ethereal%20glowing%20symbols%20floating%20on%20a%20rich%20leather%20background%2C%20professional%20photography%20with%20dramatic%20lighting&width=400&height=600&seq=book1&orientation=portrait'\n    },\n    {\n      id: 'b5',\n      title: 'Fatbeam Rural Network Solutions',\n      author: 'Dr. Mystic Bytecaster',\n      description: 'Unique case study on implementing high-speed networks in rural communities with Fatbeam solutions.',\n      difficulty: 'advanced',\n      category: 'protocols',\n      wikiLink: '/fatbeam/case-studies/rural-networks',\n      isUnique: true,\n      spineColor: 'bg-gradient-to-r from-[#8B7355] via-[#A0856E] to-[#8B7355]',\n      textColor: 'text-[#D4AF37]',\n      coverImage: 'https://readdy.ai/api/search-image?query=Mystical%20book%20cover%20featuring%20flowing%20data%20streams%20and%20magical%20energy%20patterns%20on%20aged%20leather%20with%20gold%20accents%2C%20professional%20studio%20lighting&width=400&height=600&seq=book5&orientation=portrait'\n    },\n    {\n      id: 'b6',\n      title: 'Quantum Enchantment',\n      author: 'Sage Quantumweaver',\n      description: 'Advanced concepts in quantum networking and magical entanglement.',\n      difficulty: 'advanced',\n      category: 'fundamentals',\n      spineColor: 'bg-gradient-to-r from-[#5D4037] via-[#6D4C41] to-[#5D4037]',\n      textColor: 'text-[#C0C0C0]',\n      coverImage: 'https://readdy.ai/api/search-image?query=Quantum%20physics%20inspired%20magical%20book%20cover%20with%20intricate%20geometric%20patterns%20on%20deep%20purple%20leather%2C%20professional%20photography&width=400&height=600&seq=book6&orientation=portrait'\n    },\n    {\n      id: 'b7',\n      title: 'Runic Network Defense',\n      author: 'Guardian Shieldmaster',\n      description: 'Essential protection spells for magical network security.',\n      difficulty: 'intermediate',\n      category: 'security',\n      spineColor: 'bg-gradient-to-r from-[#4E342E] via-[#5D4037] to-[#4E342E]',\n      textColor: 'text-[#C0C0C0]',\n      coverImage: 'https://readdy.ai/api/search-image?query=Ancient%20protective%20runes%20and%20magical%20shields%20on%20dark%20green%20leather%20book%20cover%20with%20metallic%20embellishments%2C%20professional%20studio%20lighting&width=400&height=600&seq=book7&orientation=portrait'\n    },\n    {\n      id: 'b8',\n      title: 'Celestial Connections',\n      author: 'Astral Networker',\n      description: 'Establishing and maintaining connections through celestial planes.',\n      difficulty: 'advanced',\n      category: 'protocols',\n      spineColor: 'bg-gradient-to-r from-[#3E2723] via-[#4E342E] to-[#3E2723]',\n      textColor: 'text-[#C0C0C0]',\n      coverImage: 'https://readdy.ai/api/search-image?query=Celestial%20map%20and%20constellation%20patterns%20on%20midnight%20blue%20leather%20book%20cover%20with%20silver%20details%2C%20professional%20photography%20with%20dramatic%20lighting&width=400&height=600&seq=book8&orientation=portrait'\n    },\n    {\n      id: 'b2',\n      title: 'Troubleshooting the Ethereal Web',\n      author: 'Dr. Thorne Cablemancer',\n      description: 'Learn the art of identifying and resolving network disruptions across magical connections.',\n      difficulty: 'intermediate',\n      category: 'troubleshooting',\n      spineColor: 'bg-[#2A1B0E]',\n      textColor: 'text-[#C0C0C0]',\n      coverImage: 'https://readdy.ai/api/search-image?query=Mystical%20book%20cover%20featuring%20intricate%20network%20troubleshooting%20diagrams%20with%20magical%20aura%2C%20ancient%20leather%20texture%20background%20with%20professional%20studio%20lighting&width=400&height=600&seq=book2&orientation=portrait'\n    },\n    {\n      id: 'b3',\n      title: 'Fundamentals of Magical Networking',\n      author: 'Sage Bytewarden',\n      description: 'Essential concepts and principles of magical network architecture for beginners.',\n      difficulty: 'beginner',\n      category: 'fundamentals',\n      spineColor: 'bg-[#2A1B0E]',\n      textColor: 'text-[#C0C0C0]',\n      coverImage: 'https://readdy.ai/api/search-image?query=Enchanted%20book%20cover%20with%20basic%20networking%20symbols%20and%20magical%20runes%2C%20elegant%20leather%20binding%20with%20soft%20mystical%20glow%2C%20professional%20product%20photography&width=400&height=600&seq=book3&orientation=portrait'\n    },\n    {\n      id: 'b4',\n      title: 'Advanced Spellbound Security',\n      author: 'Master Cryptkeeper',\n      description: 'Advanced techniques for securing magical networks against dark forces.',\n      difficulty: 'advanced',\n      category: 'security',\n      spineColor: 'bg-[#2A1B0E]',\n      textColor: 'text-[#C0C0C0]',\n      coverImage: 'https://readdy.ai/api/search-image?query=Dark%20mysterious%20book%20cover%20with%20security%20sigils%20and%20protective%20magical%20barriers%2C%20ancient%20leather%20texture%20with%20metallic%20accents%2C%20professional%20studio%20lighting&width=400&height=600&seq=book4&orientation=portrait'\n    }\n  ];\n\n  useEffect(() => {\n    let results = allBooks;\n    if (searchQuery) {\n      const query = searchQuery.toLowerCase();\n      results = results.filter(book =>\n        book.title.toLowerCase().includes(query) ||\n        book.author.toLowerCase().includes(query) ||\n        book.description.toLowerCase().includes(query)\n      );\n    }\n    if (selectedCategory !== 'all') {\n      results = results.filter(book => book.category === selectedCategory);\n    }\n    if (selectedDifficulty !== 'all') {\n      results = results.filter(book => book.difficulty === selectedDifficulty);\n    }\n    setFilteredBooks(results);\n  }, [searchQuery, selectedCategory, selectedDifficulty]);\n\n  const BookSpine = ({ book }) => {\n    return (\n      <div\n        className={`h-[300px] w-[60px] ${book.spineColor} cursor-pointer transition-all duration-500 hover:scale-105 hover:z-10 relative shadow-xl overflow-hidden group`}\n        onMouseEnter={() => setHoveredBook(book)}\n        onMouseLeave={() => setHoveredBook(null)}\n        onClick={() => {\n          setSelectedBook(book);\n          setShowBookDetails(true);\n        }}\n      >\n        <div className=\"absolute inset-0 flex items-center justify-center\">\n          <div className=\"absolute inset-0\">\n            {/* Enhanced spine edges and embossing */}\n            <div className=\"absolute top-0 left-0 right-0 h-[8px] bg-gradient-to-b from-black/50 to-transparent\"></div>\n            <div className=\"absolute bottom-0 left-0 right-0 h-[8px] bg-gradient-to-t from-black/50 to-transparent\"></div>\n            <div className=\"absolute inset-y-0 left-0 w-[4px] bg-gradient-to-r from-black/50 to-transparent\"></div>\n            <div className=\"absolute inset-y-0 right-0 w-[4px] bg-gradient-to-l from-black/50 to-transparent\"></div>\n\n            {/* Book title with enhanced styling */}\n            <div className=\"absolute inset-0 flex items-center justify-center\">\n              <span className={`${book.textColor} text-sm font-serif vertical-text whitespace-nowrap transform -rotate-90 px-4 z-10 group-hover:scale-105 transition-transform duration-300 drop-shadow-lg tracking-wider`}>\n                {book.title}\n              </span>\n            </div>\n          </div>\n        </div>\n      </div>\n    );\n  };\n\n  return (\n    <div className=\"min-h-screen bg-[#0a0500] pt-20\">\n      <header className=\"bg-[#1a0f00] border-b border-[#3a2a15] p-6\">\n        <div className=\"container mx-auto flex justify-between items-center\">\n          <h1 className=\"text-3xl text-amber-100 font-bold\">Fatbeam University Library</h1>\n          <div className=\"flex items-center gap-4\">\n            <div className=\"relative\">\n              <input\n                type=\"text\"\n                placeholder=\"Search books...\"\n                className=\"bg-[#2a1a05] text-amber-100 px-4 py-2 rounded-lg pl-10 focus:outline-none focus:ring-2 focus:ring-amber-500\"\n                value={searchQuery}\n                onChange={(e) => setSearchQuery(e.target.value)}\n              />\n              <i className=\"fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-amber-500\"></i>\n            </div>\n            <button\n              onClick={() => window.open('/learning/mod/wiki/index.php', '_blank')}\n              className=\"bg-blue-700 text-blue-100 px-4 py-2 rounded-lg hover:bg-blue-600 transition-colors rounded-button\"\n              title=\"Access Moodle Wiki - Knowledge Base & Documentation\"\n            >\n              <i className=\"fas fa-wiki-w mr-2\"></i>\n              Wiki\n            </button>\n            <button className=\"bg-amber-700 text-amber-100 px-4 py-2 rounded-lg hover:bg-amber-600 transition-colors rounded-button\">\n              <i className=\"fas fa-bookmark mr-2\"></i>\n              Bookmarked ({bookmarkedItems.length})\n            </button>\n          </div>\n        </div>\n      </header>\n\n      <main className=\"container mx-auto p-8\">\n        <div className=\"flex gap-6 mb-8\">\n          <div className=\"flex-1\">\n            <label className=\"block text-amber-200 mb-2\">Category</label>\n            <div className=\"relative\">\n              <select\n                className=\"w-full bg-[#2a1a05] text-amber-100 px-4 py-2 rounded-lg appearance-none cursor-pointer\"\n                value={selectedCategory}\n                onChange={(e) => setSelectedCategory(e.target.value)}\n              >\n                <option value=\"all\">All Categories</option>\n                <option value=\"protocols\">Protocols</option>\n                <option value=\"troubleshooting\">Troubleshooting</option>\n                <option value=\"fundamentals\">Fundamentals</option>\n                <option value=\"security\">Security</option>\n              </select>\n              <i className=\"fas fa-chevron-down absolute right-4 top-1/2 transform -translate-y-1/2 text-amber-500\"></i>\n            </div>\n          </div>\n          <div className=\"flex-1\">\n            <label className=\"block text-amber-200 mb-2\">Difficulty</label>\n            <div className=\"relative\">\n              <select\n                className=\"w-full bg-[#2a1a05] text-amber-100 px-4 py-2 rounded-lg appearance-none cursor-pointer\"\n                value={selectedDifficulty}\n                onChange={(e) => setSelectedDifficulty(e.target.value)}\n              >\n                <option value=\"all\">All Levels</option>\n                <option value=\"beginner\">Beginner</option>\n                <option value=\"intermediate\">Intermediate</option>\n                <option value=\"advanced\">Advanced</option>\n              </select>\n              <i className=\"fas fa-chevron-down absolute right-4 top-1/2 transform -translate-y-1/2 text-amber-500\"></i>\n            </div>\n          </div>\n        </div>\n\n        {/* Wiki Knowledge Base Section */}\n        <div className=\"bg-gradient-to-r from-blue-900 to-blue-800 p-6 rounded-xl shadow-2xl mb-8 border border-blue-700\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <h2 className=\"text-2xl font-bold text-blue-100 mb-2\">\n                <i className=\"fas fa-wiki-w mr-3\"></i>\n                Knowledge Base & Wiki\n              </h2>\n              <p className=\"text-blue-200 mb-4\">\n                Access our comprehensive wiki for detailed documentation, procedures, and collaborative knowledge sharing.\n                Create, edit, and maintain technical documentation with your team.\n              </p>\n              <div className=\"flex gap-4\">\n                <button\n                  onClick={() => window.open('/learning/mod/wiki/index.php', '_blank')}\n                  className=\"bg-blue-600 text-blue-100 px-6 py-3 rounded-lg hover:bg-blue-500 transition-colors font-semibold\"\n                >\n                  <i className=\"fas fa-external-link-alt mr-2\"></i>\n                  Browse Wiki\n                </button>\n                <button\n                  onClick={() => window.open('/learning/mod/wiki/create.php?action=new', '_blank')}\n                  className=\"bg-green-600 text-green-100 px-6 py-3 rounded-lg hover:bg-green-500 transition-colors font-semibold\"\n                >\n                  <i className=\"fas fa-plus mr-2\"></i>\n                  Create New Page\n                </button>\n              </div>\n            </div>\n            <div className=\"hidden md:block\">\n              <i className=\"fas fa-book-open text-6xl text-blue-300 opacity-50\"></i>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-[url('https://readdy.ai/api/search-image?query=Antique%20wooden%20bookshelf%20with%20intricate%20carvings%20and%20warm%20ambient%20lighting%2C%20rich%20mahogany%20texture%20with%20mystical%20aura%2C%20vintage%20library%20atmosphere&width=1440&height=900&seq=shelf&orientation=landscape')] bg-cover bg-center p-8 rounded-xl shadow-2xl min-h-[900px] flex items-center\">\n          <div className=\"flex gap-2 items-end\">\n            {allBooks.map(book => (\n              <BookSpine key={book.id} book={book} />\n            ))}\n          </div>\n        </div>\n\n        {hoveredBook && (\n          <div\n            className=\"fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-[#1a0f00] p-6 rounded-lg shadow-xl border border-[#3a2a15] max-w-md z-50\"\n            onClick={(e) => e.stopPropagation()}\n          >\n            <div className=\"flex gap-6\">\n              <img src={hoveredBook.coverImage} alt={hoveredBook.title} className=\"w-32 h-48 object-cover rounded-lg shadow-lg\" />\n              <div>\n                <h3 className=\"text-xl font-bold text-amber-100 mb-2\">{hoveredBook.title}</h3>\n                <p className=\"text-amber-200 text-sm mb-2\">By {hoveredBook.author}</p>\n                <p className=\"text-amber-300 text-sm mb-4\">{hoveredBook.description}</p>\n                <div className=\"flex gap-2\">\n                  <button className=\"bg-amber-700 text-amber-100 px-4 py-2 text-sm rounded-lg hover:bg-amber-600 transition-colors rounded-button\">\n                    <i className=\"fas fa-bookmark mr-2\"></i>\n                    Bookmark\n                  </button>\n                  <button\n                    onClick={() => {\n                      const overlay = document.createElement('div');\n                      overlay.className = 'fixed inset-0 bg-black/80 z-[60] flex items-center justify-center';\n                      const book = document.createElement('div');\n                      book.className = 'w-[300px] h-[400px] relative transform transition-all duration-1000';\n                      book.style.perspective = '1000px';\n                      const cover = document.createElement('div');\n                      cover.className = `absolute inset-0 bg-cover bg-center rounded-r-lg transform-origin-left transition-transform duration-1000`;\n                      cover.style.backgroundImage = `url(${hoveredBook.coverImage})`;\n                      cover.style.transformStyle = 'preserve-3d';\n                      book.appendChild(cover);\n                      overlay.appendChild(book);\n                      document.body.appendChild(overlay);\n                      setTimeout(() => {\n                        cover.style.transform = 'rotateY(-180deg)';\n                      }, 100);\n                      setTimeout(() => {\n                        window.location.href = hoveredBook.wikiLink;\n                      }, 1000);\n                    }}\n                    className=\"bg-green-700 text-green-100 px-4 py-2 text-sm rounded-lg hover:bg-green-600 transition-colors rounded-button\"\n                  >\n                    <i className=\"fas fa-book-reader mr-2\"></i>\n                    {hoveredBook.isUnique ? 'View Case Study' : 'Read Article'}\n                  </button>\n                </div>\n              </div>\n            </div>\n          </div>\n        )}\n      </main>\n    </div>\n  );\n};\n\nexport default LibraryPages;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnD,MAAMC,YAAY,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzB,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAGP,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACQ,eAAe,EAAEC,kBAAkB,CAAC,GAAGT,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACU,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGX,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACY,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGb,QAAQ,CAAC,KAAK,CAAC;EACnE,MAAM,CAACc,WAAW,EAAEC,cAAc,CAAC,GAAGf,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACgB,eAAe,EAAEC,kBAAkB,CAAC,GAAGjB,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACkB,YAAY,EAAEC,eAAe,CAAC,GAAGnB,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACoB,aAAa,EAAEC,gBAAgB,CAAC,GAAGrB,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACsB,WAAW,EAAEC,cAAc,CAAC,GAAGvB,QAAQ,CAAC,IAAI,CAAC;EAEpDC,SAAS,CAAC,MAAM;IACd,MAAMuB,kBAAkB,GAAGA,CAAA,KAAM;MAC/BD,cAAc,CAAC,IAAI,CAAC;IACtB,CAAC;IAEDE,QAAQ,CAACC,gBAAgB,CAAC,OAAO,EAAEF,kBAAkB,CAAC;IAEtD,OAAO,MAAM;MACXC,QAAQ,CAACE,mBAAmB,CAAC,OAAO,EAAEH,kBAAkB,CAAC;IAC3D,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAvB,SAAS,CAAC,MAAM;IACd,MAAM2B,KAAK,GAAGH,QAAQ,CAACI,aAAa,CAAC,OAAO,CAAC;IAC7CD,KAAK,CAACE,WAAW,GAAG;AACxB;AACA;AACA;AACA,KAAK;IACDL,QAAQ,CAACM,IAAI,CAACC,WAAW,CAACJ,KAAK,CAAC;IAChC,OAAO,MAAM;MACXH,QAAQ,CAACM,IAAI,CAACE,WAAW,CAACL,KAAK,CAAC;IAClC,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMM,QAAQ,GAAG,CACf;IACEC,EAAE,EAAE,IAAI;IACRC,KAAK,EAAE,4BAA4B;IACnCC,MAAM,EAAE,4BAA4B;IACpCC,WAAW,EAAE,8FAA8F;IAC3GC,UAAU,EAAE,cAAc;IAC1BC,QAAQ,EAAE,WAAW;IACrBC,QAAQ,EAAE,gCAAgC;IAC1CC,QAAQ,EAAE,KAAK;IACfC,UAAU,EAAE,4DAA4D;IACxEC,SAAS,EAAE,gBAAgB;IAC3BC,UAAU,EAAE;EACd,CAAC,EACD;IACEV,EAAE,EAAE,IAAI;IACRC,KAAK,EAAE,iCAAiC;IACxCC,MAAM,EAAE,uBAAuB;IAC/BC,WAAW,EAAE,oGAAoG;IACjHC,UAAU,EAAE,UAAU;IACtBC,QAAQ,EAAE,WAAW;IACrBC,QAAQ,EAAE,sCAAsC;IAChDC,QAAQ,EAAE,IAAI;IACdC,UAAU,EAAE,4DAA4D;IACxEC,SAAS,EAAE,gBAAgB;IAC3BC,UAAU,EAAE;EACd,CAAC,EACD;IACEV,EAAE,EAAE,IAAI;IACRC,KAAK,EAAE,qBAAqB;IAC5BC,MAAM,EAAE,oBAAoB;IAC5BC,WAAW,EAAE,mEAAmE;IAChFC,UAAU,EAAE,UAAU;IACtBC,QAAQ,EAAE,cAAc;IACxBG,UAAU,EAAE,4DAA4D;IACxEC,SAAS,EAAE,gBAAgB;IAC3BC,UAAU,EAAE;EACd,CAAC,EACD;IACEV,EAAE,EAAE,IAAI;IACRC,KAAK,EAAE,uBAAuB;IAC9BC,MAAM,EAAE,uBAAuB;IAC/BC,WAAW,EAAE,2DAA2D;IACxEC,UAAU,EAAE,cAAc;IAC1BC,QAAQ,EAAE,UAAU;IACpBG,UAAU,EAAE,4DAA4D;IACxEC,SAAS,EAAE,gBAAgB;IAC3BC,UAAU,EAAE;EACd,CAAC,EACD;IACEV,EAAE,EAAE,IAAI;IACRC,KAAK,EAAE,uBAAuB;IAC9BC,MAAM,EAAE,kBAAkB;IAC1BC,WAAW,EAAE,oEAAoE;IACjFC,UAAU,EAAE,UAAU;IACtBC,QAAQ,EAAE,WAAW;IACrBG,UAAU,EAAE,4DAA4D;IACxEC,SAAS,EAAE,gBAAgB;IAC3BC,UAAU,EAAE;EACd,CAAC,EACD;IACEV,EAAE,EAAE,IAAI;IACRC,KAAK,EAAE,kCAAkC;IACzCC,MAAM,EAAE,wBAAwB;IAChCC,WAAW,EAAE,4FAA4F;IACzGC,UAAU,EAAE,cAAc;IAC1BC,QAAQ,EAAE,iBAAiB;IAC3BG,UAAU,EAAE,cAAc;IAC1BC,SAAS,EAAE,gBAAgB;IAC3BC,UAAU,EAAE;EACd,CAAC,EACD;IACEV,EAAE,EAAE,IAAI;IACRC,KAAK,EAAE,oCAAoC;IAC3CC,MAAM,EAAE,iBAAiB;IACzBC,WAAW,EAAE,kFAAkF;IAC/FC,UAAU,EAAE,UAAU;IACtBC,QAAQ,EAAE,cAAc;IACxBG,UAAU,EAAE,cAAc;IAC1BC,SAAS,EAAE,gBAAgB;IAC3BC,UAAU,EAAE;EACd,CAAC,EACD;IACEV,EAAE,EAAE,IAAI;IACRC,KAAK,EAAE,8BAA8B;IACrCC,MAAM,EAAE,oBAAoB;IAC5BC,WAAW,EAAE,wEAAwE;IACrFC,UAAU,EAAE,UAAU;IACtBC,QAAQ,EAAE,UAAU;IACpBG,UAAU,EAAE,cAAc;IAC1BC,SAAS,EAAE,gBAAgB;IAC3BC,UAAU,EAAE;EACd,CAAC,CACF;EAED5C,SAAS,CAAC,MAAM;IACd,IAAI6C,OAAO,GAAGZ,QAAQ;IACtB,IAAIpB,WAAW,EAAE;MACf,MAAMiC,KAAK,GAAGjC,WAAW,CAACkC,WAAW,CAAC,CAAC;MACvCF,OAAO,GAAGA,OAAO,CAACG,MAAM,CAACC,IAAI,IAC3BA,IAAI,CAACd,KAAK,CAACY,WAAW,CAAC,CAAC,CAACG,QAAQ,CAACJ,KAAK,CAAC,IACxCG,IAAI,CAACb,MAAM,CAACW,WAAW,CAAC,CAAC,CAACG,QAAQ,CAACJ,KAAK,CAAC,IACzCG,IAAI,CAACZ,WAAW,CAACU,WAAW,CAAC,CAAC,CAACG,QAAQ,CAACJ,KAAK,CAC/C,CAAC;IACH;IACA,IAAIrC,gBAAgB,KAAK,KAAK,EAAE;MAC9BoC,OAAO,GAAGA,OAAO,CAACG,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACV,QAAQ,KAAK9B,gBAAgB,CAAC;IACtE;IACA,IAAIE,kBAAkB,KAAK,KAAK,EAAE;MAChCkC,OAAO,GAAGA,OAAO,CAACG,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACX,UAAU,KAAK3B,kBAAkB,CAAC;IAC1E;IACAS,gBAAgB,CAACyB,OAAO,CAAC;EAC3B,CAAC,EAAE,CAAChC,WAAW,EAAEJ,gBAAgB,EAAEE,kBAAkB,CAAC,CAAC;EAEvD,MAAMwC,SAAS,GAAGA,CAAC;IAAEF;EAAK,CAAC,KAAK;IAC9B,oBACE/C,OAAA;MACEkD,SAAS,EAAE,sBAAsBH,IAAI,CAACP,UAAU,iHAAkH;MAClKW,YAAY,EAAEA,CAAA,KAAM/B,cAAc,CAAC2B,IAAI,CAAE;MACzCK,YAAY,EAAEA,CAAA,KAAMhC,cAAc,CAAC,IAAI,CAAE;MACzCiC,OAAO,EAAEA,CAAA,KAAM;QACbrC,eAAe,CAAC+B,IAAI,CAAC;QACrBjC,kBAAkB,CAAC,IAAI,CAAC;MAC1B,CAAE;MAAAwC,QAAA,eAEFtD,OAAA;QAAKkD,SAAS,EAAC,mDAAmD;QAAAI,QAAA,eAChEtD,OAAA;UAAKkD,SAAS,EAAC,kBAAkB;UAAAI,QAAA,gBAE/BtD,OAAA;YAAKkD,SAAS,EAAC;UAAqF;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC3G1D,OAAA;YAAKkD,SAAS,EAAC;UAAwF;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC9G1D,OAAA;YAAKkD,SAAS,EAAC;UAAiF;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACvG1D,OAAA;YAAKkD,SAAS,EAAC;UAAkF;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAGxG1D,OAAA;YAAKkD,SAAS,EAAC,mDAAmD;YAAAI,QAAA,eAChEtD,OAAA;cAAMkD,SAAS,EAAE,GAAGH,IAAI,CAACN,SAAS,0KAA2K;cAAAa,QAAA,EAC1MP,IAAI,CAACd;YAAK;cAAAsB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACP;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV,CAAC;EAED,oBACE1D,OAAA;IAAKkD,SAAS,EAAC,iCAAiC;IAAAI,QAAA,gBAC9CtD,OAAA;MAAQkD,SAAS,EAAC,4CAA4C;MAAAI,QAAA,eAC5DtD,OAAA;QAAKkD,SAAS,EAAC,qDAAqD;QAAAI,QAAA,gBAClEtD,OAAA;UAAIkD,SAAS,EAAC,mCAAmC;UAAAI,QAAA,EAAC;QAA0B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACjF1D,OAAA;UAAKkD,SAAS,EAAC,yBAAyB;UAAAI,QAAA,gBACtCtD,OAAA;YAAKkD,SAAS,EAAC,UAAU;YAAAI,QAAA,gBACvBtD,OAAA;cACE2D,IAAI,EAAC,MAAM;cACXC,WAAW,EAAC,iBAAiB;cAC7BV,SAAS,EAAC,6GAA6G;cACvHW,KAAK,EAAElD,WAAY;cACnBmD,QAAQ,EAAGC,CAAC,IAAKnD,cAAc,CAACmD,CAAC,CAACC,MAAM,CAACH,KAAK;YAAE;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjD,CAAC,eACF1D,OAAA;cAAGkD,SAAS,EAAC;YAAiF;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChG,CAAC,eACN1D,OAAA;YACEqD,OAAO,EAAEA,CAAA,KAAMY,MAAM,CAACC,IAAI,CAAC,8BAA8B,EAAE,QAAQ,CAAE;YACrEhB,SAAS,EAAC,mGAAmG;YAC7GjB,KAAK,EAAC,qDAAqD;YAAAqB,QAAA,gBAE3DtD,OAAA;cAAGkD,SAAS,EAAC;YAAoB;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,QAExC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT1D,OAAA;YAAQkD,SAAS,EAAC,sGAAsG;YAAAI,QAAA,gBACtHtD,OAAA;cAAGkD,SAAS,EAAC;YAAsB;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,gBAC5B,EAACvD,eAAe,CAACgE,MAAM,EAAC,GACtC;UAAA;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eAET1D,OAAA;MAAMkD,SAAS,EAAC,uBAAuB;MAAAI,QAAA,gBACrCtD,OAAA;QAAKkD,SAAS,EAAC,iBAAiB;QAAAI,QAAA,gBAC9BtD,OAAA;UAAKkD,SAAS,EAAC,QAAQ;UAAAI,QAAA,gBACrBtD,OAAA;YAAOkD,SAAS,EAAC,2BAA2B;YAAAI,QAAA,EAAC;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC7D1D,OAAA;YAAKkD,SAAS,EAAC,UAAU;YAAAI,QAAA,gBACvBtD,OAAA;cACEkD,SAAS,EAAC,wFAAwF;cAClGW,KAAK,EAAEtD,gBAAiB;cACxBuD,QAAQ,EAAGC,CAAC,IAAKvD,mBAAmB,CAACuD,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;cAAAP,QAAA,gBAErDtD,OAAA;gBAAQ6D,KAAK,EAAC,KAAK;gBAAAP,QAAA,EAAC;cAAc;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC3C1D,OAAA;gBAAQ6D,KAAK,EAAC,WAAW;gBAAAP,QAAA,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC5C1D,OAAA;gBAAQ6D,KAAK,EAAC,iBAAiB;gBAAAP,QAAA,EAAC;cAAe;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACxD1D,OAAA;gBAAQ6D,KAAK,EAAC,cAAc;gBAAAP,QAAA,EAAC;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAClD1D,OAAA;gBAAQ6D,KAAK,EAAC,UAAU;gBAAAP,QAAA,EAAC;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpC,CAAC,eACT1D,OAAA;cAAGkD,SAAS,EAAC;YAAwF;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACN1D,OAAA;UAAKkD,SAAS,EAAC,QAAQ;UAAAI,QAAA,gBACrBtD,OAAA;YAAOkD,SAAS,EAAC,2BAA2B;YAAAI,QAAA,EAAC;UAAU;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC/D1D,OAAA;YAAKkD,SAAS,EAAC,UAAU;YAAAI,QAAA,gBACvBtD,OAAA;cACEkD,SAAS,EAAC,wFAAwF;cAClGW,KAAK,EAAEpD,kBAAmB;cAC1BqD,QAAQ,EAAGC,CAAC,IAAKrD,qBAAqB,CAACqD,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;cAAAP,QAAA,gBAEvDtD,OAAA;gBAAQ6D,KAAK,EAAC,KAAK;gBAAAP,QAAA,EAAC;cAAU;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACvC1D,OAAA;gBAAQ6D,KAAK,EAAC,UAAU;gBAAAP,QAAA,EAAC;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC1C1D,OAAA;gBAAQ6D,KAAK,EAAC,cAAc;gBAAAP,QAAA,EAAC;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAClD1D,OAAA;gBAAQ6D,KAAK,EAAC,UAAU;gBAAAP,QAAA,EAAC;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpC,CAAC,eACT1D,OAAA;cAAGkD,SAAS,EAAC;YAAwF;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGN1D,OAAA;QAAKkD,SAAS,EAAC,kGAAkG;QAAAI,QAAA,eAC/GtD,OAAA;UAAKkD,SAAS,EAAC,mCAAmC;UAAAI,QAAA,gBAChDtD,OAAA;YAAAsD,QAAA,gBACEtD,OAAA;cAAIkD,SAAS,EAAC,uCAAuC;cAAAI,QAAA,gBACnDtD,OAAA;gBAAGkD,SAAS,EAAC;cAAoB;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,yBAExC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACL1D,OAAA;cAAGkD,SAAS,EAAC,oBAAoB;cAAAI,QAAA,EAAC;YAGlC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACJ1D,OAAA;cAAKkD,SAAS,EAAC,YAAY;cAAAI,QAAA,gBACzBtD,OAAA;gBACEqD,OAAO,EAAEA,CAAA,KAAMY,MAAM,CAACC,IAAI,CAAC,8BAA8B,EAAE,QAAQ,CAAE;gBACrEhB,SAAS,EAAC,kGAAkG;gBAAAI,QAAA,gBAE5GtD,OAAA;kBAAGkD,SAAS,EAAC;gBAA+B;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAEnD;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACT1D,OAAA;gBACEqD,OAAO,EAAEA,CAAA,KAAMY,MAAM,CAACC,IAAI,CAAC,0CAA0C,EAAE,QAAQ,CAAE;gBACjFhB,SAAS,EAAC,qGAAqG;gBAAAI,QAAA,gBAE/GtD,OAAA;kBAAGkD,SAAS,EAAC;gBAAkB;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,mBAEtC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACN1D,OAAA;YAAKkD,SAAS,EAAC,iBAAiB;YAAAI,QAAA,eAC9BtD,OAAA;cAAGkD,SAAS,EAAC;YAAoD;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN1D,OAAA;QAAKkD,SAAS,EAAC,kXAAkX;QAAAI,QAAA,eAC/XtD,OAAA;UAAKkD,SAAS,EAAC,sBAAsB;UAAAI,QAAA,EAClCvB,QAAQ,CAACqC,GAAG,CAACrB,IAAI,iBAChB/C,OAAA,CAACiD,SAAS;YAAeF,IAAI,EAAEA;UAAK,GAApBA,IAAI,CAACf,EAAE;YAAAuB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAe,CACvC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAELvC,WAAW,iBACVnB,OAAA;QACEkD,SAAS,EAAC,gJAAgJ;QAC1JG,OAAO,EAAGU,CAAC,IAAKA,CAAC,CAACM,eAAe,CAAC,CAAE;QAAAf,QAAA,eAEpCtD,OAAA;UAAKkD,SAAS,EAAC,YAAY;UAAAI,QAAA,gBACzBtD,OAAA;YAAKsE,GAAG,EAAEnD,WAAW,CAACuB,UAAW;YAAC6B,GAAG,EAAEpD,WAAW,CAACc,KAAM;YAACiB,SAAS,EAAC;UAA6C;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACpH1D,OAAA;YAAAsD,QAAA,gBACEtD,OAAA;cAAIkD,SAAS,EAAC,uCAAuC;cAAAI,QAAA,EAAEnC,WAAW,CAACc;YAAK;cAAAsB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC9E1D,OAAA;cAAGkD,SAAS,EAAC,6BAA6B;cAAAI,QAAA,GAAC,KAAG,EAACnC,WAAW,CAACe,MAAM;YAAA;cAAAqB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACtE1D,OAAA;cAAGkD,SAAS,EAAC,6BAA6B;cAAAI,QAAA,EAAEnC,WAAW,CAACgB;YAAW;cAAAoB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACxE1D,OAAA;cAAKkD,SAAS,EAAC,YAAY;cAAAI,QAAA,gBACzBtD,OAAA;gBAAQkD,SAAS,EAAC,8GAA8G;gBAAAI,QAAA,gBAC9HtD,OAAA;kBAAGkD,SAAS,EAAC;gBAAsB;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,YAE1C;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACT1D,OAAA;gBACEqD,OAAO,EAAEA,CAAA,KAAM;kBACb,MAAMmB,OAAO,GAAGlD,QAAQ,CAACI,aAAa,CAAC,KAAK,CAAC;kBAC7C8C,OAAO,CAACtB,SAAS,GAAG,mEAAmE;kBACvF,MAAMH,IAAI,GAAGzB,QAAQ,CAACI,aAAa,CAAC,KAAK,CAAC;kBAC1CqB,IAAI,CAACG,SAAS,GAAG,qEAAqE;kBACtFH,IAAI,CAACtB,KAAK,CAACgD,WAAW,GAAG,QAAQ;kBACjC,MAAMC,KAAK,GAAGpD,QAAQ,CAACI,aAAa,CAAC,KAAK,CAAC;kBAC3CgD,KAAK,CAACxB,SAAS,GAAG,2GAA2G;kBAC7HwB,KAAK,CAACjD,KAAK,CAACkD,eAAe,GAAG,OAAOxD,WAAW,CAACuB,UAAU,GAAG;kBAC9DgC,KAAK,CAACjD,KAAK,CAACmD,cAAc,GAAG,aAAa;kBAC1C7B,IAAI,CAAClB,WAAW,CAAC6C,KAAK,CAAC;kBACvBF,OAAO,CAAC3C,WAAW,CAACkB,IAAI,CAAC;kBACzBzB,QAAQ,CAACuD,IAAI,CAAChD,WAAW,CAAC2C,OAAO,CAAC;kBAClCM,UAAU,CAAC,MAAM;oBACfJ,KAAK,CAACjD,KAAK,CAACsD,SAAS,GAAG,kBAAkB;kBAC5C,CAAC,EAAE,GAAG,CAAC;kBACPD,UAAU,CAAC,MAAM;oBACfb,MAAM,CAACe,QAAQ,CAACC,IAAI,GAAG9D,WAAW,CAACmB,QAAQ;kBAC7C,CAAC,EAAE,IAAI,CAAC;gBACV,CAAE;gBACFY,SAAS,EAAC,8GAA8G;gBAAAI,QAAA,gBAExHtD,OAAA;kBAAGkD,SAAS,EAAC;gBAAyB;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,EAC1CvC,WAAW,CAACoB,QAAQ,GAAG,iBAAiB,GAAG,cAAc;cAAA;gBAAAgB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAACxD,EAAA,CAxVID,YAAY;AAAAiF,EAAA,GAAZjF,YAAY;AA0VlB,eAAeA,YAAY;AAAC,IAAAiF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}