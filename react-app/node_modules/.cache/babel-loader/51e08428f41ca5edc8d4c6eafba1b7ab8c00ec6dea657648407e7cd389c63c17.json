{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/FatbeamU/react-app/src/components/MoodleIntegration.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport moodleAPI from '../services/moodleAPI';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst MoodleIntegration = () => {\n  _s();\n  const [connectionStatus, setConnectionStatus] = useState('checking');\n  const [apiData, setApiData] = useState(null);\n  const [error, setError] = useState(null);\n  useEffect(() => {\n    checkMoodleConnection();\n  }, []);\n  const checkMoodleConnection = async () => {\n    try {\n      setConnectionStatus('checking');\n      const siteInfo = await moodleAPI.getSiteInfo();\n      setApiData(siteInfo);\n      setConnectionStatus('connected');\n      setError(null);\n    } catch (err) {\n      setConnectionStatus('error');\n      setError(err.message);\n    }\n  };\n  const testApiCall = async endpoint => {\n    try {\n      let result;\n      switch (endpoint) {\n        case 'courses':\n          result = await moodleAPI.getCourses();\n          break;\n        case 'users':\n          result = await moodleAPI.getCurrentUser();\n          break;\n        case 'site':\n          result = await moodleAPI.getSiteInfo();\n          break;\n        default:\n          result = {\n            error: 'Unknown endpoint'\n          };\n      }\n      alert(`API Test Result:\\n${JSON.stringify(result, null, 2)}`);\n    } catch (err) {\n      alert(`API Test Error:\\n${err.message}`);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-[#1a0f00] rounded-lg p-6 border border-gray-700\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"text-xl font-semibold mb-4\",\n        children: \"Moodle Connection Status\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 52,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: `w-4 h-4 rounded-full mr-3 ${connectionStatus === 'connected' ? 'bg-green-500' : connectionStatus === 'error' ? 'bg-red-500' : 'bg-yellow-500'}`\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 55,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"font-medium\",\n            children: connectionStatus === 'connected' ? 'Connected' : connectionStatus === 'error' ? 'Connection Error' : 'Checking...'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 59,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 54,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: checkMoodleConnection,\n          className: \"px-4 py-2 bg-[#15a7dd] rounded hover:bg-[#1397c7] transition-colors duration-300\",\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-sync-alt mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 68,\n            columnNumber: 13\n          }, this), \"Refresh\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 64,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 53,\n        columnNumber: 9\n      }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-4 p-3 bg-red-900/20 border border-red-500/30 rounded\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-red-400 text-sm\",\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 75,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 74,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 51,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-[#1a0f00] rounded-lg p-6 border border-gray-700\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"text-xl font-semibold mb-4\",\n        children: \"API Configuration\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 82,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"block text-sm font-medium mb-2\",\n            children: \"Moodle URL\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 85,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            value: process.env.REACT_APP_MOODLE_URL || '',\n            readOnly: true,\n            className: \"w-full px-3 py-2 bg-[#2a1a05] border border-gray-600 rounded text-gray-300\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 86,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 84,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"block text-sm font-medium mb-2\",\n            children: \"API Endpoint\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 94,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            value: process.env.REACT_APP_MOODLE_API_URL || '',\n            readOnly: true,\n            className: \"w-full px-3 py-2 bg-[#2a1a05] border border-gray-600 rounded text-gray-300\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 95,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 93,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"block text-sm font-medium mb-2\",\n            children: \"Token Status\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 103,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            value: process.env.REACT_APP_MOODLE_TOKEN ? 'Configured' : 'Not Set',\n            readOnly: true,\n            className: \"w-full px-3 py-2 bg-[#2a1a05] border border-gray-600 rounded text-gray-300\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 104,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 102,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"block text-sm font-medium mb-2\",\n            children: \"Connection Type\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 112,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            value: \"REST API\",\n            readOnly: true,\n            className: \"w-full px-3 py-2 bg-[#2a1a05] border border-gray-600 rounded text-gray-300\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 113,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 111,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 83,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 81,\n      columnNumber: 7\n    }, this), apiData && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-[#1a0f00] rounded-lg p-6 border border-gray-700\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"text-xl font-semibold mb-4\",\n        children: \"Moodle Site Information\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 126,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"block text-sm font-medium mb-2\",\n            children: \"Site Name\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 129,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-300\",\n            children: apiData.sitename || 'N/A'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 130,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 128,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"block text-sm font-medium mb-2\",\n            children: \"Version\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 133,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-300\",\n            children: apiData.release || 'N/A'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 134,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 132,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"block text-sm font-medium mb-2\",\n            children: \"Language\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 137,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-300\",\n            children: apiData.lang || 'N/A'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 138,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 136,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"block text-sm font-medium mb-2\",\n            children: \"User ID\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 141,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-300\",\n            children: apiData.userid || 'N/A'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 142,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 140,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 127,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 125,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-[#1a0f00] rounded-lg p-6 border border-gray-700\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"text-xl font-semibold mb-4\",\n        children: \"API Testing\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 150,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-gray-400 mb-4\",\n        children: \"Test various Moodle API endpoints to ensure proper integration.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 151,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => testApiCall('site'),\n          className: \"p-3 bg-blue-600 rounded hover:bg-blue-700 transition-colors duration-300\",\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-info-circle mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 157,\n            columnNumber: 13\n          }, this), \"Test Site Info\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 153,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => testApiCall('courses'),\n          className: \"p-3 bg-green-600 rounded hover:bg-green-700 transition-colors duration-300\",\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-book mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 164,\n            columnNumber: 13\n          }, this), \"Test Courses\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 160,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => testApiCall('users'),\n          className: \"p-3 bg-purple-600 rounded hover:bg-purple-700 transition-colors duration-300\",\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-user mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 171,\n            columnNumber: 13\n          }, this), \"Test User Info\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 167,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 152,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 149,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-[#1a0f00] rounded-lg p-6 border border-gray-700\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"text-xl font-semibold mb-4\",\n        children: \"Integration Features\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 179,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            className: \"font-semibold mb-2 text-[#15a7dd]\",\n            children: \"Implemented Features\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 182,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n            className: \"space-y-2 text-sm\",\n            children: [/*#__PURE__*/_jsxDEV(\"li\", {\n              className: \"flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-check text-green-500 mr-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 185,\n                columnNumber: 17\n              }, this), \"Single Sign-On (SSO) Integration\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 184,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              className: \"flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-check text-green-500 mr-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 189,\n                columnNumber: 17\n              }, this), \"Course Data Synchronization\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 188,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              className: \"flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-check text-green-500 mr-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 193,\n                columnNumber: 17\n              }, this), \"User Progress Tracking\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 192,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              className: \"flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-check text-green-500 mr-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 197,\n                columnNumber: 17\n              }, this), \"Unified Navigation\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 196,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 183,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 181,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            className: \"font-semibold mb-2 text-yellow-500\",\n            children: \"Planned Features\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 203,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n            className: \"space-y-2 text-sm\",\n            children: [/*#__PURE__*/_jsxDEV(\"li\", {\n              className: \"flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-clock text-yellow-500 mr-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 206,\n                columnNumber: 17\n              }, this), \"Real-time Notifications\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 205,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              className: \"flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-clock text-yellow-500 mr-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 210,\n                columnNumber: 17\n              }, this), \"Grade Synchronization\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 209,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              className: \"flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-clock text-yellow-500 mr-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 214,\n                columnNumber: 17\n              }, this), \"Assignment Submission\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 213,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              className: \"flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-clock text-yellow-500 mr-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 218,\n                columnNumber: 17\n              }, this), \"Discussion Forum Integration\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 217,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 204,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 202,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 180,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 178,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-[#1a0f00] rounded-lg p-6 border border-gray-700\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"text-xl font-semibold mb-4\",\n        children: \"Documentation & Resources\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 228,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"a\", {\n          href: \"https://docs.moodle.org/dev/Web_service_API_functions\",\n          target: \"_blank\",\n          rel: \"noopener noreferrer\",\n          className: \"flex items-center p-3 bg-[#2a1a05] rounded hover:bg-[#3a2a15] transition-colors duration-300\",\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-external-link-alt mr-3 text-[#15a7dd]\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 236,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              className: \"font-semibold\",\n              children: \"Moodle Web Services API\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 238,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-gray-400\",\n              children: \"Official API documentation\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 239,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 237,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 230,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n          href: \"https://docs.moodle.org/dev/Creating_a_web_service_client\",\n          target: \"_blank\",\n          rel: \"noopener noreferrer\",\n          className: \"flex items-center p-3 bg-[#2a1a05] rounded hover:bg-[#3a2a15] transition-colors duration-300\",\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-external-link-alt mr-3 text-[#15a7dd]\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 248,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              className: \"font-semibold\",\n              children: \"Web Service Client Guide\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 250,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-gray-400\",\n              children: \"Integration best practices\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 251,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 249,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 242,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 229,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 227,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 49,\n    columnNumber: 5\n  }, this);\n};\n_s(MoodleIntegration, \"t5ikklbWy2x9E7gAoRk0U6USghQ=\");\n_c = MoodleIntegration;\nexport default MoodleIntegration;\nvar _c;\n$RefreshReg$(_c, \"MoodleIntegration\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "moodleAPI", "jsxDEV", "_jsxDEV", "MoodleIntegration", "_s", "connectionStatus", "setConnectionStatus", "apiData", "setApiData", "error", "setError", "checkMoodleConnection", "siteInfo", "getSiteInfo", "err", "message", "testApiCall", "endpoint", "result", "getCourses", "getCurrentUser", "alert", "JSON", "stringify", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "type", "value", "process", "env", "REACT_APP_MOODLE_URL", "readOnly", "REACT_APP_MOODLE_API_URL", "REACT_APP_MOODLE_TOKEN", "sitename", "release", "lang", "userid", "href", "target", "rel", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/FatbeamU/react-app/src/components/MoodleIntegration.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport moodle<PERSON><PERSON> from '../services/moodleAPI';\n\nconst MoodleIntegration = () => {\n  const [connectionStatus, setConnectionStatus] = useState('checking');\n  const [apiData, setApiData] = useState(null);\n  const [error, setError] = useState(null);\n\n  useEffect(() => {\n    checkMoodleConnection();\n  }, []);\n\n  const checkMoodleConnection = async () => {\n    try {\n      setConnectionStatus('checking');\n      const siteInfo = await moodleAPI.getSiteInfo();\n      setApiData(siteInfo);\n      setConnectionStatus('connected');\n      setError(null);\n    } catch (err) {\n      setConnectionStatus('error');\n      setError(err.message);\n    }\n  };\n\n  const testApiCall = async (endpoint) => {\n    try {\n      let result;\n      switch (endpoint) {\n        case 'courses':\n          result = await moodleAPI.getCourses();\n          break;\n        case 'users':\n          result = await moodleAPI.getCurrentUser();\n          break;\n        case 'site':\n          result = await moodleAPI.getSiteInfo();\n          break;\n        default:\n          result = { error: 'Unknown endpoint' };\n      }\n      alert(`API Test Result:\\n${JSON.stringify(result, null, 2)}`);\n    } catch (err) {\n      alert(`API Test Error:\\n${err.message}`);\n    }\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Connection Status */}\n      <div className=\"bg-[#1a0f00] rounded-lg p-6 border border-gray-700\">\n        <h3 className=\"text-xl font-semibold mb-4\">Moodle Connection Status</h3>\n        <div className=\"flex items-center justify-between\">\n          <div className=\"flex items-center\">\n            <div className={`w-4 h-4 rounded-full mr-3 ${\n              connectionStatus === 'connected' ? 'bg-green-500' :\n              connectionStatus === 'error' ? 'bg-red-500' : 'bg-yellow-500'\n            }`}></div>\n            <span className=\"font-medium\">\n              {connectionStatus === 'connected' ? 'Connected' :\n               connectionStatus === 'error' ? 'Connection Error' : 'Checking...'}\n            </span>\n          </div>\n          <button\n            onClick={checkMoodleConnection}\n            className=\"px-4 py-2 bg-[#15a7dd] rounded hover:bg-[#1397c7] transition-colors duration-300\"\n          >\n            <i className=\"fas fa-sync-alt mr-2\"></i>\n            Refresh\n          </button>\n        </div>\n        \n        {error && (\n          <div className=\"mt-4 p-3 bg-red-900/20 border border-red-500/30 rounded\">\n            <p className=\"text-red-400 text-sm\">{error}</p>\n          </div>\n        )}\n      </div>\n\n      {/* API Configuration */}\n      <div className=\"bg-[#1a0f00] rounded-lg p-6 border border-gray-700\">\n        <h3 className=\"text-xl font-semibold mb-4\">API Configuration</h3>\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n          <div>\n            <label className=\"block text-sm font-medium mb-2\">Moodle URL</label>\n            <input\n              type=\"text\"\n              value={process.env.REACT_APP_MOODLE_URL || ''}\n              readOnly\n              className=\"w-full px-3 py-2 bg-[#2a1a05] border border-gray-600 rounded text-gray-300\"\n            />\n          </div>\n          <div>\n            <label className=\"block text-sm font-medium mb-2\">API Endpoint</label>\n            <input\n              type=\"text\"\n              value={process.env.REACT_APP_MOODLE_API_URL || ''}\n              readOnly\n              className=\"w-full px-3 py-2 bg-[#2a1a05] border border-gray-600 rounded text-gray-300\"\n            />\n          </div>\n          <div>\n            <label className=\"block text-sm font-medium mb-2\">Token Status</label>\n            <input\n              type=\"text\"\n              value={process.env.REACT_APP_MOODLE_TOKEN ? 'Configured' : 'Not Set'}\n              readOnly\n              className=\"w-full px-3 py-2 bg-[#2a1a05] border border-gray-600 rounded text-gray-300\"\n            />\n          </div>\n          <div>\n            <label className=\"block text-sm font-medium mb-2\">Connection Type</label>\n            <input\n              type=\"text\"\n              value=\"REST API\"\n              readOnly\n              className=\"w-full px-3 py-2 bg-[#2a1a05] border border-gray-600 rounded text-gray-300\"\n            />\n          </div>\n        </div>\n      </div>\n\n      {/* Site Information */}\n      {apiData && (\n        <div className=\"bg-[#1a0f00] rounded-lg p-6 border border-gray-700\">\n          <h3 className=\"text-xl font-semibold mb-4\">Moodle Site Information</h3>\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n            <div>\n              <label className=\"block text-sm font-medium mb-2\">Site Name</label>\n              <p className=\"text-gray-300\">{apiData.sitename || 'N/A'}</p>\n            </div>\n            <div>\n              <label className=\"block text-sm font-medium mb-2\">Version</label>\n              <p className=\"text-gray-300\">{apiData.release || 'N/A'}</p>\n            </div>\n            <div>\n              <label className=\"block text-sm font-medium mb-2\">Language</label>\n              <p className=\"text-gray-300\">{apiData.lang || 'N/A'}</p>\n            </div>\n            <div>\n              <label className=\"block text-sm font-medium mb-2\">User ID</label>\n              <p className=\"text-gray-300\">{apiData.userid || 'N/A'}</p>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* API Testing */}\n      <div className=\"bg-[#1a0f00] rounded-lg p-6 border border-gray-700\">\n        <h3 className=\"text-xl font-semibold mb-4\">API Testing</h3>\n        <p className=\"text-gray-400 mb-4\">Test various Moodle API endpoints to ensure proper integration.</p>\n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n          <button\n            onClick={() => testApiCall('site')}\n            className=\"p-3 bg-blue-600 rounded hover:bg-blue-700 transition-colors duration-300\"\n          >\n            <i className=\"fas fa-info-circle mr-2\"></i>\n            Test Site Info\n          </button>\n          <button\n            onClick={() => testApiCall('courses')}\n            className=\"p-3 bg-green-600 rounded hover:bg-green-700 transition-colors duration-300\"\n          >\n            <i className=\"fas fa-book mr-2\"></i>\n            Test Courses\n          </button>\n          <button\n            onClick={() => testApiCall('users')}\n            className=\"p-3 bg-purple-600 rounded hover:bg-purple-700 transition-colors duration-300\"\n          >\n            <i className=\"fas fa-user mr-2\"></i>\n            Test User Info\n          </button>\n        </div>\n      </div>\n\n      {/* Integration Features */}\n      <div className=\"bg-[#1a0f00] rounded-lg p-6 border border-gray-700\">\n        <h3 className=\"text-xl font-semibold mb-4\">Integration Features</h3>\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n          <div>\n            <h4 className=\"font-semibold mb-2 text-[#15a7dd]\">Implemented Features</h4>\n            <ul className=\"space-y-2 text-sm\">\n              <li className=\"flex items-center\">\n                <i className=\"fas fa-check text-green-500 mr-2\"></i>\n                Single Sign-On (SSO) Integration\n              </li>\n              <li className=\"flex items-center\">\n                <i className=\"fas fa-check text-green-500 mr-2\"></i>\n                Course Data Synchronization\n              </li>\n              <li className=\"flex items-center\">\n                <i className=\"fas fa-check text-green-500 mr-2\"></i>\n                User Progress Tracking\n              </li>\n              <li className=\"flex items-center\">\n                <i className=\"fas fa-check text-green-500 mr-2\"></i>\n                Unified Navigation\n              </li>\n            </ul>\n          </div>\n          <div>\n            <h4 className=\"font-semibold mb-2 text-yellow-500\">Planned Features</h4>\n            <ul className=\"space-y-2 text-sm\">\n              <li className=\"flex items-center\">\n                <i className=\"fas fa-clock text-yellow-500 mr-2\"></i>\n                Real-time Notifications\n              </li>\n              <li className=\"flex items-center\">\n                <i className=\"fas fa-clock text-yellow-500 mr-2\"></i>\n                Grade Synchronization\n              </li>\n              <li className=\"flex items-center\">\n                <i className=\"fas fa-clock text-yellow-500 mr-2\"></i>\n                Assignment Submission\n              </li>\n              <li className=\"flex items-center\">\n                <i className=\"fas fa-clock text-yellow-500 mr-2\"></i>\n                Discussion Forum Integration\n              </li>\n            </ul>\n          </div>\n        </div>\n      </div>\n\n      {/* Documentation Links */}\n      <div className=\"bg-[#1a0f00] rounded-lg p-6 border border-gray-700\">\n        <h3 className=\"text-xl font-semibold mb-4\">Documentation & Resources</h3>\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n          <a\n            href=\"https://docs.moodle.org/dev/Web_service_API_functions\"\n            target=\"_blank\"\n            rel=\"noopener noreferrer\"\n            className=\"flex items-center p-3 bg-[#2a1a05] rounded hover:bg-[#3a2a15] transition-colors duration-300\"\n          >\n            <i className=\"fas fa-external-link-alt mr-3 text-[#15a7dd]\"></i>\n            <div>\n              <h4 className=\"font-semibold\">Moodle Web Services API</h4>\n              <p className=\"text-sm text-gray-400\">Official API documentation</p>\n            </div>\n          </a>\n          <a\n            href=\"https://docs.moodle.org/dev/Creating_a_web_service_client\"\n            target=\"_blank\"\n            rel=\"noopener noreferrer\"\n            className=\"flex items-center p-3 bg-[#2a1a05] rounded hover:bg-[#3a2a15] transition-colors duration-300\"\n          >\n            <i className=\"fas fa-external-link-alt mr-3 text-[#15a7dd]\"></i>\n            <div>\n              <h4 className=\"font-semibold\">Web Service Client Guide</h4>\n              <p className=\"text-sm text-gray-400\">Integration best practices</p>\n            </div>\n          </a>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default MoodleIntegration;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,SAAS,MAAM,uBAAuB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9C,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC9B,MAAM,CAACC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGR,QAAQ,CAAC,UAAU,CAAC;EACpE,MAAM,CAACS,OAAO,EAAEC,UAAU,CAAC,GAAGV,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACW,KAAK,EAAEC,QAAQ,CAAC,GAAGZ,QAAQ,CAAC,IAAI,CAAC;EAExCC,SAAS,CAAC,MAAM;IACdY,qBAAqB,CAAC,CAAC;EACzB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,qBAAqB,GAAG,MAAAA,CAAA,KAAY;IACxC,IAAI;MACFL,mBAAmB,CAAC,UAAU,CAAC;MAC/B,MAAMM,QAAQ,GAAG,MAAMZ,SAAS,CAACa,WAAW,CAAC,CAAC;MAC9CL,UAAU,CAACI,QAAQ,CAAC;MACpBN,mBAAmB,CAAC,WAAW,CAAC;MAChCI,QAAQ,CAAC,IAAI,CAAC;IAChB,CAAC,CAAC,OAAOI,GAAG,EAAE;MACZR,mBAAmB,CAAC,OAAO,CAAC;MAC5BI,QAAQ,CAACI,GAAG,CAACC,OAAO,CAAC;IACvB;EACF,CAAC;EAED,MAAMC,WAAW,GAAG,MAAOC,QAAQ,IAAK;IACtC,IAAI;MACF,IAAIC,MAAM;MACV,QAAQD,QAAQ;QACd,KAAK,SAAS;UACZC,MAAM,GAAG,MAAMlB,SAAS,CAACmB,UAAU,CAAC,CAAC;UACrC;QACF,KAAK,OAAO;UACVD,MAAM,GAAG,MAAMlB,SAAS,CAACoB,cAAc,CAAC,CAAC;UACzC;QACF,KAAK,MAAM;UACTF,MAAM,GAAG,MAAMlB,SAAS,CAACa,WAAW,CAAC,CAAC;UACtC;QACF;UACEK,MAAM,GAAG;YAAET,KAAK,EAAE;UAAmB,CAAC;MAC1C;MACAY,KAAK,CAAC,qBAAqBC,IAAI,CAACC,SAAS,CAACL,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC;IAC/D,CAAC,CAAC,OAAOJ,GAAG,EAAE;MACZO,KAAK,CAAC,oBAAoBP,GAAG,CAACC,OAAO,EAAE,CAAC;IAC1C;EACF,CAAC;EAED,oBACEb,OAAA;IAAKsB,SAAS,EAAC,WAAW;IAAAC,QAAA,gBAExBvB,OAAA;MAAKsB,SAAS,EAAC,oDAAoD;MAAAC,QAAA,gBACjEvB,OAAA;QAAIsB,SAAS,EAAC,4BAA4B;QAAAC,QAAA,EAAC;MAAwB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACxE3B,OAAA;QAAKsB,SAAS,EAAC,mCAAmC;QAAAC,QAAA,gBAChDvB,OAAA;UAAKsB,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChCvB,OAAA;YAAKsB,SAAS,EAAE,6BACdnB,gBAAgB,KAAK,WAAW,GAAG,cAAc,GACjDA,gBAAgB,KAAK,OAAO,GAAG,YAAY,GAAG,eAAe;UAC5D;YAAAqB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACV3B,OAAA;YAAMsB,SAAS,EAAC,aAAa;YAAAC,QAAA,EAC1BpB,gBAAgB,KAAK,WAAW,GAAG,WAAW,GAC9CA,gBAAgB,KAAK,OAAO,GAAG,kBAAkB,GAAG;UAAa;YAAAqB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9D,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eACN3B,OAAA;UACE4B,OAAO,EAAEnB,qBAAsB;UAC/Ba,SAAS,EAAC,kFAAkF;UAAAC,QAAA,gBAE5FvB,OAAA;YAAGsB,SAAS,EAAC;UAAsB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,WAE1C;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,EAELpB,KAAK,iBACJP,OAAA;QAAKsB,SAAS,EAAC,yDAAyD;QAAAC,QAAA,eACtEvB,OAAA;UAAGsB,SAAS,EAAC,sBAAsB;UAAAC,QAAA,EAAEhB;QAAK;UAAAiB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5C,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGN3B,OAAA;MAAKsB,SAAS,EAAC,oDAAoD;MAAAC,QAAA,gBACjEvB,OAAA;QAAIsB,SAAS,EAAC,4BAA4B;QAAAC,QAAA,EAAC;MAAiB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACjE3B,OAAA;QAAKsB,SAAS,EAAC,uCAAuC;QAAAC,QAAA,gBACpDvB,OAAA;UAAAuB,QAAA,gBACEvB,OAAA;YAAOsB,SAAS,EAAC,gCAAgC;YAAAC,QAAA,EAAC;UAAU;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACpE3B,OAAA;YACE6B,IAAI,EAAC,MAAM;YACXC,KAAK,EAAEC,OAAO,CAACC,GAAG,CAACC,oBAAoB,IAAI,EAAG;YAC9CC,QAAQ;YACRZ,SAAS,EAAC;UAA4E;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACN3B,OAAA;UAAAuB,QAAA,gBACEvB,OAAA;YAAOsB,SAAS,EAAC,gCAAgC;YAAAC,QAAA,EAAC;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACtE3B,OAAA;YACE6B,IAAI,EAAC,MAAM;YACXC,KAAK,EAAEC,OAAO,CAACC,GAAG,CAACG,wBAAwB,IAAI,EAAG;YAClDD,QAAQ;YACRZ,SAAS,EAAC;UAA4E;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACN3B,OAAA;UAAAuB,QAAA,gBACEvB,OAAA;YAAOsB,SAAS,EAAC,gCAAgC;YAAAC,QAAA,EAAC;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACtE3B,OAAA;YACE6B,IAAI,EAAC,MAAM;YACXC,KAAK,EAAEC,OAAO,CAACC,GAAG,CAACI,sBAAsB,GAAG,YAAY,GAAG,SAAU;YACrEF,QAAQ;YACRZ,SAAS,EAAC;UAA4E;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACN3B,OAAA;UAAAuB,QAAA,gBACEvB,OAAA;YAAOsB,SAAS,EAAC,gCAAgC;YAAAC,QAAA,EAAC;UAAe;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACzE3B,OAAA;YACE6B,IAAI,EAAC,MAAM;YACXC,KAAK,EAAC,UAAU;YAChBI,QAAQ;YACRZ,SAAS,EAAC;UAA4E;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGLtB,OAAO,iBACNL,OAAA;MAAKsB,SAAS,EAAC,oDAAoD;MAAAC,QAAA,gBACjEvB,OAAA;QAAIsB,SAAS,EAAC,4BAA4B;QAAAC,QAAA,EAAC;MAAuB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACvE3B,OAAA;QAAKsB,SAAS,EAAC,uCAAuC;QAAAC,QAAA,gBACpDvB,OAAA;UAAAuB,QAAA,gBACEvB,OAAA;YAAOsB,SAAS,EAAC,gCAAgC;YAAAC,QAAA,EAAC;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACnE3B,OAAA;YAAGsB,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAElB,OAAO,CAACgC,QAAQ,IAAI;UAAK;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzD,CAAC,eACN3B,OAAA;UAAAuB,QAAA,gBACEvB,OAAA;YAAOsB,SAAS,EAAC,gCAAgC;YAAAC,QAAA,EAAC;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACjE3B,OAAA;YAAGsB,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAElB,OAAO,CAACiC,OAAO,IAAI;UAAK;YAAAd,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxD,CAAC,eACN3B,OAAA;UAAAuB,QAAA,gBACEvB,OAAA;YAAOsB,SAAS,EAAC,gCAAgC;YAAAC,QAAA,EAAC;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAClE3B,OAAA;YAAGsB,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAElB,OAAO,CAACkC,IAAI,IAAI;UAAK;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrD,CAAC,eACN3B,OAAA;UAAAuB,QAAA,gBACEvB,OAAA;YAAOsB,SAAS,EAAC,gCAAgC;YAAAC,QAAA,EAAC;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACjE3B,OAAA;YAAGsB,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAElB,OAAO,CAACmC,MAAM,IAAI;UAAK;YAAAhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAGD3B,OAAA;MAAKsB,SAAS,EAAC,oDAAoD;MAAAC,QAAA,gBACjEvB,OAAA;QAAIsB,SAAS,EAAC,4BAA4B;QAAAC,QAAA,EAAC;MAAW;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC3D3B,OAAA;QAAGsB,SAAS,EAAC,oBAAoB;QAAAC,QAAA,EAAC;MAA+D;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eACrG3B,OAAA;QAAKsB,SAAS,EAAC,uCAAuC;QAAAC,QAAA,gBACpDvB,OAAA;UACE4B,OAAO,EAAEA,CAAA,KAAMd,WAAW,CAAC,MAAM,CAAE;UACnCQ,SAAS,EAAC,0EAA0E;UAAAC,QAAA,gBAEpFvB,OAAA;YAAGsB,SAAS,EAAC;UAAyB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,kBAE7C;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT3B,OAAA;UACE4B,OAAO,EAAEA,CAAA,KAAMd,WAAW,CAAC,SAAS,CAAE;UACtCQ,SAAS,EAAC,4EAA4E;UAAAC,QAAA,gBAEtFvB,OAAA;YAAGsB,SAAS,EAAC;UAAkB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,gBAEtC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT3B,OAAA;UACE4B,OAAO,EAAEA,CAAA,KAAMd,WAAW,CAAC,OAAO,CAAE;UACpCQ,SAAS,EAAC,8EAA8E;UAAAC,QAAA,gBAExFvB,OAAA;YAAGsB,SAAS,EAAC;UAAkB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,kBAEtC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN3B,OAAA;MAAKsB,SAAS,EAAC,oDAAoD;MAAAC,QAAA,gBACjEvB,OAAA;QAAIsB,SAAS,EAAC,4BAA4B;QAAAC,QAAA,EAAC;MAAoB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACpE3B,OAAA;QAAKsB,SAAS,EAAC,uCAAuC;QAAAC,QAAA,gBACpDvB,OAAA;UAAAuB,QAAA,gBACEvB,OAAA;YAAIsB,SAAS,EAAC,mCAAmC;YAAAC,QAAA,EAAC;UAAoB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC3E3B,OAAA;YAAIsB,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAC/BvB,OAAA;cAAIsB,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAC/BvB,OAAA;gBAAGsB,SAAS,EAAC;cAAkC;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,oCAEtD;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACL3B,OAAA;cAAIsB,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAC/BvB,OAAA;gBAAGsB,SAAS,EAAC;cAAkC;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,+BAEtD;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACL3B,OAAA;cAAIsB,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAC/BvB,OAAA;gBAAGsB,SAAS,EAAC;cAAkC;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,0BAEtD;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACL3B,OAAA;cAAIsB,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAC/BvB,OAAA;gBAAGsB,SAAS,EAAC;cAAkC;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,sBAEtD;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACN3B,OAAA;UAAAuB,QAAA,gBACEvB,OAAA;YAAIsB,SAAS,EAAC,oCAAoC;YAAAC,QAAA,EAAC;UAAgB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACxE3B,OAAA;YAAIsB,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAC/BvB,OAAA;cAAIsB,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAC/BvB,OAAA;gBAAGsB,SAAS,EAAC;cAAmC;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,2BAEvD;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACL3B,OAAA;cAAIsB,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAC/BvB,OAAA;gBAAGsB,SAAS,EAAC;cAAmC;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,yBAEvD;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACL3B,OAAA;cAAIsB,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAC/BvB,OAAA;gBAAGsB,SAAS,EAAC;cAAmC;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,yBAEvD;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACL3B,OAAA;cAAIsB,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAC/BvB,OAAA;gBAAGsB,SAAS,EAAC;cAAmC;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,gCAEvD;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN3B,OAAA;MAAKsB,SAAS,EAAC,oDAAoD;MAAAC,QAAA,gBACjEvB,OAAA;QAAIsB,SAAS,EAAC,4BAA4B;QAAAC,QAAA,EAAC;MAAyB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACzE3B,OAAA;QAAKsB,SAAS,EAAC,uCAAuC;QAAAC,QAAA,gBACpDvB,OAAA;UACEyC,IAAI,EAAC,uDAAuD;UAC5DC,MAAM,EAAC,QAAQ;UACfC,GAAG,EAAC,qBAAqB;UACzBrB,SAAS,EAAC,8FAA8F;UAAAC,QAAA,gBAExGvB,OAAA;YAAGsB,SAAS,EAAC;UAA8C;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAChE3B,OAAA;YAAAuB,QAAA,gBACEvB,OAAA;cAAIsB,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAuB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC1D3B,OAAA;cAAGsB,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAA0B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACJ3B,OAAA;UACEyC,IAAI,EAAC,2DAA2D;UAChEC,MAAM,EAAC,QAAQ;UACfC,GAAG,EAAC,qBAAqB;UACzBrB,SAAS,EAAC,8FAA8F;UAAAC,QAAA,gBAExGvB,OAAA;YAAGsB,SAAS,EAAC;UAA8C;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAChE3B,OAAA;YAAAuB,QAAA,gBACEvB,OAAA;cAAIsB,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAwB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC3D3B,OAAA;cAAGsB,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAA0B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACzB,EAAA,CA9PID,iBAAiB;AAAA2C,EAAA,GAAjB3C,iBAAiB;AAgQvB,eAAeA,iBAAiB;AAAC,IAAA2C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}