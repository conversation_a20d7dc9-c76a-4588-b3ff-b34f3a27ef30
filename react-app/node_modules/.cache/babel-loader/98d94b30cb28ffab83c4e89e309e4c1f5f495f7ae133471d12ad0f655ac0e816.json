{"ast": null, "code": "import React,{useState,useEffect}from'react';import{<PERSON>}from'react-router-dom';import MoodleIntegration from'./MoodleIntegration';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const UnifiedDashboard=_ref=>{var _moodleData$courses,_moodleData$notificat,_moodleData$recentAct,_moodleData$notificat2,_moodleData$courses2;let{user}=_ref;const[moodleData,setMoodleData]=useState(null);const[loading,setLoading]=useState(true);const[activeSection,setActiveSection]=useState('overview');useEffect(()=>{// Fetch Moodle data when component mounts\nconst fetchMoodleData=async()=>{try{setLoading(true);// This will be implemented with actual Moodle API calls\n// For now, using mock data\nconst mockData={courses:[{id:1,name:'Fundamentals of Magical Networking',progress:85},{id:2,name:'Fiber Optic Enchantments',progress:60},{id:3,name:'Network Security Wards',progress:30}],recentActivity:[{type:'course_completion',course:'Basic Network Spells',timestamp:'2024-01-15'},{type:'assignment_submission',course:'Fiber Optics 101',timestamp:'2024-01-14'}],notifications:[{type:'assignment_due',message:'Assignment due in 3 days',course:'Network Security'},{type:'new_course',message:'New course available: Advanced Routing',course:null}]};setMoodleData(mockData);}catch(error){console.error('Error fetching Moodle data:',error);}finally{setLoading(false);}};fetchMoodleData();},[]);if(loading){return/*#__PURE__*/_jsx(\"div\",{className:\"min-h-screen bg-gradient-to-br from-[#0a0500] via-[#1a0f00] to-[#2a1a05] text-white pt-20\",children:/*#__PURE__*/_jsx(\"div\",{className:\"container mx-auto px-6 py-12\",children:/*#__PURE__*/_jsx(\"div\",{className:\"flex items-center justify-center h-64\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"text-center\",children:[/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-spinner fa-spin text-4xl text-[#15a7dd] mb-4\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-xl\",children:\"Loading your magical dashboard...\"})]})})})});}return/*#__PURE__*/_jsx(\"div\",{className:\"min-h-screen bg-gradient-to-br from-[#0a0500] via-[#1a0f00] to-[#2a1a05] text-white pt-20\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"container mx-auto px-6 py-12\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"mb-12\",children:[/*#__PURE__*/_jsxs(\"h1\",{className:\"text-4xl md:text-5xl font-serif font-bold mb-4\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-[#15a7dd]\",children:\"Unified\"}),\" Learning Dashboard\"]}),/*#__PURE__*/_jsx(\"p\",{className:\"text-xl text-gray-300 max-w-3xl\",children:\"Your central command center for all learning activities across the React frontend and Moodle backend.\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\"flex flex-wrap border-b border-gray-700 mb-8\",children:['overview','courses','progress','integration'].map(section=>/*#__PURE__*/_jsx(\"button\",{onClick:()=>setActiveSection(section),className:\"px-6 py-3 font-medium text-sm transition-colors duration-300 rounded-button whitespace-nowrap cursor-pointer \".concat(activeSection===section?'text-[#15a7dd] border-b-2 border-[#15a7dd]':'text-gray-400 hover:text-[#15a7dd]'),children:section.charAt(0).toUpperCase()+section.slice(1)},section))}),activeSection==='overview'&&/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-1 lg:grid-cols-3 gap-8\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"lg:col-span-2\",children:[/*#__PURE__*/_jsx(\"h2\",{className:\"text-2xl font-serif font-bold mb-6\",children:\"Learning Overview\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"bg-gradient-to-br from-[#15a7dd]/20 to-[#15a7dd]/10 rounded-lg p-6 border border-[#15a7dd]/30\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg font-semibold mb-2\",children:\"Active Courses\"}),/*#__PURE__*/_jsx(\"div\",{className:\"text-3xl font-bold text-[#15a7dd]\",children:(moodleData===null||moodleData===void 0?void 0:(_moodleData$courses=moodleData.courses)===null||_moodleData$courses===void 0?void 0:_moodleData$courses.length)||0})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"bg-gradient-to-br from-[#6a3293]/20 to-[#6a3293]/10 rounded-lg p-6 border border-[#6a3293]/30\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg font-semibold mb-2\",children:\"Avg Progress\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-3xl font-bold text-[#6a3293]\",children:[moodleData!==null&&moodleData!==void 0&&moodleData.courses?Math.round(moodleData.courses.reduce((acc,course)=>acc+course.progress,0)/moodleData.courses.length):0,\"%\"]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"bg-gradient-to-br from-yellow-500/20 to-yellow-500/10 rounded-lg p-6 border border-yellow-500/30\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg font-semibold mb-2\",children:\"Notifications\"}),/*#__PURE__*/_jsx(\"div\",{className:\"text-3xl font-bold text-yellow-500\",children:(moodleData===null||moodleData===void 0?void 0:(_moodleData$notificat=moodleData.notifications)===null||_moodleData$notificat===void 0?void 0:_moodleData$notificat.length)||0})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"bg-[#1a0f00] rounded-lg p-6 border border-gray-700\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"text-xl font-semibold mb-4\",children:\"Recent Activity\"}),/*#__PURE__*/_jsx(\"div\",{className:\"space-y-4\",children:(moodleData===null||moodleData===void 0?void 0:(_moodleData$recentAct=moodleData.recentActivity)===null||_moodleData$recentAct===void 0?void 0:_moodleData$recentAct.map((activity,index)=>/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between p-3 bg-[#2a1a05] rounded\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center\",children:[/*#__PURE__*/_jsx(\"i\",{className:\"fas \".concat(activity.type==='course_completion'?'fa-check-circle text-green-500':'fa-upload text-blue-500',\" mr-3\")}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"p\",{className:\"font-medium\",children:activity.type.replace('_',' ').toUpperCase()}),/*#__PURE__*/_jsx(\"p\",{className:\"text-sm text-gray-400\",children:activity.course})]})]}),/*#__PURE__*/_jsx(\"span\",{className:\"text-sm text-gray-400\",children:activity.timestamp})]},index)))||/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-400 text-center py-4\",children:\"No recent activity\"})})]})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h2\",{className:\"text-2xl font-serif font-bold mb-6\",children:\"Quick Actions\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-4\",children:[/*#__PURE__*/_jsx(Link,{to:\"/classroom\",className:\"block w-full p-4 bg-[#15a7dd] rounded-lg hover:bg-[#1397c7] transition-colors duration-300\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center\",children:[/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-chalkboard-teacher mr-3 text-xl\"}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h3\",{className:\"font-semibold\",children:\"My Classroom\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-sm opacity-90\",children:\"Personal learning space\"})]})]})}),/*#__PURE__*/_jsx(\"a\",{href:\"/learning\",className:\"block w-full p-4 bg-[#6a3293] rounded-lg hover:bg-[#5a2283] transition-colors duration-300\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center\",children:[/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-graduation-cap mr-3 text-xl\"}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h3\",{className:\"font-semibold\",children:\"Moodle LMS\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-sm opacity-90\",children:\"Access full course system\"})]})]})}),/*#__PURE__*/_jsx(Link,{to:\"/library\",className:\"block w-full p-4 bg-yellow-600 rounded-lg hover:bg-yellow-700 transition-colors duration-300\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center\",children:[/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-book mr-3 text-xl\"}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h3\",{className:\"font-semibold\",children:\"Library\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-sm opacity-90\",children:\"Browse resources\"})]})]})}),/*#__PURE__*/_jsx(Link,{to:\"/departments\",className:\"block w-full p-4 bg-green-600 rounded-lg hover:bg-green-700 transition-colors duration-300\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center\",children:[/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-building mr-3 text-xl\"}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h3\",{className:\"font-semibold\",children:\"Departments\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-sm opacity-90\",children:\"Explore specializations\"})]})]})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"mt-8 bg-[#1a0f00] rounded-lg p-6 border border-gray-700\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"text-xl font-semibold mb-4\",children:\"Notifications\"}),/*#__PURE__*/_jsx(\"div\",{className:\"space-y-3\",children:(moodleData===null||moodleData===void 0?void 0:(_moodleData$notificat2=moodleData.notifications)===null||_moodleData$notificat2===void 0?void 0:_moodleData$notificat2.map((notification,index)=>/*#__PURE__*/_jsxs(\"div\",{className:\"p-3 bg-[#2a1a05] rounded border-l-4 border-yellow-500\",children:[/*#__PURE__*/_jsx(\"p\",{className:\"text-sm font-medium\",children:notification.message}),notification.course&&/*#__PURE__*/_jsx(\"p\",{className:\"text-xs text-gray-400 mt-1\",children:notification.course})]},index)))||/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-400 text-center py-4\",children:\"No notifications\"})})]})]})]}),activeSection==='courses'&&/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h2\",{className:\"text-2xl font-serif font-bold mb-6\",children:\"Course Management\"}),/*#__PURE__*/_jsx(\"div\",{className:\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",children:(moodleData===null||moodleData===void 0?void 0:(_moodleData$courses2=moodleData.courses)===null||_moodleData$courses2===void 0?void 0:_moodleData$courses2.map(course=>/*#__PURE__*/_jsxs(\"div\",{className:\"bg-[#1a0f00] rounded-lg p-6 border border-gray-700\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"text-xl font-semibold mb-4\",children:course.name}),/*#__PURE__*/_jsxs(\"div\",{className:\"mb-4\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex justify-between text-sm mb-2\",children:[/*#__PURE__*/_jsx(\"span\",{children:\"Progress\"}),/*#__PURE__*/_jsxs(\"span\",{children:[course.progress,\"%\"]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"w-full bg-gray-700 rounded-full h-2\",children:/*#__PURE__*/_jsx(\"div\",{className:\"bg-[#15a7dd] h-2 rounded-full\",style:{width:\"\".concat(course.progress,\"%\")}})})]}),/*#__PURE__*/_jsx(\"a\",{href:\"/learning\",className:\"block w-full text-center py-2 bg-[#15a7dd] rounded hover:bg-[#1397c7] transition-colors duration-300\",children:\"Open in Moodle\"})]},course.id)))||/*#__PURE__*/_jsx(\"div\",{className:\"col-span-full text-center py-12\",children:/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-400\",children:\"No courses found\"})})})]}),activeSection==='progress'&&/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h2\",{className:\"text-2xl font-serif font-bold mb-6\",children:\"Learning Progress\"}),/*#__PURE__*/_jsx(\"div\",{className:\"bg-[#1a0f00] rounded-lg p-6 border border-gray-700\",children:/*#__PURE__*/_jsxs(\"p\",{className:\"text-center text-gray-400 py-12\",children:[/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-chart-line text-4xl mb-4 block\"}),\"Detailed progress analytics coming soon!\"]})})]}),activeSection==='integration'&&/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h2\",{className:\"text-2xl font-serif font-bold mb-6\",children:\"React-Moodle Integration\"}),/*#__PURE__*/_jsx(MoodleIntegration,{})]})]})});};export default UnifiedDashboard;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Link", "MoodleIntegration", "jsx", "_jsx", "jsxs", "_jsxs", "UnifiedDashboard", "_ref", "_moodleData$courses", "_moodleData$notificat", "_moodleData$recentAct", "_moodleData$notificat2", "_moodleData$courses2", "user", "moodleData", "setMoodleData", "loading", "setLoading", "activeSection", "setActiveSection", "fetchMoodleData", "mockData", "courses", "id", "name", "progress", "recentActivity", "type", "course", "timestamp", "notifications", "message", "error", "console", "className", "children", "map", "section", "onClick", "concat", "char<PERSON>t", "toUpperCase", "slice", "length", "Math", "round", "reduce", "acc", "activity", "index", "replace", "to", "href", "notification", "style", "width"], "sources": ["/Users/<USER>/Documents/FatbeamU/react-app/src/components/UnifiedDashboard.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Link } from 'react-router-dom';\nimport MoodleIntegration from './MoodleIntegration';\n\nconst UnifiedDashboard = ({ user }) => {\n  const [moodleData, setMoodleData] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [activeSection, setActiveSection] = useState('overview');\n\n  useEffect(() => {\n    // Fetch Moodle data when component mounts\n    const fetchMoodleData = async () => {\n      try {\n        setLoading(true);\n        // This will be implemented with actual Moodle API calls\n        // For now, using mock data\n        const mockData = {\n          courses: [\n            { id: 1, name: 'Fundamentals of Magical Networking', progress: 85 },\n            { id: 2, name: 'Fiber Optic Enchantments', progress: 60 },\n            { id: 3, name: 'Network Security Wards', progress: 30 }\n          ],\n          recentActivity: [\n            { type: 'course_completion', course: 'Basic Network Spells', timestamp: '2024-01-15' },\n            { type: 'assignment_submission', course: 'Fiber Optics 101', timestamp: '2024-01-14' }\n          ],\n          notifications: [\n            { type: 'assignment_due', message: 'Assignment due in 3 days', course: 'Network Security' },\n            { type: 'new_course', message: 'New course available: Advanced Routing', course: null }\n          ]\n        };\n        setMoodleData(mockData);\n      } catch (error) {\n        console.error('Error fetching Moodle data:', error);\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchMoodleData();\n  }, []);\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-[#0a0500] via-[#1a0f00] to-[#2a1a05] text-white pt-20\">\n        <div className=\"container mx-auto px-6 py-12\">\n          <div className=\"flex items-center justify-center h-64\">\n            <div className=\"text-center\">\n              <i className=\"fas fa-spinner fa-spin text-4xl text-[#15a7dd] mb-4\"></i>\n              <p className=\"text-xl\">Loading your magical dashboard...</p>\n            </div>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-[#0a0500] via-[#1a0f00] to-[#2a1a05] text-white pt-20\">\n      <div className=\"container mx-auto px-6 py-12\">\n        {/* Header */}\n        <div className=\"mb-12\">\n          <h1 className=\"text-4xl md:text-5xl font-serif font-bold mb-4\">\n            <span className=\"text-[#15a7dd]\">Unified</span> Learning Dashboard\n          </h1>\n          <p className=\"text-xl text-gray-300 max-w-3xl\">\n            Your central command center for all learning activities across the React frontend and Moodle backend.\n          </p>\n        </div>\n\n        {/* Navigation Tabs */}\n        <div className=\"flex flex-wrap border-b border-gray-700 mb-8\">\n          {['overview', 'courses', 'progress', 'integration'].map((section) => (\n            <button\n              key={section}\n              onClick={() => setActiveSection(section)}\n              className={`px-6 py-3 font-medium text-sm transition-colors duration-300 rounded-button whitespace-nowrap cursor-pointer ${\n                activeSection === section\n                  ? 'text-[#15a7dd] border-b-2 border-[#15a7dd]'\n                  : 'text-gray-400 hover:text-[#15a7dd]'\n              }`}\n            >\n              {section.charAt(0).toUpperCase() + section.slice(1)}\n            </button>\n          ))}\n        </div>\n\n        {/* Overview Section */}\n        {activeSection === 'overview' && (\n          <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-8\">\n            <div className=\"lg:col-span-2\">\n              <h2 className=\"text-2xl font-serif font-bold mb-6\">Learning Overview</h2>\n              \n              {/* Quick Stats */}\n              <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8\">\n                <div className=\"bg-gradient-to-br from-[#15a7dd]/20 to-[#15a7dd]/10 rounded-lg p-6 border border-[#15a7dd]/30\">\n                  <h3 className=\"text-lg font-semibold mb-2\">Active Courses</h3>\n                  <div className=\"text-3xl font-bold text-[#15a7dd]\">{moodleData?.courses?.length || 0}</div>\n                </div>\n                \n                <div className=\"bg-gradient-to-br from-[#6a3293]/20 to-[#6a3293]/10 rounded-lg p-6 border border-[#6a3293]/30\">\n                  <h3 className=\"text-lg font-semibold mb-2\">Avg Progress</h3>\n                  <div className=\"text-3xl font-bold text-[#6a3293]\">\n                    {moodleData?.courses ? Math.round(moodleData.courses.reduce((acc, course) => acc + course.progress, 0) / moodleData.courses.length) : 0}%\n                  </div>\n                </div>\n                \n                <div className=\"bg-gradient-to-br from-yellow-500/20 to-yellow-500/10 rounded-lg p-6 border border-yellow-500/30\">\n                  <h3 className=\"text-lg font-semibold mb-2\">Notifications</h3>\n                  <div className=\"text-3xl font-bold text-yellow-500\">{moodleData?.notifications?.length || 0}</div>\n                </div>\n              </div>\n\n              {/* Recent Activity */}\n              <div className=\"bg-[#1a0f00] rounded-lg p-6 border border-gray-700\">\n                <h3 className=\"text-xl font-semibold mb-4\">Recent Activity</h3>\n                <div className=\"space-y-4\">\n                  {moodleData?.recentActivity?.map((activity, index) => (\n                    <div key={index} className=\"flex items-center justify-between p-3 bg-[#2a1a05] rounded\">\n                      <div className=\"flex items-center\">\n                        <i className={`fas ${activity.type === 'course_completion' ? 'fa-check-circle text-green-500' : 'fa-upload text-blue-500'} mr-3`}></i>\n                        <div>\n                          <p className=\"font-medium\">{activity.type.replace('_', ' ').toUpperCase()}</p>\n                          <p className=\"text-sm text-gray-400\">{activity.course}</p>\n                        </div>\n                      </div>\n                      <span className=\"text-sm text-gray-400\">{activity.timestamp}</span>\n                    </div>\n                  )) || (\n                    <p className=\"text-gray-400 text-center py-4\">No recent activity</p>\n                  )}\n                </div>\n              </div>\n            </div>\n\n            <div>\n              <h2 className=\"text-2xl font-serif font-bold mb-6\">Quick Actions</h2>\n              <div className=\"space-y-4\">\n                <Link to=\"/classroom\" className=\"block w-full p-4 bg-[#15a7dd] rounded-lg hover:bg-[#1397c7] transition-colors duration-300\">\n                  <div className=\"flex items-center\">\n                    <i className=\"fas fa-chalkboard-teacher mr-3 text-xl\"></i>\n                    <div>\n                      <h3 className=\"font-semibold\">My Classroom</h3>\n                      <p className=\"text-sm opacity-90\">Personal learning space</p>\n                    </div>\n                  </div>\n                </Link>\n\n                <a href=\"/learning\" className=\"block w-full p-4 bg-[#6a3293] rounded-lg hover:bg-[#5a2283] transition-colors duration-300\">\n                  <div className=\"flex items-center\">\n                    <i className=\"fas fa-graduation-cap mr-3 text-xl\"></i>\n                    <div>\n                      <h3 className=\"font-semibold\">Moodle LMS</h3>\n                      <p className=\"text-sm opacity-90\">Access full course system</p>\n                    </div>\n                  </div>\n                </a>\n\n                <Link to=\"/library\" className=\"block w-full p-4 bg-yellow-600 rounded-lg hover:bg-yellow-700 transition-colors duration-300\">\n                  <div className=\"flex items-center\">\n                    <i className=\"fas fa-book mr-3 text-xl\"></i>\n                    <div>\n                      <h3 className=\"font-semibold\">Library</h3>\n                      <p className=\"text-sm opacity-90\">Browse resources</p>\n                    </div>\n                  </div>\n                </Link>\n\n                <Link to=\"/departments\" className=\"block w-full p-4 bg-green-600 rounded-lg hover:bg-green-700 transition-colors duration-300\">\n                  <div className=\"flex items-center\">\n                    <i className=\"fas fa-building mr-3 text-xl\"></i>\n                    <div>\n                      <h3 className=\"font-semibold\">Departments</h3>\n                      <p className=\"text-sm opacity-90\">Explore specializations</p>\n                    </div>\n                  </div>\n                </Link>\n              </div>\n\n              {/* Notifications */}\n              <div className=\"mt-8 bg-[#1a0f00] rounded-lg p-6 border border-gray-700\">\n                <h3 className=\"text-xl font-semibold mb-4\">Notifications</h3>\n                <div className=\"space-y-3\">\n                  {moodleData?.notifications?.map((notification, index) => (\n                    <div key={index} className=\"p-3 bg-[#2a1a05] rounded border-l-4 border-yellow-500\">\n                      <p className=\"text-sm font-medium\">{notification.message}</p>\n                      {notification.course && (\n                        <p className=\"text-xs text-gray-400 mt-1\">{notification.course}</p>\n                      )}\n                    </div>\n                  )) || (\n                    <p className=\"text-gray-400 text-center py-4\">No notifications</p>\n                  )}\n                </div>\n              </div>\n            </div>\n          </div>\n        )}\n\n        {/* Courses Section */}\n        {activeSection === 'courses' && (\n          <div>\n            <h2 className=\"text-2xl font-serif font-bold mb-6\">Course Management</h2>\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n              {moodleData?.courses?.map((course) => (\n                <div key={course.id} className=\"bg-[#1a0f00] rounded-lg p-6 border border-gray-700\">\n                  <h3 className=\"text-xl font-semibold mb-4\">{course.name}</h3>\n                  <div className=\"mb-4\">\n                    <div className=\"flex justify-between text-sm mb-2\">\n                      <span>Progress</span>\n                      <span>{course.progress}%</span>\n                    </div>\n                    <div className=\"w-full bg-gray-700 rounded-full h-2\">\n                      <div className=\"bg-[#15a7dd] h-2 rounded-full\" style={{ width: `${course.progress}%` }}></div>\n                    </div>\n                  </div>\n                  <a href=\"/learning\" className=\"block w-full text-center py-2 bg-[#15a7dd] rounded hover:bg-[#1397c7] transition-colors duration-300\">\n                    Open in Moodle\n                  </a>\n                </div>\n              )) || (\n                <div className=\"col-span-full text-center py-12\">\n                  <p className=\"text-gray-400\">No courses found</p>\n                </div>\n              )}\n            </div>\n          </div>\n        )}\n\n        {/* Progress Section */}\n        {activeSection === 'progress' && (\n          <div>\n            <h2 className=\"text-2xl font-serif font-bold mb-6\">Learning Progress</h2>\n            <div className=\"bg-[#1a0f00] rounded-lg p-6 border border-gray-700\">\n              <p className=\"text-center text-gray-400 py-12\">\n                <i className=\"fas fa-chart-line text-4xl mb-4 block\"></i>\n                Detailed progress analytics coming soon!\n              </p>\n            </div>\n          </div>\n        )}\n\n        {/* Integration Section */}\n        {activeSection === 'integration' && (\n          <div>\n            <h2 className=\"text-2xl font-serif font-bold mb-6\">React-Moodle Integration</h2>\n            <MoodleIntegration />\n          </div>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default UnifiedDashboard;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OAASC,IAAI,KAAQ,kBAAkB,CACvC,MAAO,CAAAC,iBAAiB,KAAM,qBAAqB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEpD,KAAM,CAAAC,gBAAgB,CAAGC,IAAA,EAAc,KAAAC,mBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,sBAAA,CAAAC,oBAAA,IAAb,CAAEC,IAAK,CAAC,CAAAN,IAAA,CAChC,KAAM,CAACO,UAAU,CAAEC,aAAa,CAAC,CAAGjB,QAAQ,CAAC,IAAI,CAAC,CAClD,KAAM,CAACkB,OAAO,CAAEC,UAAU,CAAC,CAAGnB,QAAQ,CAAC,IAAI,CAAC,CAC5C,KAAM,CAACoB,aAAa,CAAEC,gBAAgB,CAAC,CAAGrB,QAAQ,CAAC,UAAU,CAAC,CAE9DC,SAAS,CAAC,IAAM,CACd;AACA,KAAM,CAAAqB,eAAe,CAAG,KAAAA,CAAA,GAAY,CAClC,GAAI,CACFH,UAAU,CAAC,IAAI,CAAC,CAChB;AACA;AACA,KAAM,CAAAI,QAAQ,CAAG,CACfC,OAAO,CAAE,CACP,CAAEC,EAAE,CAAE,CAAC,CAAEC,IAAI,CAAE,oCAAoC,CAAEC,QAAQ,CAAE,EAAG,CAAC,CACnE,CAAEF,EAAE,CAAE,CAAC,CAAEC,IAAI,CAAE,0BAA0B,CAAEC,QAAQ,CAAE,EAAG,CAAC,CACzD,CAAEF,EAAE,CAAE,CAAC,CAAEC,IAAI,CAAE,wBAAwB,CAAEC,QAAQ,CAAE,EAAG,CAAC,CACxD,CACDC,cAAc,CAAE,CACd,CAAEC,IAAI,CAAE,mBAAmB,CAAEC,MAAM,CAAE,sBAAsB,CAAEC,SAAS,CAAE,YAAa,CAAC,CACtF,CAAEF,IAAI,CAAE,uBAAuB,CAAEC,MAAM,CAAE,kBAAkB,CAAEC,SAAS,CAAE,YAAa,CAAC,CACvF,CACDC,aAAa,CAAE,CACb,CAAEH,IAAI,CAAE,gBAAgB,CAAEI,OAAO,CAAE,0BAA0B,CAAEH,MAAM,CAAE,kBAAmB,CAAC,CAC3F,CAAED,IAAI,CAAE,YAAY,CAAEI,OAAO,CAAE,wCAAwC,CAAEH,MAAM,CAAE,IAAK,CAAC,CAE3F,CAAC,CACDb,aAAa,CAACM,QAAQ,CAAC,CACzB,CAAE,MAAOW,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,6BAA6B,CAAEA,KAAK,CAAC,CACrD,CAAC,OAAS,CACRf,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAEDG,eAAe,CAAC,CAAC,CACnB,CAAC,CAAE,EAAE,CAAC,CAEN,GAAIJ,OAAO,CAAE,CACX,mBACEb,IAAA,QAAK+B,SAAS,CAAC,2FAA2F,CAAAC,QAAA,cACxGhC,IAAA,QAAK+B,SAAS,CAAC,8BAA8B,CAAAC,QAAA,cAC3ChC,IAAA,QAAK+B,SAAS,CAAC,uCAAuC,CAAAC,QAAA,cACpD9B,KAAA,QAAK6B,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1BhC,IAAA,MAAG+B,SAAS,CAAC,qDAAqD,CAAI,CAAC,cACvE/B,IAAA,MAAG+B,SAAS,CAAC,SAAS,CAAAC,QAAA,CAAC,mCAAiC,CAAG,CAAC,EACzD,CAAC,CACH,CAAC,CACH,CAAC,CACH,CAAC,CAEV,CAEA,mBACEhC,IAAA,QAAK+B,SAAS,CAAC,2FAA2F,CAAAC,QAAA,cACxG9B,KAAA,QAAK6B,SAAS,CAAC,8BAA8B,CAAAC,QAAA,eAE3C9B,KAAA,QAAK6B,SAAS,CAAC,OAAO,CAAAC,QAAA,eACpB9B,KAAA,OAAI6B,SAAS,CAAC,gDAAgD,CAAAC,QAAA,eAC5DhC,IAAA,SAAM+B,SAAS,CAAC,gBAAgB,CAAAC,QAAA,CAAC,SAAO,CAAM,CAAC,sBACjD,EAAI,CAAC,cACLhC,IAAA,MAAG+B,SAAS,CAAC,iCAAiC,CAAAC,QAAA,CAAC,uGAE/C,CAAG,CAAC,EACD,CAAC,cAGNhC,IAAA,QAAK+B,SAAS,CAAC,8CAA8C,CAAAC,QAAA,CAC1D,CAAC,UAAU,CAAE,SAAS,CAAE,UAAU,CAAE,aAAa,CAAC,CAACC,GAAG,CAAEC,OAAO,eAC9DlC,IAAA,WAEEmC,OAAO,CAAEA,CAAA,GAAMnB,gBAAgB,CAACkB,OAAO,CAAE,CACzCH,SAAS,iHAAAK,MAAA,CACPrB,aAAa,GAAKmB,OAAO,CACrB,4CAA4C,CAC5C,oCAAoC,CACvC,CAAAF,QAAA,CAEFE,OAAO,CAACG,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAAGJ,OAAO,CAACK,KAAK,CAAC,CAAC,CAAC,EAR9CL,OASC,CACT,CAAC,CACC,CAAC,CAGLnB,aAAa,GAAK,UAAU,eAC3Bb,KAAA,QAAK6B,SAAS,CAAC,uCAAuC,CAAAC,QAAA,eACpD9B,KAAA,QAAK6B,SAAS,CAAC,eAAe,CAAAC,QAAA,eAC5BhC,IAAA,OAAI+B,SAAS,CAAC,oCAAoC,CAAAC,QAAA,CAAC,mBAAiB,CAAI,CAAC,cAGzE9B,KAAA,QAAK6B,SAAS,CAAC,4CAA4C,CAAAC,QAAA,eACzD9B,KAAA,QAAK6B,SAAS,CAAC,+FAA+F,CAAAC,QAAA,eAC5GhC,IAAA,OAAI+B,SAAS,CAAC,4BAA4B,CAAAC,QAAA,CAAC,gBAAc,CAAI,CAAC,cAC9DhC,IAAA,QAAK+B,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CAAE,CAAArB,UAAU,SAAVA,UAAU,kBAAAN,mBAAA,CAAVM,UAAU,CAAEQ,OAAO,UAAAd,mBAAA,iBAAnBA,mBAAA,CAAqBmC,MAAM,GAAI,CAAC,CAAM,CAAC,EACxF,CAAC,cAENtC,KAAA,QAAK6B,SAAS,CAAC,+FAA+F,CAAAC,QAAA,eAC5GhC,IAAA,OAAI+B,SAAS,CAAC,4BAA4B,CAAAC,QAAA,CAAC,cAAY,CAAI,CAAC,cAC5D9B,KAAA,QAAK6B,SAAS,CAAC,mCAAmC,CAAAC,QAAA,EAC/CrB,UAAU,SAAVA,UAAU,WAAVA,UAAU,CAAEQ,OAAO,CAAGsB,IAAI,CAACC,KAAK,CAAC/B,UAAU,CAACQ,OAAO,CAACwB,MAAM,CAAC,CAACC,GAAG,CAAEnB,MAAM,GAAKmB,GAAG,CAAGnB,MAAM,CAACH,QAAQ,CAAE,CAAC,CAAC,CAAGX,UAAU,CAACQ,OAAO,CAACqB,MAAM,CAAC,CAAG,CAAC,CAAC,GAC1I,EAAK,CAAC,EACH,CAAC,cAENtC,KAAA,QAAK6B,SAAS,CAAC,kGAAkG,CAAAC,QAAA,eAC/GhC,IAAA,OAAI+B,SAAS,CAAC,4BAA4B,CAAAC,QAAA,CAAC,eAAa,CAAI,CAAC,cAC7DhC,IAAA,QAAK+B,SAAS,CAAC,oCAAoC,CAAAC,QAAA,CAAE,CAAArB,UAAU,SAAVA,UAAU,kBAAAL,qBAAA,CAAVK,UAAU,CAAEgB,aAAa,UAAArB,qBAAA,iBAAzBA,qBAAA,CAA2BkC,MAAM,GAAI,CAAC,CAAM,CAAC,EAC/F,CAAC,EACH,CAAC,cAGNtC,KAAA,QAAK6B,SAAS,CAAC,oDAAoD,CAAAC,QAAA,eACjEhC,IAAA,OAAI+B,SAAS,CAAC,4BAA4B,CAAAC,QAAA,CAAC,iBAAe,CAAI,CAAC,cAC/DhC,IAAA,QAAK+B,SAAS,CAAC,WAAW,CAAAC,QAAA,CACvB,CAAArB,UAAU,SAAVA,UAAU,kBAAAJ,qBAAA,CAAVI,UAAU,CAAEY,cAAc,UAAAhB,qBAAA,iBAA1BA,qBAAA,CAA4B0B,GAAG,CAAC,CAACY,QAAQ,CAAEC,KAAK,gBAC/C5C,KAAA,QAAiB6B,SAAS,CAAC,4DAA4D,CAAAC,QAAA,eACrF9B,KAAA,QAAK6B,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChChC,IAAA,MAAG+B,SAAS,QAAAK,MAAA,CAASS,QAAQ,CAACrB,IAAI,GAAK,mBAAmB,CAAG,gCAAgC,CAAG,yBAAyB,SAAQ,CAAI,CAAC,cACtItB,KAAA,QAAA8B,QAAA,eACEhC,IAAA,MAAG+B,SAAS,CAAC,aAAa,CAAAC,QAAA,CAAEa,QAAQ,CAACrB,IAAI,CAACuB,OAAO,CAAC,GAAG,CAAE,GAAG,CAAC,CAACT,WAAW,CAAC,CAAC,CAAI,CAAC,cAC9EtC,IAAA,MAAG+B,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAEa,QAAQ,CAACpB,MAAM,CAAI,CAAC,EACvD,CAAC,EACH,CAAC,cACNzB,IAAA,SAAM+B,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAEa,QAAQ,CAACnB,SAAS,CAAO,CAAC,GAR3DoB,KASL,CACN,CAAC,gBACA9C,IAAA,MAAG+B,SAAS,CAAC,gCAAgC,CAAAC,QAAA,CAAC,oBAAkB,CAAG,CACpE,CACE,CAAC,EACH,CAAC,EACH,CAAC,cAEN9B,KAAA,QAAA8B,QAAA,eACEhC,IAAA,OAAI+B,SAAS,CAAC,oCAAoC,CAAAC,QAAA,CAAC,eAAa,CAAI,CAAC,cACrE9B,KAAA,QAAK6B,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxBhC,IAAA,CAACH,IAAI,EAACmD,EAAE,CAAC,YAAY,CAACjB,SAAS,CAAC,4FAA4F,CAAAC,QAAA,cAC1H9B,KAAA,QAAK6B,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChChC,IAAA,MAAG+B,SAAS,CAAC,wCAAwC,CAAI,CAAC,cAC1D7B,KAAA,QAAA8B,QAAA,eACEhC,IAAA,OAAI+B,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,cAAY,CAAI,CAAC,cAC/ChC,IAAA,MAAG+B,SAAS,CAAC,oBAAoB,CAAAC,QAAA,CAAC,yBAAuB,CAAG,CAAC,EAC1D,CAAC,EACH,CAAC,CACF,CAAC,cAEPhC,IAAA,MAAGiD,IAAI,CAAC,WAAW,CAAClB,SAAS,CAAC,4FAA4F,CAAAC,QAAA,cACxH9B,KAAA,QAAK6B,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChChC,IAAA,MAAG+B,SAAS,CAAC,oCAAoC,CAAI,CAAC,cACtD7B,KAAA,QAAA8B,QAAA,eACEhC,IAAA,OAAI+B,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,YAAU,CAAI,CAAC,cAC7ChC,IAAA,MAAG+B,SAAS,CAAC,oBAAoB,CAAAC,QAAA,CAAC,2BAAyB,CAAG,CAAC,EAC5D,CAAC,EACH,CAAC,CACL,CAAC,cAEJhC,IAAA,CAACH,IAAI,EAACmD,EAAE,CAAC,UAAU,CAACjB,SAAS,CAAC,8FAA8F,CAAAC,QAAA,cAC1H9B,KAAA,QAAK6B,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChChC,IAAA,MAAG+B,SAAS,CAAC,0BAA0B,CAAI,CAAC,cAC5C7B,KAAA,QAAA8B,QAAA,eACEhC,IAAA,OAAI+B,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,SAAO,CAAI,CAAC,cAC1ChC,IAAA,MAAG+B,SAAS,CAAC,oBAAoB,CAAAC,QAAA,CAAC,kBAAgB,CAAG,CAAC,EACnD,CAAC,EACH,CAAC,CACF,CAAC,cAEPhC,IAAA,CAACH,IAAI,EAACmD,EAAE,CAAC,cAAc,CAACjB,SAAS,CAAC,4FAA4F,CAAAC,QAAA,cAC5H9B,KAAA,QAAK6B,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChChC,IAAA,MAAG+B,SAAS,CAAC,8BAA8B,CAAI,CAAC,cAChD7B,KAAA,QAAA8B,QAAA,eACEhC,IAAA,OAAI+B,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,aAAW,CAAI,CAAC,cAC9ChC,IAAA,MAAG+B,SAAS,CAAC,oBAAoB,CAAAC,QAAA,CAAC,yBAAuB,CAAG,CAAC,EAC1D,CAAC,EACH,CAAC,CACF,CAAC,EACJ,CAAC,cAGN9B,KAAA,QAAK6B,SAAS,CAAC,yDAAyD,CAAAC,QAAA,eACtEhC,IAAA,OAAI+B,SAAS,CAAC,4BAA4B,CAAAC,QAAA,CAAC,eAAa,CAAI,CAAC,cAC7DhC,IAAA,QAAK+B,SAAS,CAAC,WAAW,CAAAC,QAAA,CACvB,CAAArB,UAAU,SAAVA,UAAU,kBAAAH,sBAAA,CAAVG,UAAU,CAAEgB,aAAa,UAAAnB,sBAAA,iBAAzBA,sBAAA,CAA2ByB,GAAG,CAAC,CAACiB,YAAY,CAAEJ,KAAK,gBAClD5C,KAAA,QAAiB6B,SAAS,CAAC,uDAAuD,CAAAC,QAAA,eAChFhC,IAAA,MAAG+B,SAAS,CAAC,qBAAqB,CAAAC,QAAA,CAAEkB,YAAY,CAACtB,OAAO,CAAI,CAAC,CAC5DsB,YAAY,CAACzB,MAAM,eAClBzB,IAAA,MAAG+B,SAAS,CAAC,4BAA4B,CAAAC,QAAA,CAAEkB,YAAY,CAACzB,MAAM,CAAI,CACnE,GAJOqB,KAKL,CACN,CAAC,gBACA9C,IAAA,MAAG+B,SAAS,CAAC,gCAAgC,CAAAC,QAAA,CAAC,kBAAgB,CAAG,CAClE,CACE,CAAC,EACH,CAAC,EACH,CAAC,EACH,CACN,CAGAjB,aAAa,GAAK,SAAS,eAC1Bb,KAAA,QAAA8B,QAAA,eACEhC,IAAA,OAAI+B,SAAS,CAAC,oCAAoC,CAAAC,QAAA,CAAC,mBAAiB,CAAI,CAAC,cACzEhC,IAAA,QAAK+B,SAAS,CAAC,sDAAsD,CAAAC,QAAA,CAClE,CAAArB,UAAU,SAAVA,UAAU,kBAAAF,oBAAA,CAAVE,UAAU,CAAEQ,OAAO,UAAAV,oBAAA,iBAAnBA,oBAAA,CAAqBwB,GAAG,CAAER,MAAM,eAC/BvB,KAAA,QAAqB6B,SAAS,CAAC,oDAAoD,CAAAC,QAAA,eACjFhC,IAAA,OAAI+B,SAAS,CAAC,4BAA4B,CAAAC,QAAA,CAAEP,MAAM,CAACJ,IAAI,CAAK,CAAC,cAC7DnB,KAAA,QAAK6B,SAAS,CAAC,MAAM,CAAAC,QAAA,eACnB9B,KAAA,QAAK6B,SAAS,CAAC,mCAAmC,CAAAC,QAAA,eAChDhC,IAAA,SAAAgC,QAAA,CAAM,UAAQ,CAAM,CAAC,cACrB9B,KAAA,SAAA8B,QAAA,EAAOP,MAAM,CAACH,QAAQ,CAAC,GAAC,EAAM,CAAC,EAC5B,CAAC,cACNtB,IAAA,QAAK+B,SAAS,CAAC,qCAAqC,CAAAC,QAAA,cAClDhC,IAAA,QAAK+B,SAAS,CAAC,+BAA+B,CAACoB,KAAK,CAAE,CAAEC,KAAK,IAAAhB,MAAA,CAAKX,MAAM,CAACH,QAAQ,KAAI,CAAE,CAAM,CAAC,CAC3F,CAAC,EACH,CAAC,cACNtB,IAAA,MAAGiD,IAAI,CAAC,WAAW,CAAClB,SAAS,CAAC,sGAAsG,CAAAC,QAAA,CAAC,gBAErI,CAAG,CAAC,GAbIP,MAAM,CAACL,EAcZ,CACN,CAAC,gBACApB,IAAA,QAAK+B,SAAS,CAAC,iCAAiC,CAAAC,QAAA,cAC9ChC,IAAA,MAAG+B,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,kBAAgB,CAAG,CAAC,CAC9C,CACN,CACE,CAAC,EACH,CACN,CAGAjB,aAAa,GAAK,UAAU,eAC3Bb,KAAA,QAAA8B,QAAA,eACEhC,IAAA,OAAI+B,SAAS,CAAC,oCAAoC,CAAAC,QAAA,CAAC,mBAAiB,CAAI,CAAC,cACzEhC,IAAA,QAAK+B,SAAS,CAAC,oDAAoD,CAAAC,QAAA,cACjE9B,KAAA,MAAG6B,SAAS,CAAC,iCAAiC,CAAAC,QAAA,eAC5ChC,IAAA,MAAG+B,SAAS,CAAC,uCAAuC,CAAI,CAAC,2CAE3D,EAAG,CAAC,CACD,CAAC,EACH,CACN,CAGAhB,aAAa,GAAK,aAAa,eAC9Bb,KAAA,QAAA8B,QAAA,eACEhC,IAAA,OAAI+B,SAAS,CAAC,oCAAoC,CAAAC,QAAA,CAAC,0BAAwB,CAAI,CAAC,cAChFhC,IAAA,CAACF,iBAAiB,GAAE,CAAC,EAClB,CACN,EACE,CAAC,CACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAAK,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}