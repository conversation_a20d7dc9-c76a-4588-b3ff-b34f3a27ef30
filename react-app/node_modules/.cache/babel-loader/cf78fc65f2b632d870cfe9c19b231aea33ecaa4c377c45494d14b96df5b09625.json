{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/FatbeamU/react-app/src/pages/departments/ConstructionDeptPage.js\";\nimport React from 'react';\nimport { Link } from 'react-router-dom';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ConstructionDeptPage = () => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-white text-gray-800 font-sans pt-20\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container mx-auto px-6 py-12\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center mb-12\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-4xl md:text-5xl font-serif font-bold mb-6\",\n          children: [\"Construction Department \", /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-orange-600\",\n            children: \"(Earth Mages)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 10,\n            columnNumber: 37\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 9,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-xl text-gray-600 max-w-3xl mx-auto\",\n          children: \"Harness the elements to lay the physical foundations of our network, connecting the material world to the digital realm.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 12,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 8,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-orange-50 rounded-lg p-8 text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"i\", {\n          className: \"fas fa-shovel text-6xl text-orange-600 mb-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 18,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-2xl font-bold text-gray-800 mb-4\",\n          children: \"Department Page Coming Soon\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 19,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600 mb-6\",\n          children: \"Our earth mages are currently working on manifesting this page from the digital realm.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 20,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/departments\",\n          className: \"inline-flex items-center px-6 py-3 bg-orange-600 text-white rounded-full hover:bg-orange-700 transition-colors duration-300\",\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-arrow-left mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 24,\n            columnNumber: 13\n          }, this), \"Back to Departments\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 23,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 17,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 7,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 6,\n    columnNumber: 5\n  }, this);\n};\n_c = ConstructionDeptPage;\nexport default ConstructionDeptPage;\nvar _c;\n$RefreshReg$(_c, \"ConstructionDeptPage\");", "map": {"version": 3, "names": ["React", "Link", "jsxDEV", "_jsxDEV", "ConstructionDeptPage", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/FatbeamU/react-app/src/pages/departments/ConstructionDeptPage.js"], "sourcesContent": ["import React from 'react';\nimport { Link } from 'react-router-dom';\n\nconst ConstructionDeptPage = () => {\n  return (\n    <div className=\"min-h-screen bg-white text-gray-800 font-sans pt-20\">\n      <div className=\"container mx-auto px-6 py-12\">\n        <div className=\"text-center mb-12\">\n          <h1 className=\"text-4xl md:text-5xl font-serif font-bold mb-6\">\n            Construction Department <span className=\"text-orange-600\">(Earth Mages)</span>\n          </h1>\n          <p className=\"text-xl text-gray-600 max-w-3xl mx-auto\">\n            Harness the elements to lay the physical foundations of our network, connecting the material world to the digital realm.\n          </p>\n        </div>\n        \n        <div className=\"bg-orange-50 rounded-lg p-8 text-center\">\n          <i className=\"fas fa-shovel text-6xl text-orange-600 mb-4\"></i>\n          <h2 className=\"text-2xl font-bold text-gray-800 mb-4\">Department Page Coming Soon</h2>\n          <p className=\"text-gray-600 mb-6\">\n            Our earth mages are currently working on manifesting this page from the digital realm.\n          </p>\n          <Link to=\"/departments\" className=\"inline-flex items-center px-6 py-3 bg-orange-600 text-white rounded-full hover:bg-orange-700 transition-colors duration-300\">\n            <i className=\"fas fa-arrow-left mr-2\"></i>\n            Back to Departments\n          </Link>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default ConstructionDeptPage;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExC,MAAMC,oBAAoB,GAAGA,CAAA,KAAM;EACjC,oBACED,OAAA;IAAKE,SAAS,EAAC,qDAAqD;IAAAC,QAAA,eAClEH,OAAA;MAAKE,SAAS,EAAC,8BAA8B;MAAAC,QAAA,gBAC3CH,OAAA;QAAKE,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChCH,OAAA;UAAIE,SAAS,EAAC,gDAAgD;UAAAC,QAAA,GAAC,0BACrC,eAAAH,OAAA;YAAME,SAAS,EAAC,iBAAiB;YAAAC,QAAA,EAAC;UAAa;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5E,CAAC,eACLP,OAAA;UAAGE,SAAS,EAAC,yCAAyC;UAAAC,QAAA,EAAC;QAEvD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAENP,OAAA;QAAKE,SAAS,EAAC,yCAAyC;QAAAC,QAAA,gBACtDH,OAAA;UAAGE,SAAS,EAAC;QAA6C;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC/DP,OAAA;UAAIE,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EAAC;QAA2B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACtFP,OAAA;UAAGE,SAAS,EAAC,oBAAoB;UAAAC,QAAA,EAAC;QAElC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACJP,OAAA,CAACF,IAAI;UAACU,EAAE,EAAC,cAAc;UAACN,SAAS,EAAC,6HAA6H;UAAAC,QAAA,gBAC7JH,OAAA;YAAGE,SAAS,EAAC;UAAwB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,uBAE5C;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACE,EAAA,GA3BIR,oBAAoB;AA6B1B,eAAeA,oBAAoB;AAAC,IAAAQ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}