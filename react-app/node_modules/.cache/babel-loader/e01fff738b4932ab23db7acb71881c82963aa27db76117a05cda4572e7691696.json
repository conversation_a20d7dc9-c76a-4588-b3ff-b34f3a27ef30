{"ast": null, "code": "import React,{useState,useEffect}from'react';import{Link}from'react-router-dom';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const FinancialDeptPage=()=>{const[activeTab,setActiveTab]=useState('overview');const[isScrolled,setIsScrolled]=useState(false);const[isMenuOpen,setIsMenuOpen]=useState(false);const[showFacultyDetails,setShowFacultyDetails]=useState(null);useEffect(()=>{const handleScroll=()=>{if(window.scrollY>50){setIsScrolled(true);}else{setIsScrolled(false);}};window.addEventListener('scroll',handleScroll);// Particle animation\nconst createParticles=()=>{const particleContainer=document.getElementById('particle-container');if(!particleContainer)return;for(let i=0;i<30;i++){const particle=document.createElement('div');particle.className='absolute w-1 h-1 rounded-full bg-yellow-400 opacity-0';// Random position\nparticle.style.left=\"\".concat(Math.random()*100,\"%\");particle.style.top=\"\".concat(Math.random()*100,\"%\");// Random animation duration\nconst duration=3+Math.random()*5;particle.style.animation=\"float \".concat(duration,\"s ease-in-out infinite\");particle.style.animationDelay=\"\".concat(Math.random()*5,\"s\");particleContainer.appendChild(particle);}};createParticles();return()=>{window.removeEventListener('scroll',handleScroll);};},[]);const facultyMembers=[{name:\"Professor Goldstein\",title:\"Gold Transmutation Master\",experience:\"45 years\",achievements:\"Discovered the Golden Ratio Spell, Author of 'Modern Alchemical Finance'\",image:\"https://readdy.ai/api/search-image?query=A%20distinguished%20elderly%20wizard%20professor%20with%20a%20neatly%20trimmed%20white%20beard%20and%20gold-rimmed%20spectacles&width=300&height=300&seq=prof1&orientation=squarish\"},{name:\"Dr. Silverton\",title:\"Precious Metals Expert\",experience:\"38 years\",achievements:\"Created the Silver Stream Investment Method, Former Royal Treasury Advisor\",image:\"https://readdy.ai/api/search-image?query=A%20middle-aged%20female%20professor%20with%20silver-streaked%20hair%20in%20an%20elegant%20updo&width=300&height=300&seq=prof2&orientation=squarish\"},{name:\"Master Bronzewing\",title:\"Risk Assessment Specialist\",experience:\"29 years\",achievements:\"Developed the Bronze Shield Protection Spell, Led the Great Market Stabilization of 2018\",image:\"https://readdy.ai/api/search-image?query=A%20confident%20wizard%20in%20his%20thirties%20with%20short%20bronze-colored%20hair%20and%20a%20well-groomed%20beard&width=300&height=300&seq=prof3&orientation=squarish\"},{name:\"Lady Platina\",title:\"Investment Strategy Archmage\",experience:\"42 years\",achievements:\"Inventor of the Platinum Growth Portfolio Spell, Five-time winner of the Golden Cauldron Award\",image:\"https://readdy.ai/api/search-image?query=An%20elegant%20older%20woman%20with%20platinum%20blonde%20hair%20in%20a%20sophisticated%20style&width=300&height=300&seq=prof4&orientation=squarish\"}];const programs=[{title:\"Magical Investment Management\",description:\"Learn to harness magical energies to identify investment opportunities and maximize returns through alchemical transformations.\",icon:\"fa-chart-line\"},{title:\"Alchemical Trading\",description:\"Master the ancient art of transforming base metals into gold while applying modern trading strategies to magical markets.\",icon:\"fa-exchange-alt\"},{title:\"Mystical Risk Assessment\",description:\"Develop your third eye to foresee market fluctuations and create protective wards against financial losses.\",icon:\"fa-shield-alt\"},{title:\"Enchanted Portfolio Management\",description:\"Craft balanced portfolios using divination techniques and magical asset allocation to achieve long-term prosperity.\",icon:\"fa-wallet\"}];return/*#__PURE__*/_jsxs(\"div\",{className:\"min-h-screen bg-white text-gray-800 font-sans pt-20\",children:[/*#__PURE__*/_jsx(\"div\",{id:\"particle-container\",className:\"fixed inset-0 pointer-events-none z-0\"}),/*#__PURE__*/_jsx(\"div\",{className:\"bg-gray-100 py-4\",children:/*#__PURE__*/_jsx(\"div\",{className:\"container mx-auto px-6\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center text-sm text-gray-600\",children:[/*#__PURE__*/_jsx(Link,{to:\"/\",className:\"hover:text-[#15a7dd] transition-colors duration-300 cursor-pointer\",children:\"Home\"}),/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-chevron-right mx-2 text-xs text-gray-400\"}),/*#__PURE__*/_jsx(Link,{to:\"/departments\",className:\"hover:text-[#15a7dd] transition-colors duration-300 cursor-pointer\",children:\"Departments\"}),/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-chevron-right mx-2 text-xs text-gray-400\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-[#15a7dd]\",children:\"Financial Department (Alchemists)\"})]})})}),/*#__PURE__*/_jsxs(\"div\",{className:\"relative h-[50vh] min-h-[600px] overflow-hidden\",children:[/*#__PURE__*/_jsx(\"img\",{src:\"https://readdy.ai/api/search-image?query=A%20magical%20financial%20department%20with%20floating%20gold%20coins%20and%20magical%20ledgers.%20Wizards%20in%20elegant%20robes%20with%20gold%20trim%20work%20at%20enchanted%20desks&width=1440&height=800&seq=herobanner&orientation=landscape\",alt:\"Financial Department\",className:\"w-full h-full object-cover\"}),/*#__PURE__*/_jsx(\"div\",{className:\"absolute inset-0 bg-gradient-to-r from-black/70 to-transparent\"}),/*#__PURE__*/_jsx(\"div\",{className:\"absolute inset-0 bg-gradient-to-b from-transparent via-transparent to-white/90\"}),/*#__PURE__*/_jsx(\"div\",{className:\"absolute inset-0 flex items-center\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"container mx-auto px-6\",children:[/*#__PURE__*/_jsxs(\"h1\",{className:\"text-5xl md:text-6xl lg:text-7xl font-serif font-bold text-white mb-6\",children:[\"Financial Department \",/*#__PURE__*/_jsx(\"span\",{className:\"bg-clip-text text-transparent bg-gradient-to-r from-yellow-400 to-yellow-600\",children:\"(Alchemists)\"})]}),/*#__PURE__*/_jsx(\"p\",{className:\"text-xl md:text-2xl text-white max-w-3xl leading-relaxed\",children:\"Where financial expertise meets magical alchemy to transform resources into prosperity through ancient arts and modern techniques.\"})]})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"container mx-auto px-6 py-12\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"flex flex-wrap border-b border-gray-200 mb-12\",children:['overview','programs','faculty','careers'].map(tab=>/*#__PURE__*/_jsx(\"button\",{onClick:()=>setActiveTab(tab),className:\"px-6 py-3 font-medium text-sm transition-colors duration-300 rounded-button whitespace-nowrap cursor-pointer \".concat(activeTab===tab?'text-[#15a7dd] border-b-2 border-[#15a7dd]':'text-gray-500 hover:text-[#15a7dd]'),children:tab.charAt(0).toUpperCase()+tab.slice(1)},tab))}),activeTab==='overview'&&/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-1 lg:grid-cols-3 gap-12\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"lg:col-span-2\",children:[/*#__PURE__*/_jsx(\"h2\",{className:\"text-3xl font-serif font-bold mb-6 text-gray-800\",children:\"Department Overview\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-700 mb-6 leading-relaxed\",children:\"The Financial Department, known colloquially as the Alchemists, represents the perfect blend of ancient magical wisdom and cutting-edge financial expertise. Our department trains students in the delicate art of transforming raw resources into financial prosperity through a combination of traditional alchemical practices and modern financial theory.\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-700 mb-6 leading-relaxed\",children:\"Founded by Master Goldstein in 1823, our department has a long tradition of excellence in magical finance. We pride ourselves on maintaining the highest ethical standards while teaching students to harness the power of financial alchemy for the greater good of the magical community.\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"bg-gradient-to-br from-yellow-50 to-yellow-100 rounded-lg p-6 shadow-md\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-yellow-600 mb-2\",children:/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-coins text-3xl\"})}),/*#__PURE__*/_jsx(\"h3\",{className:\"text-xl font-bold text-gray-800 mb-2\",children:\"98%\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-600\",children:\"Successful Financial Transformations\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"bg-gradient-to-br from-yellow-50 to-yellow-100 rounded-lg p-6 shadow-md\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-yellow-600 mb-2\",children:/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-exchange-alt text-3xl\"})}),/*#__PURE__*/_jsx(\"h3\",{className:\"text-xl font-bold text-gray-800 mb-2\",children:\"1:42\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-600\",children:\"Gold-to-Value Conversion Rate\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"bg-gradient-to-br from-yellow-50 to-yellow-100 rounded-lg p-6 shadow-md\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-yellow-600 mb-2\",children:/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-user-graduate text-3xl\"})}),/*#__PURE__*/_jsx(\"h3\",{className:\"text-xl font-bold text-gray-800 mb-2\",children:\"96%\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-600\",children:\"Graduate Placement Rate\"})]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"lg:col-span-1\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"bg-gradient-to-br from-yellow-50 to-yellow-100 rounded-lg p-6 shadow-md mb-8\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"text-xl font-serif font-bold mb-4 text-gray-800\",children:\"Department Philosophy\"}),/*#__PURE__*/_jsx(\"div\",{className:\"mb-6 flex justify-center\",children:/*#__PURE__*/_jsx(\"div\",{className:\"w-32 h-32 rounded-full bg-gradient-to-br from-yellow-300 to-yellow-500 flex items-center justify-center\",children:/*#__PURE__*/_jsx(\"img\",{src:\"https://readdy.ai/api/search-image?query=An%20alchemical%20symbol%20representing%20financial%20transformation&width=200&height=200&seq=symbol&orientation=squarish\",alt:\"Alchemical Symbol\",className:\"w-24 h-24 object-contain\"})})}),/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-700 mb-4\",children:\"\\\"Through the perfect balance of ancient wisdom and modern innovation, we transform not just metals, but minds and markets.\\\"\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-600 italic text-sm\",children:\"- Founding principle established by Master Goldstein, 1823\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"bg-white rounded-lg p-6 shadow-md border border-gray-200\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"text-xl font-serif font-bold mb-4 text-gray-800\",children:\"Quick Actions\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-3\",children:[/*#__PURE__*/_jsxs(Link,{to:\"/library\",className:\"w-full py-3 bg-gradient-to-r from-yellow-400 to-yellow-600 text-white rounded-full hover:from-yellow-500 hover:to-yellow-700 transition-all duration-300 flex items-center justify-center rounded-button whitespace-nowrap cursor-pointer\",children:[/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-book-reader mr-2\"}),\"Visit Library\"]}),/*#__PURE__*/_jsxs(Link,{to:\"/classroom\",className:\"w-full py-3 border-2 border-yellow-600 text-yellow-600 rounded-full hover:bg-yellow-50 transition-all duration-300 flex items-center justify-center rounded-button whitespace-nowrap cursor-pointer\",children:[/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-chalkboard-teacher mr-2\"}),\"Enter Classroom\"]})]})]})]})]}),activeTab==='programs'&&/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h2\",{className:\"text-3xl font-serif font-bold mb-8 text-gray-800\",children:\"Specialized Programs\"}),/*#__PURE__*/_jsx(\"div\",{className:\"grid grid-cols-1 md:grid-cols-2 gap-8\",children:programs.map((program,index)=>/*#__PURE__*/_jsxs(\"div\",{className:\"bg-white rounded-lg overflow-hidden shadow-lg border border-yellow-100 hover:shadow-xl transition-all duration-300 group\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"bg-gradient-to-r from-yellow-400 to-yellow-600 h-3\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"p-8\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-16 h-16 rounded-full bg-yellow-100 flex items-center justify-center mb-6\",children:/*#__PURE__*/_jsx(\"i\",{className:\"fas \".concat(program.icon,\" text-yellow-600 text-2xl\")})}),/*#__PURE__*/_jsx(\"h3\",{className:\"text-2xl font-serif font-bold mb-4 text-gray-800 group-hover:text-yellow-600 transition-colors duration-300\",children:program.title}),/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-700 mb-6\",children:program.description}),/*#__PURE__*/_jsxs(\"button\",{className:\"px-6 py-2 border border-yellow-600 text-yellow-600 rounded-full hover:bg-yellow-600 hover:text-white transition-all duration-300 flex items-center rounded-button whitespace-nowrap cursor-pointer\",children:[/*#__PURE__*/_jsx(\"span\",{children:\"View Curriculum\"}),/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-arrow-right ml-2 transition-transform duration-300 group-hover:translate-x-1\"})]})]})]},index))})]}),activeTab==='faculty'&&/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h2\",{className:\"text-3xl font-serif font-bold mb-8 text-gray-800\",children:\"Distinguished Faculty\"}),/*#__PURE__*/_jsx(\"div\",{className:\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\",children:facultyMembers.map((faculty,index)=>/*#__PURE__*/_jsxs(\"div\",{className:\"bg-white rounded-lg overflow-hidden shadow-md hover:shadow-xl transition-all duration-300 cursor-pointer\",onClick:()=>setShowFacultyDetails(showFacultyDetails===index?null:index),children:[/*#__PURE__*/_jsxs(\"div\",{className:\"relative\",children:[/*#__PURE__*/_jsx(\"img\",{src:faculty.image,alt:faculty.name,className:\"w-full h-64 object-cover object-top\"}),/*#__PURE__*/_jsx(\"div\",{className:\"absolute inset-0 bg-gradient-to-t from-black/70 to-transparent\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"absolute bottom-0 left-0 right-0 p-4\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"text-xl font-serif font-bold text-white\",children:faculty.name}),/*#__PURE__*/_jsx(\"p\",{className:\"text-yellow-300\",children:faculty.title})]}),/*#__PURE__*/_jsx(\"div\",{className:\"absolute top-4 right-4 w-16 h-16 rounded-full border-4 border-white bg-gradient-to-br from-yellow-400 to-yellow-600 flex items-center justify-center\",children:/*#__PURE__*/_jsx(\"span\",{className:\"text-white font-bold text-sm\",children:faculty.experience})})]}),showFacultyDetails===index&&/*#__PURE__*/_jsxs(\"div\",{className:\"p-6 bg-gray-50\",children:[/*#__PURE__*/_jsx(\"h4\",{className:\"font-bold text-gray-800 mb-2\",children:\"Notable Achievements:\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-700 text-sm\",children:faculty.achievements})]})]},index))})]}),activeTab==='careers'&&/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h2\",{className:\"text-3xl font-serif font-bold mb-8 text-gray-800\",children:\"Career Opportunities\"}),/*#__PURE__*/_jsx(\"div\",{className:\"grid grid-cols-1 md:grid-cols-3 gap-8\",children:[{title:\"Magical Investment Banks\",positions:[\"Junior Gold Transmuter\",\"Market Divination Analyst\",\"Wealth Management Sorcerer\"],icon:\"fa-landmark\"},{title:\"Alchemical Trading Firms\",positions:[\"Metals Transformation Specialist\",\"Magical Commodities Trader\",\"Alchemical Algorithm Developer\"],icon:\"fa-exchange-alt\"},{title:\"Mystical Financial Consultancies\",positions:[\"Financial Forecast Diviner\",\"Wealth Protection Warder\",\"Prosperity Spell Consultant\"],icon:\"fa-hand-holding-usd\"}].map((career,index)=>/*#__PURE__*/_jsxs(\"div\",{className:\"bg-white rounded-lg p-6 shadow-lg border border-yellow-100\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-16 h-16 rounded-full bg-yellow-100 flex items-center justify-center mb-6\",children:/*#__PURE__*/_jsx(\"i\",{className:\"fas \".concat(career.icon,\" text-yellow-600 text-2xl\")})}),/*#__PURE__*/_jsx(\"h3\",{className:\"text-xl font-serif font-bold mb-4 text-gray-800\",children:career.title}),/*#__PURE__*/_jsx(\"ul\",{className:\"space-y-2\",children:career.positions.map((position,idx)=>/*#__PURE__*/_jsxs(\"li\",{className:\"text-gray-700 text-sm flex items-center\",children:[/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-star text-yellow-500 mr-2 text-xs\"}),position]},idx))})]},index))})]})]})]});};export default FinancialDeptPage;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Link", "jsx", "_jsx", "jsxs", "_jsxs", "FinancialDeptPage", "activeTab", "setActiveTab", "isScrolled", "setIsScrolled", "isMenuOpen", "setIsMenuOpen", "showFacultyDetails", "setShowFacultyDetails", "handleScroll", "window", "scrollY", "addEventListener", "createParticles", "particleContainer", "document", "getElementById", "i", "particle", "createElement", "className", "style", "left", "concat", "Math", "random", "top", "duration", "animation", "animationDelay", "append<PERSON><PERSON><PERSON>", "removeEventListener", "facultyMembers", "name", "title", "experience", "achievements", "image", "programs", "description", "icon", "children", "id", "to", "src", "alt", "map", "tab", "onClick", "char<PERSON>t", "toUpperCase", "slice", "program", "index", "faculty", "positions", "career", "position", "idx"], "sources": ["/Users/<USER>/Documents/FatbeamU/react-app/src/pages/departments/FinancialDeptPage.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Link } from 'react-router-dom';\n\nconst FinancialDeptPage = () => {\n  const [activeTab, setActiveTab] = useState('overview');\n  const [isScrolled, setIsScrolled] = useState(false);\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n  const [showFacultyDetails, setShowFacultyDetails] = useState(null);\n\n  useEffect(() => {\n    const handleScroll = () => {\n      if (window.scrollY > 50) {\n        setIsScrolled(true);\n      } else {\n        setIsScrolled(false);\n      }\n    };\n    \n    window.addEventListener('scroll', handleScroll);\n    \n    // Particle animation\n    const createParticles = () => {\n      const particleContainer = document.getElementById('particle-container');\n      if (!particleContainer) return;\n      \n      for (let i = 0; i < 30; i++) {\n        const particle = document.createElement('div');\n        particle.className = 'absolute w-1 h-1 rounded-full bg-yellow-400 opacity-0';\n        // Random position\n        particle.style.left = `${Math.random() * 100}%`;\n        particle.style.top = `${Math.random() * 100}%`;\n        // Random animation duration\n        const duration = 3 + Math.random() * 5;\n        particle.style.animation = `float ${duration}s ease-in-out infinite`;\n        particle.style.animationDelay = `${Math.random() * 5}s`;\n        particleContainer.appendChild(particle);\n      }\n    };\n    \n    createParticles();\n    \n    return () => {\n      window.removeEventListener('scroll', handleScroll);\n    };\n  }, []);\n\n  const facultyMembers = [\n    {\n      name: \"Professor Goldstein\",\n      title: \"Gold Transmutation Master\",\n      experience: \"45 years\",\n      achievements: \"Discovered the Golden Ratio Spell, Author of 'Modern Alchemical Finance'\",\n      image: \"https://readdy.ai/api/search-image?query=A%20distinguished%20elderly%20wizard%20professor%20with%20a%20neatly%20trimmed%20white%20beard%20and%20gold-rimmed%20spectacles&width=300&height=300&seq=prof1&orientation=squarish\"\n    },\n    {\n      name: \"Dr. Silverton\",\n      title: \"Precious Metals Expert\",\n      experience: \"38 years\",\n      achievements: \"Created the Silver Stream Investment Method, Former Royal Treasury Advisor\",\n      image: \"https://readdy.ai/api/search-image?query=A%20middle-aged%20female%20professor%20with%20silver-streaked%20hair%20in%20an%20elegant%20updo&width=300&height=300&seq=prof2&orientation=squarish\"\n    },\n    {\n      name: \"Master Bronzewing\",\n      title: \"Risk Assessment Specialist\",\n      experience: \"29 years\",\n      achievements: \"Developed the Bronze Shield Protection Spell, Led the Great Market Stabilization of 2018\",\n      image: \"https://readdy.ai/api/search-image?query=A%20confident%20wizard%20in%20his%20thirties%20with%20short%20bronze-colored%20hair%20and%20a%20well-groomed%20beard&width=300&height=300&seq=prof3&orientation=squarish\"\n    },\n    {\n      name: \"Lady Platina\",\n      title: \"Investment Strategy Archmage\",\n      experience: \"42 years\",\n      achievements: \"Inventor of the Platinum Growth Portfolio Spell, Five-time winner of the Golden Cauldron Award\",\n      image: \"https://readdy.ai/api/search-image?query=An%20elegant%20older%20woman%20with%20platinum%20blonde%20hair%20in%20a%20sophisticated%20style&width=300&height=300&seq=prof4&orientation=squarish\"\n    }\n  ];\n\n  const programs = [\n    {\n      title: \"Magical Investment Management\",\n      description: \"Learn to harness magical energies to identify investment opportunities and maximize returns through alchemical transformations.\",\n      icon: \"fa-chart-line\"\n    },\n    {\n      title: \"Alchemical Trading\",\n      description: \"Master the ancient art of transforming base metals into gold while applying modern trading strategies to magical markets.\",\n      icon: \"fa-exchange-alt\"\n    },\n    {\n      title: \"Mystical Risk Assessment\",\n      description: \"Develop your third eye to foresee market fluctuations and create protective wards against financial losses.\",\n      icon: \"fa-shield-alt\"\n    },\n    {\n      title: \"Enchanted Portfolio Management\",\n      description: \"Craft balanced portfolios using divination techniques and magical asset allocation to achieve long-term prosperity.\",\n      icon: \"fa-wallet\"\n    }\n  ];\n\n  return (\n    <div className=\"min-h-screen bg-white text-gray-800 font-sans pt-20\">\n      {/* Particle container for magical effects */}\n      <div id=\"particle-container\" className=\"fixed inset-0 pointer-events-none z-0\"></div>\n      \n      {/* Breadcrumb */}\n      <div className=\"bg-gray-100 py-4\">\n        <div className=\"container mx-auto px-6\">\n          <div className=\"flex items-center text-sm text-gray-600\">\n            <Link to=\"/\" className=\"hover:text-[#15a7dd] transition-colors duration-300 cursor-pointer\">Home</Link>\n            <i className=\"fas fa-chevron-right mx-2 text-xs text-gray-400\"></i>\n            <Link to=\"/departments\" className=\"hover:text-[#15a7dd] transition-colors duration-300 cursor-pointer\">Departments</Link>\n            <i className=\"fas fa-chevron-right mx-2 text-xs text-gray-400\"></i>\n            <span className=\"text-[#15a7dd]\">Financial Department (Alchemists)</span>\n          </div>\n        </div>\n      </div>\n\n      {/* Hero Banner */}\n      <div className=\"relative h-[50vh] min-h-[600px] overflow-hidden\">\n        <img\n          src=\"https://readdy.ai/api/search-image?query=A%20magical%20financial%20department%20with%20floating%20gold%20coins%20and%20magical%20ledgers.%20Wizards%20in%20elegant%20robes%20with%20gold%20trim%20work%20at%20enchanted%20desks&width=1440&height=800&seq=herobanner&orientation=landscape\"\n          alt=\"Financial Department\"\n          className=\"w-full h-full object-cover\"\n        />\n        <div className=\"absolute inset-0 bg-gradient-to-r from-black/70 to-transparent\"></div>\n        <div className=\"absolute inset-0 bg-gradient-to-b from-transparent via-transparent to-white/90\"></div>\n        <div className=\"absolute inset-0 flex items-center\">\n          <div className=\"container mx-auto px-6\">\n            <h1 className=\"text-5xl md:text-6xl lg:text-7xl font-serif font-bold text-white mb-6\">\n              Financial Department <span className=\"bg-clip-text text-transparent bg-gradient-to-r from-yellow-400 to-yellow-600\">(Alchemists)</span>\n            </h1>\n            <p className=\"text-xl md:text-2xl text-white max-w-3xl leading-relaxed\">\n              Where financial expertise meets magical alchemy to transform resources into prosperity through ancient arts and modern techniques.\n            </p>\n          </div>\n        </div>\n      </div>\n\n      {/* Main Content */}\n      <div className=\"container mx-auto px-6 py-12\">\n        {/* Tabs Navigation */}\n        <div className=\"flex flex-wrap border-b border-gray-200 mb-12\">\n          {['overview', 'programs', 'faculty', 'careers'].map((tab) => (\n            <button\n              key={tab}\n              onClick={() => setActiveTab(tab)}\n              className={`px-6 py-3 font-medium text-sm transition-colors duration-300 rounded-button whitespace-nowrap cursor-pointer ${\n                activeTab === tab\n                  ? 'text-[#15a7dd] border-b-2 border-[#15a7dd]'\n                  : 'text-gray-500 hover:text-[#15a7dd]'\n              }`}\n            >\n              {tab.charAt(0).toUpperCase() + tab.slice(1)}\n            </button>\n          ))}\n        </div>\n\n        {/* Overview Section */}\n        {activeTab === 'overview' && (\n          <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-12\">\n            <div className=\"lg:col-span-2\">\n              <h2 className=\"text-3xl font-serif font-bold mb-6 text-gray-800\">Department Overview</h2>\n              <p className=\"text-gray-700 mb-6 leading-relaxed\">\n                The Financial Department, known colloquially as the Alchemists, represents the perfect blend of ancient magical wisdom and cutting-edge financial expertise. Our department trains students in the delicate art of transforming raw resources into financial prosperity through a combination of traditional alchemical practices and modern financial theory.\n              </p>\n              <p className=\"text-gray-700 mb-6 leading-relaxed\">\n                Founded by Master Goldstein in 1823, our department has a long tradition of excellence in magical finance. We pride ourselves on maintaining the highest ethical standards while teaching students to harness the power of financial alchemy for the greater good of the magical community.\n              </p>\n              \n              <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8\">\n                <div className=\"bg-gradient-to-br from-yellow-50 to-yellow-100 rounded-lg p-6 shadow-md\">\n                  <div className=\"text-yellow-600 mb-2\">\n                    <i className=\"fas fa-coins text-3xl\"></i>\n                  </div>\n                  <h3 className=\"text-xl font-bold text-gray-800 mb-2\">98%</h3>\n                  <p className=\"text-gray-600\">Successful Financial Transformations</p>\n                </div>\n                <div className=\"bg-gradient-to-br from-yellow-50 to-yellow-100 rounded-lg p-6 shadow-md\">\n                  <div className=\"text-yellow-600 mb-2\">\n                    <i className=\"fas fa-exchange-alt text-3xl\"></i>\n                  </div>\n                  <h3 className=\"text-xl font-bold text-gray-800 mb-2\">1:42</h3>\n                  <p className=\"text-gray-600\">Gold-to-Value Conversion Rate</p>\n                </div>\n                <div className=\"bg-gradient-to-br from-yellow-50 to-yellow-100 rounded-lg p-6 shadow-md\">\n                  <div className=\"text-yellow-600 mb-2\">\n                    <i className=\"fas fa-user-graduate text-3xl\"></i>\n                  </div>\n                  <h3 className=\"text-xl font-bold text-gray-800 mb-2\">96%</h3>\n                  <p className=\"text-gray-600\">Graduate Placement Rate</p>\n                </div>\n              </div>\n            </div>\n\n            <div className=\"lg:col-span-1\">\n              <div className=\"bg-gradient-to-br from-yellow-50 to-yellow-100 rounded-lg p-6 shadow-md mb-8\">\n                <h3 className=\"text-xl font-serif font-bold mb-4 text-gray-800\">Department Philosophy</h3>\n                <div className=\"mb-6 flex justify-center\">\n                  <div className=\"w-32 h-32 rounded-full bg-gradient-to-br from-yellow-300 to-yellow-500 flex items-center justify-center\">\n                    <img\n                      src=\"https://readdy.ai/api/search-image?query=An%20alchemical%20symbol%20representing%20financial%20transformation&width=200&height=200&seq=symbol&orientation=squarish\"\n                      alt=\"Alchemical Symbol\"\n                      className=\"w-24 h-24 object-contain\"\n                    />\n                  </div>\n                </div>\n                <p className=\"text-gray-700 mb-4\">\n                  \"Through the perfect balance of ancient wisdom and modern innovation, we transform not just metals, but minds and markets.\"\n                </p>\n                <p className=\"text-gray-600 italic text-sm\">\n                  - Founding principle established by Master Goldstein, 1823\n                </p>\n              </div>\n\n              <div className=\"bg-white rounded-lg p-6 shadow-md border border-gray-200\">\n                <h3 className=\"text-xl font-serif font-bold mb-4 text-gray-800\">Quick Actions</h3>\n                <div className=\"space-y-3\">\n                  <Link to=\"/library\" className=\"w-full py-3 bg-gradient-to-r from-yellow-400 to-yellow-600 text-white rounded-full hover:from-yellow-500 hover:to-yellow-700 transition-all duration-300 flex items-center justify-center rounded-button whitespace-nowrap cursor-pointer\">\n                    <i className=\"fas fa-book-reader mr-2\"></i>\n                    Visit Library\n                  </Link>\n                  <Link to=\"/classroom\" className=\"w-full py-3 border-2 border-yellow-600 text-yellow-600 rounded-full hover:bg-yellow-50 transition-all duration-300 flex items-center justify-center rounded-button whitespace-nowrap cursor-pointer\">\n                    <i className=\"fas fa-chalkboard-teacher mr-2\"></i>\n                    Enter Classroom\n                  </Link>\n                </div>\n              </div>\n            </div>\n          </div>\n        )}\n\n        {/* Programs Section */}\n        {activeTab === 'programs' && (\n          <div>\n            <h2 className=\"text-3xl font-serif font-bold mb-8 text-gray-800\">Specialized Programs</h2>\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-8\">\n              {programs.map((program, index) => (\n                <div key={index} className=\"bg-white rounded-lg overflow-hidden shadow-lg border border-yellow-100 hover:shadow-xl transition-all duration-300 group\">\n                  <div className=\"bg-gradient-to-r from-yellow-400 to-yellow-600 h-3\"></div>\n                  <div className=\"p-8\">\n                    <div className=\"w-16 h-16 rounded-full bg-yellow-100 flex items-center justify-center mb-6\">\n                      <i className={`fas ${program.icon} text-yellow-600 text-2xl`}></i>\n                    </div>\n                    <h3 className=\"text-2xl font-serif font-bold mb-4 text-gray-800 group-hover:text-yellow-600 transition-colors duration-300\">\n                      {program.title}\n                    </h3>\n                    <p className=\"text-gray-700 mb-6\">\n                      {program.description}\n                    </p>\n                    <button className=\"px-6 py-2 border border-yellow-600 text-yellow-600 rounded-full hover:bg-yellow-600 hover:text-white transition-all duration-300 flex items-center rounded-button whitespace-nowrap cursor-pointer\">\n                      <span>View Curriculum</span>\n                      <i className=\"fas fa-arrow-right ml-2 transition-transform duration-300 group-hover:translate-x-1\"></i>\n                    </button>\n                  </div>\n                </div>\n              ))}\n            </div>\n          </div>\n        )}\n\n        {/* Faculty Section */}\n        {activeTab === 'faculty' && (\n          <div>\n            <h2 className=\"text-3xl font-serif font-bold mb-8 text-gray-800\">Distinguished Faculty</h2>\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\">\n              {facultyMembers.map((faculty, index) => (\n                <div\n                  key={index}\n                  className=\"bg-white rounded-lg overflow-hidden shadow-md hover:shadow-xl transition-all duration-300 cursor-pointer\"\n                  onClick={() => setShowFacultyDetails(showFacultyDetails === index ? null : index)}\n                >\n                  <div className=\"relative\">\n                    <img\n                      src={faculty.image}\n                      alt={faculty.name}\n                      className=\"w-full h-64 object-cover object-top\"\n                    />\n                    <div className=\"absolute inset-0 bg-gradient-to-t from-black/70 to-transparent\"></div>\n                    <div className=\"absolute bottom-0 left-0 right-0 p-4\">\n                      <h3 className=\"text-xl font-serif font-bold text-white\">{faculty.name}</h3>\n                      <p className=\"text-yellow-300\">{faculty.title}</p>\n                    </div>\n                    <div className=\"absolute top-4 right-4 w-16 h-16 rounded-full border-4 border-white bg-gradient-to-br from-yellow-400 to-yellow-600 flex items-center justify-center\">\n                      <span className=\"text-white font-bold text-sm\">{faculty.experience}</span>\n                    </div>\n                  </div>\n                  {showFacultyDetails === index && (\n                    <div className=\"p-6 bg-gray-50\">\n                      <h4 className=\"font-bold text-gray-800 mb-2\">Notable Achievements:</h4>\n                      <p className=\"text-gray-700 text-sm\">{faculty.achievements}</p>\n                    </div>\n                  )}\n                </div>\n              ))}\n            </div>\n          </div>\n        )}\n\n        {/* Careers Section */}\n        {activeTab === 'careers' && (\n          <div>\n            <h2 className=\"text-3xl font-serif font-bold mb-8 text-gray-800\">Career Opportunities</h2>\n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8\">\n              {[\n                {\n                  title: \"Magical Investment Banks\",\n                  positions: [\"Junior Gold Transmuter\", \"Market Divination Analyst\", \"Wealth Management Sorcerer\"],\n                  icon: \"fa-landmark\"\n                },\n                {\n                  title: \"Alchemical Trading Firms\",\n                  positions: [\"Metals Transformation Specialist\", \"Magical Commodities Trader\", \"Alchemical Algorithm Developer\"],\n                  icon: \"fa-exchange-alt\"\n                },\n                {\n                  title: \"Mystical Financial Consultancies\",\n                  positions: [\"Financial Forecast Diviner\", \"Wealth Protection Warder\", \"Prosperity Spell Consultant\"],\n                  icon: \"fa-hand-holding-usd\"\n                }\n              ].map((career, index) => (\n                <div key={index} className=\"bg-white rounded-lg p-6 shadow-lg border border-yellow-100\">\n                  <div className=\"w-16 h-16 rounded-full bg-yellow-100 flex items-center justify-center mb-6\">\n                    <i className={`fas ${career.icon} text-yellow-600 text-2xl`}></i>\n                  </div>\n                  <h3 className=\"text-xl font-serif font-bold mb-4 text-gray-800\">{career.title}</h3>\n                  <ul className=\"space-y-2\">\n                    {career.positions.map((position, idx) => (\n                      <li key={idx} className=\"text-gray-700 text-sm flex items-center\">\n                        <i className=\"fas fa-star text-yellow-500 mr-2 text-xs\"></i>\n                        {position}\n                      </li>\n                    ))}\n                  </ul>\n                </div>\n              ))}\n            </div>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default FinancialDeptPage;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OAASC,IAAI,KAAQ,kBAAkB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAExC,KAAM,CAAAC,iBAAiB,CAAGA,CAAA,GAAM,CAC9B,KAAM,CAACC,SAAS,CAAEC,YAAY,CAAC,CAAGT,QAAQ,CAAC,UAAU,CAAC,CACtD,KAAM,CAACU,UAAU,CAAEC,aAAa,CAAC,CAAGX,QAAQ,CAAC,KAAK,CAAC,CACnD,KAAM,CAACY,UAAU,CAAEC,aAAa,CAAC,CAAGb,QAAQ,CAAC,KAAK,CAAC,CACnD,KAAM,CAACc,kBAAkB,CAAEC,qBAAqB,CAAC,CAAGf,QAAQ,CAAC,IAAI,CAAC,CAElEC,SAAS,CAAC,IAAM,CACd,KAAM,CAAAe,YAAY,CAAGA,CAAA,GAAM,CACzB,GAAIC,MAAM,CAACC,OAAO,CAAG,EAAE,CAAE,CACvBP,aAAa,CAAC,IAAI,CAAC,CACrB,CAAC,IAAM,CACLA,aAAa,CAAC,KAAK,CAAC,CACtB,CACF,CAAC,CAEDM,MAAM,CAACE,gBAAgB,CAAC,QAAQ,CAAEH,YAAY,CAAC,CAE/C;AACA,KAAM,CAAAI,eAAe,CAAGA,CAAA,GAAM,CAC5B,KAAM,CAAAC,iBAAiB,CAAGC,QAAQ,CAACC,cAAc,CAAC,oBAAoB,CAAC,CACvE,GAAI,CAACF,iBAAiB,CAAE,OAExB,IAAK,GAAI,CAAAG,CAAC,CAAG,CAAC,CAAEA,CAAC,CAAG,EAAE,CAAEA,CAAC,EAAE,CAAE,CAC3B,KAAM,CAAAC,QAAQ,CAAGH,QAAQ,CAACI,aAAa,CAAC,KAAK,CAAC,CAC9CD,QAAQ,CAACE,SAAS,CAAG,uDAAuD,CAC5E;AACAF,QAAQ,CAACG,KAAK,CAACC,IAAI,IAAAC,MAAA,CAAMC,IAAI,CAACC,MAAM,CAAC,CAAC,CAAG,GAAG,KAAG,CAC/CP,QAAQ,CAACG,KAAK,CAACK,GAAG,IAAAH,MAAA,CAAMC,IAAI,CAACC,MAAM,CAAC,CAAC,CAAG,GAAG,KAAG,CAC9C;AACA,KAAM,CAAAE,QAAQ,CAAG,CAAC,CAAGH,IAAI,CAACC,MAAM,CAAC,CAAC,CAAG,CAAC,CACtCP,QAAQ,CAACG,KAAK,CAACO,SAAS,UAAAL,MAAA,CAAYI,QAAQ,0BAAwB,CACpET,QAAQ,CAACG,KAAK,CAACQ,cAAc,IAAAN,MAAA,CAAMC,IAAI,CAACC,MAAM,CAAC,CAAC,CAAG,CAAC,KAAG,CACvDX,iBAAiB,CAACgB,WAAW,CAACZ,QAAQ,CAAC,CACzC,CACF,CAAC,CAEDL,eAAe,CAAC,CAAC,CAEjB,MAAO,IAAM,CACXH,MAAM,CAACqB,mBAAmB,CAAC,QAAQ,CAAEtB,YAAY,CAAC,CACpD,CAAC,CACH,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAuB,cAAc,CAAG,CACrB,CACEC,IAAI,CAAE,qBAAqB,CAC3BC,KAAK,CAAE,2BAA2B,CAClCC,UAAU,CAAE,UAAU,CACtBC,YAAY,CAAE,0EAA0E,CACxFC,KAAK,CAAE,8NACT,CAAC,CACD,CACEJ,IAAI,CAAE,eAAe,CACrBC,KAAK,CAAE,wBAAwB,CAC/BC,UAAU,CAAE,UAAU,CACtBC,YAAY,CAAE,4EAA4E,CAC1FC,KAAK,CAAE,8LACT,CAAC,CACD,CACEJ,IAAI,CAAE,mBAAmB,CACzBC,KAAK,CAAE,4BAA4B,CACnCC,UAAU,CAAE,UAAU,CACtBC,YAAY,CAAE,0FAA0F,CACxGC,KAAK,CAAE,mNACT,CAAC,CACD,CACEJ,IAAI,CAAE,cAAc,CACpBC,KAAK,CAAE,8BAA8B,CACrCC,UAAU,CAAE,UAAU,CACtBC,YAAY,CAAE,gGAAgG,CAC9GC,KAAK,CAAE,8LACT,CAAC,CACF,CAED,KAAM,CAAAC,QAAQ,CAAG,CACf,CACEJ,KAAK,CAAE,+BAA+B,CACtCK,WAAW,CAAE,iIAAiI,CAC9IC,IAAI,CAAE,eACR,CAAC,CACD,CACEN,KAAK,CAAE,oBAAoB,CAC3BK,WAAW,CAAE,2HAA2H,CACxIC,IAAI,CAAE,iBACR,CAAC,CACD,CACEN,KAAK,CAAE,0BAA0B,CACjCK,WAAW,CAAE,6GAA6G,CAC1HC,IAAI,CAAE,eACR,CAAC,CACD,CACEN,KAAK,CAAE,gCAAgC,CACvCK,WAAW,CAAE,qHAAqH,CAClIC,IAAI,CAAE,WACR,CAAC,CACF,CAED,mBACEzC,KAAA,QAAKqB,SAAS,CAAC,qDAAqD,CAAAqB,QAAA,eAElE5C,IAAA,QAAK6C,EAAE,CAAC,oBAAoB,CAACtB,SAAS,CAAC,uCAAuC,CAAM,CAAC,cAGrFvB,IAAA,QAAKuB,SAAS,CAAC,kBAAkB,CAAAqB,QAAA,cAC/B5C,IAAA,QAAKuB,SAAS,CAAC,wBAAwB,CAAAqB,QAAA,cACrC1C,KAAA,QAAKqB,SAAS,CAAC,yCAAyC,CAAAqB,QAAA,eACtD5C,IAAA,CAACF,IAAI,EAACgD,EAAE,CAAC,GAAG,CAACvB,SAAS,CAAC,oEAAoE,CAAAqB,QAAA,CAAC,MAAI,CAAM,CAAC,cACvG5C,IAAA,MAAGuB,SAAS,CAAC,iDAAiD,CAAI,CAAC,cACnEvB,IAAA,CAACF,IAAI,EAACgD,EAAE,CAAC,cAAc,CAACvB,SAAS,CAAC,oEAAoE,CAAAqB,QAAA,CAAC,aAAW,CAAM,CAAC,cACzH5C,IAAA,MAAGuB,SAAS,CAAC,iDAAiD,CAAI,CAAC,cACnEvB,IAAA,SAAMuB,SAAS,CAAC,gBAAgB,CAAAqB,QAAA,CAAC,mCAAiC,CAAM,CAAC,EACtE,CAAC,CACH,CAAC,CACH,CAAC,cAGN1C,KAAA,QAAKqB,SAAS,CAAC,iDAAiD,CAAAqB,QAAA,eAC9D5C,IAAA,QACE+C,GAAG,CAAC,4RAA4R,CAChSC,GAAG,CAAC,sBAAsB,CAC1BzB,SAAS,CAAC,4BAA4B,CACvC,CAAC,cACFvB,IAAA,QAAKuB,SAAS,CAAC,gEAAgE,CAAM,CAAC,cACtFvB,IAAA,QAAKuB,SAAS,CAAC,gFAAgF,CAAM,CAAC,cACtGvB,IAAA,QAAKuB,SAAS,CAAC,oCAAoC,CAAAqB,QAAA,cACjD1C,KAAA,QAAKqB,SAAS,CAAC,wBAAwB,CAAAqB,QAAA,eACrC1C,KAAA,OAAIqB,SAAS,CAAC,uEAAuE,CAAAqB,QAAA,EAAC,uBAC/D,cAAA5C,IAAA,SAAMuB,SAAS,CAAC,8EAA8E,CAAAqB,QAAA,CAAC,cAAY,CAAM,CAAC,EACrI,CAAC,cACL5C,IAAA,MAAGuB,SAAS,CAAC,0DAA0D,CAAAqB,QAAA,CAAC,oIAExE,CAAG,CAAC,EACD,CAAC,CACH,CAAC,EACH,CAAC,cAGN1C,KAAA,QAAKqB,SAAS,CAAC,8BAA8B,CAAAqB,QAAA,eAE3C5C,IAAA,QAAKuB,SAAS,CAAC,+CAA+C,CAAAqB,QAAA,CAC3D,CAAC,UAAU,CAAE,UAAU,CAAE,SAAS,CAAE,SAAS,CAAC,CAACK,GAAG,CAAEC,GAAG,eACtDlD,IAAA,WAEEmD,OAAO,CAAEA,CAAA,GAAM9C,YAAY,CAAC6C,GAAG,CAAE,CACjC3B,SAAS,iHAAAG,MAAA,CACPtB,SAAS,GAAK8C,GAAG,CACb,4CAA4C,CAC5C,oCAAoC,CACvC,CAAAN,QAAA,CAEFM,GAAG,CAACE,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAAGH,GAAG,CAACI,KAAK,CAAC,CAAC,CAAC,EARtCJ,GASC,CACT,CAAC,CACC,CAAC,CAGL9C,SAAS,GAAK,UAAU,eACvBF,KAAA,QAAKqB,SAAS,CAAC,wCAAwC,CAAAqB,QAAA,eACrD1C,KAAA,QAAKqB,SAAS,CAAC,eAAe,CAAAqB,QAAA,eAC5B5C,IAAA,OAAIuB,SAAS,CAAC,kDAAkD,CAAAqB,QAAA,CAAC,qBAAmB,CAAI,CAAC,cACzF5C,IAAA,MAAGuB,SAAS,CAAC,oCAAoC,CAAAqB,QAAA,CAAC,gWAElD,CAAG,CAAC,cACJ5C,IAAA,MAAGuB,SAAS,CAAC,oCAAoC,CAAAqB,QAAA,CAAC,6RAElD,CAAG,CAAC,cAEJ1C,KAAA,QAAKqB,SAAS,CAAC,4CAA4C,CAAAqB,QAAA,eACzD1C,KAAA,QAAKqB,SAAS,CAAC,yEAAyE,CAAAqB,QAAA,eACtF5C,IAAA,QAAKuB,SAAS,CAAC,sBAAsB,CAAAqB,QAAA,cACnC5C,IAAA,MAAGuB,SAAS,CAAC,uBAAuB,CAAI,CAAC,CACtC,CAAC,cACNvB,IAAA,OAAIuB,SAAS,CAAC,sCAAsC,CAAAqB,QAAA,CAAC,KAAG,CAAI,CAAC,cAC7D5C,IAAA,MAAGuB,SAAS,CAAC,eAAe,CAAAqB,QAAA,CAAC,sCAAoC,CAAG,CAAC,EAClE,CAAC,cACN1C,KAAA,QAAKqB,SAAS,CAAC,yEAAyE,CAAAqB,QAAA,eACtF5C,IAAA,QAAKuB,SAAS,CAAC,sBAAsB,CAAAqB,QAAA,cACnC5C,IAAA,MAAGuB,SAAS,CAAC,8BAA8B,CAAI,CAAC,CAC7C,CAAC,cACNvB,IAAA,OAAIuB,SAAS,CAAC,sCAAsC,CAAAqB,QAAA,CAAC,MAAI,CAAI,CAAC,cAC9D5C,IAAA,MAAGuB,SAAS,CAAC,eAAe,CAAAqB,QAAA,CAAC,+BAA6B,CAAG,CAAC,EAC3D,CAAC,cACN1C,KAAA,QAAKqB,SAAS,CAAC,yEAAyE,CAAAqB,QAAA,eACtF5C,IAAA,QAAKuB,SAAS,CAAC,sBAAsB,CAAAqB,QAAA,cACnC5C,IAAA,MAAGuB,SAAS,CAAC,+BAA+B,CAAI,CAAC,CAC9C,CAAC,cACNvB,IAAA,OAAIuB,SAAS,CAAC,sCAAsC,CAAAqB,QAAA,CAAC,KAAG,CAAI,CAAC,cAC7D5C,IAAA,MAAGuB,SAAS,CAAC,eAAe,CAAAqB,QAAA,CAAC,yBAAuB,CAAG,CAAC,EACrD,CAAC,EACH,CAAC,EACH,CAAC,cAEN1C,KAAA,QAAKqB,SAAS,CAAC,eAAe,CAAAqB,QAAA,eAC5B1C,KAAA,QAAKqB,SAAS,CAAC,8EAA8E,CAAAqB,QAAA,eAC3F5C,IAAA,OAAIuB,SAAS,CAAC,iDAAiD,CAAAqB,QAAA,CAAC,uBAAqB,CAAI,CAAC,cAC1F5C,IAAA,QAAKuB,SAAS,CAAC,0BAA0B,CAAAqB,QAAA,cACvC5C,IAAA,QAAKuB,SAAS,CAAC,yGAAyG,CAAAqB,QAAA,cACtH5C,IAAA,QACE+C,GAAG,CAAC,oKAAoK,CACxKC,GAAG,CAAC,mBAAmB,CACvBzB,SAAS,CAAC,0BAA0B,CACrC,CAAC,CACC,CAAC,CACH,CAAC,cACNvB,IAAA,MAAGuB,SAAS,CAAC,oBAAoB,CAAAqB,QAAA,CAAC,+HAElC,CAAG,CAAC,cACJ5C,IAAA,MAAGuB,SAAS,CAAC,8BAA8B,CAAAqB,QAAA,CAAC,4DAE5C,CAAG,CAAC,EACD,CAAC,cAEN1C,KAAA,QAAKqB,SAAS,CAAC,0DAA0D,CAAAqB,QAAA,eACvE5C,IAAA,OAAIuB,SAAS,CAAC,iDAAiD,CAAAqB,QAAA,CAAC,eAAa,CAAI,CAAC,cAClF1C,KAAA,QAAKqB,SAAS,CAAC,WAAW,CAAAqB,QAAA,eACxB1C,KAAA,CAACJ,IAAI,EAACgD,EAAE,CAAC,UAAU,CAACvB,SAAS,CAAC,2OAA2O,CAAAqB,QAAA,eACvQ5C,IAAA,MAAGuB,SAAS,CAAC,yBAAyB,CAAI,CAAC,gBAE7C,EAAM,CAAC,cACPrB,KAAA,CAACJ,IAAI,EAACgD,EAAE,CAAC,YAAY,CAACvB,SAAS,CAAC,qMAAqM,CAAAqB,QAAA,eACnO5C,IAAA,MAAGuB,SAAS,CAAC,gCAAgC,CAAI,CAAC,kBAEpD,EAAM,CAAC,EACJ,CAAC,EACH,CAAC,EACH,CAAC,EACH,CACN,CAGAnB,SAAS,GAAK,UAAU,eACvBF,KAAA,QAAA0C,QAAA,eACE5C,IAAA,OAAIuB,SAAS,CAAC,kDAAkD,CAAAqB,QAAA,CAAC,sBAAoB,CAAI,CAAC,cAC1F5C,IAAA,QAAKuB,SAAS,CAAC,uCAAuC,CAAAqB,QAAA,CACnDH,QAAQ,CAACQ,GAAG,CAAC,CAACM,OAAO,CAAEC,KAAK,gBAC3BtD,KAAA,QAAiBqB,SAAS,CAAC,0HAA0H,CAAAqB,QAAA,eACnJ5C,IAAA,QAAKuB,SAAS,CAAC,oDAAoD,CAAM,CAAC,cAC1ErB,KAAA,QAAKqB,SAAS,CAAC,KAAK,CAAAqB,QAAA,eAClB5C,IAAA,QAAKuB,SAAS,CAAC,4EAA4E,CAAAqB,QAAA,cACzF5C,IAAA,MAAGuB,SAAS,QAAAG,MAAA,CAAS6B,OAAO,CAACZ,IAAI,6BAA4B,CAAI,CAAC,CAC/D,CAAC,cACN3C,IAAA,OAAIuB,SAAS,CAAC,6GAA6G,CAAAqB,QAAA,CACxHW,OAAO,CAAClB,KAAK,CACZ,CAAC,cACLrC,IAAA,MAAGuB,SAAS,CAAC,oBAAoB,CAAAqB,QAAA,CAC9BW,OAAO,CAACb,WAAW,CACnB,CAAC,cACJxC,KAAA,WAAQqB,SAAS,CAAC,oMAAoM,CAAAqB,QAAA,eACpN5C,IAAA,SAAA4C,QAAA,CAAM,iBAAe,CAAM,CAAC,cAC5B5C,IAAA,MAAGuB,SAAS,CAAC,qFAAqF,CAAI,CAAC,EACjG,CAAC,EACN,CAAC,GAhBEiC,KAiBL,CACN,CAAC,CACC,CAAC,EACH,CACN,CAGApD,SAAS,GAAK,SAAS,eACtBF,KAAA,QAAA0C,QAAA,eACE5C,IAAA,OAAIuB,SAAS,CAAC,kDAAkD,CAAAqB,QAAA,CAAC,uBAAqB,CAAI,CAAC,cAC3F5C,IAAA,QAAKuB,SAAS,CAAC,sDAAsD,CAAAqB,QAAA,CAClET,cAAc,CAACc,GAAG,CAAC,CAACQ,OAAO,CAAED,KAAK,gBACjCtD,KAAA,QAEEqB,SAAS,CAAC,0GAA0G,CACpH4B,OAAO,CAAEA,CAAA,GAAMxC,qBAAqB,CAACD,kBAAkB,GAAK8C,KAAK,CAAG,IAAI,CAAGA,KAAK,CAAE,CAAAZ,QAAA,eAElF1C,KAAA,QAAKqB,SAAS,CAAC,UAAU,CAAAqB,QAAA,eACvB5C,IAAA,QACE+C,GAAG,CAAEU,OAAO,CAACjB,KAAM,CACnBQ,GAAG,CAAES,OAAO,CAACrB,IAAK,CAClBb,SAAS,CAAC,qCAAqC,CAChD,CAAC,cACFvB,IAAA,QAAKuB,SAAS,CAAC,gEAAgE,CAAM,CAAC,cACtFrB,KAAA,QAAKqB,SAAS,CAAC,sCAAsC,CAAAqB,QAAA,eACnD5C,IAAA,OAAIuB,SAAS,CAAC,yCAAyC,CAAAqB,QAAA,CAAEa,OAAO,CAACrB,IAAI,CAAK,CAAC,cAC3EpC,IAAA,MAAGuB,SAAS,CAAC,iBAAiB,CAAAqB,QAAA,CAAEa,OAAO,CAACpB,KAAK,CAAI,CAAC,EAC/C,CAAC,cACNrC,IAAA,QAAKuB,SAAS,CAAC,sJAAsJ,CAAAqB,QAAA,cACnK5C,IAAA,SAAMuB,SAAS,CAAC,8BAA8B,CAAAqB,QAAA,CAAEa,OAAO,CAACnB,UAAU,CAAO,CAAC,CACvE,CAAC,EACH,CAAC,CACL5B,kBAAkB,GAAK8C,KAAK,eAC3BtD,KAAA,QAAKqB,SAAS,CAAC,gBAAgB,CAAAqB,QAAA,eAC7B5C,IAAA,OAAIuB,SAAS,CAAC,8BAA8B,CAAAqB,QAAA,CAAC,uBAAqB,CAAI,CAAC,cACvE5C,IAAA,MAAGuB,SAAS,CAAC,uBAAuB,CAAAqB,QAAA,CAAEa,OAAO,CAAClB,YAAY,CAAI,CAAC,EAC5D,CACN,GAxBIiB,KAyBF,CACN,CAAC,CACC,CAAC,EACH,CACN,CAGApD,SAAS,GAAK,SAAS,eACtBF,KAAA,QAAA0C,QAAA,eACE5C,IAAA,OAAIuB,SAAS,CAAC,kDAAkD,CAAAqB,QAAA,CAAC,sBAAoB,CAAI,CAAC,cAC1F5C,IAAA,QAAKuB,SAAS,CAAC,uCAAuC,CAAAqB,QAAA,CACnD,CACC,CACEP,KAAK,CAAE,0BAA0B,CACjCqB,SAAS,CAAE,CAAC,wBAAwB,CAAE,2BAA2B,CAAE,4BAA4B,CAAC,CAChGf,IAAI,CAAE,aACR,CAAC,CACD,CACEN,KAAK,CAAE,0BAA0B,CACjCqB,SAAS,CAAE,CAAC,kCAAkC,CAAE,4BAA4B,CAAE,gCAAgC,CAAC,CAC/Gf,IAAI,CAAE,iBACR,CAAC,CACD,CACEN,KAAK,CAAE,kCAAkC,CACzCqB,SAAS,CAAE,CAAC,4BAA4B,CAAE,0BAA0B,CAAE,6BAA6B,CAAC,CACpGf,IAAI,CAAE,qBACR,CAAC,CACF,CAACM,GAAG,CAAC,CAACU,MAAM,CAAEH,KAAK,gBAClBtD,KAAA,QAAiBqB,SAAS,CAAC,4DAA4D,CAAAqB,QAAA,eACrF5C,IAAA,QAAKuB,SAAS,CAAC,4EAA4E,CAAAqB,QAAA,cACzF5C,IAAA,MAAGuB,SAAS,QAAAG,MAAA,CAASiC,MAAM,CAAChB,IAAI,6BAA4B,CAAI,CAAC,CAC9D,CAAC,cACN3C,IAAA,OAAIuB,SAAS,CAAC,iDAAiD,CAAAqB,QAAA,CAAEe,MAAM,CAACtB,KAAK,CAAK,CAAC,cACnFrC,IAAA,OAAIuB,SAAS,CAAC,WAAW,CAAAqB,QAAA,CACtBe,MAAM,CAACD,SAAS,CAACT,GAAG,CAAC,CAACW,QAAQ,CAAEC,GAAG,gBAClC3D,KAAA,OAAcqB,SAAS,CAAC,yCAAyC,CAAAqB,QAAA,eAC/D5C,IAAA,MAAGuB,SAAS,CAAC,0CAA0C,CAAI,CAAC,CAC3DqC,QAAQ,GAFFC,GAGL,CACL,CAAC,CACA,CAAC,GAZGL,KAaL,CACN,CAAC,CACC,CAAC,EACH,CACN,EACE,CAAC,EACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAArD,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}