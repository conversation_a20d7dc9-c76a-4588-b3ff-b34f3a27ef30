{"ast": null, "code": "import React from'react';import{BrowserRouter as Router,Routes,Route}from'react-router-dom';import Navigation from'./components/Navigation';import MainPage from'./pages/MainPage';import DepartmentPages from'./pages/DepartmentPages';import LibraryPages from'./pages/LibraryPages';import ClassroomPage from'./pages/ClassroomPage';import UnifiedDashboard from'./components/UnifiedDashboard';import'./App.css';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";function App(){return/*#__PURE__*/_jsx(Router,{children:/*#__PURE__*/_jsxs(\"div\",{className:\"App\",children:[/*#__PURE__*/_jsx(Navigation,{}),/*#__PURE__*/_jsxs(Routes,{children:[/*#__PURE__*/_jsx(Route,{path:\"/\",element:/*#__PURE__*/_jsx(MainPage,{})}),/*#__PURE__*/_jsx(Route,{path:\"/departments\",element:/*#__PURE__*/_jsx(DepartmentPages,{})}),/*#__PURE__*/_jsx(Route,{path:\"/departments/:deptName\",element:/*#__PURE__*/_jsx(DepartmentPages,{})}),/*#__PURE__*/_jsx(Route,{path:\"/library\",element:/*#__PURE__*/_jsx(LibraryPages,{})}),/*#__PURE__*/_jsx(Route,{path:\"/classroom\",element:/*#__PURE__*/_jsx(ClassroomPage,{})}),/*#__PURE__*/_jsx(Route,{path:\"/dashboard\",element:/*#__PURE__*/_jsx(UnifiedDashboard,{})}),/*#__PURE__*/_jsx(Route,{path:\"/FinanceDept\",element:/*#__PURE__*/_jsx(DepartmentPages,{deptType:\"financial\"})}),/*#__PURE__*/_jsx(Route,{path:\"/ConstructionDept\",element:/*#__PURE__*/_jsx(DepartmentPages,{deptType:\"construction\"})}),/*#__PURE__*/_jsx(Route,{path:\"/TowerTechnicians\",element:/*#__PURE__*/_jsx(DepartmentPages,{deptType:\"tower\"})}),/*#__PURE__*/_jsx(Route,{path:\"/HumanRelationsDept\",element:/*#__PURE__*/_jsx(DepartmentPages,{deptType:\"hr\"})}),/*#__PURE__*/_jsx(Route,{path:\"/SalesDept\",element:/*#__PURE__*/_jsx(DepartmentPages,{deptType:\"sales\"})}),/*#__PURE__*/_jsx(Route,{path:\"/NetworkOperations\",element:/*#__PURE__*/_jsx(DepartmentPages,{deptType:\"network\"})}),/*#__PURE__*/_jsx(Route,{path:\"/LeadershipTeam\",element:/*#__PURE__*/_jsx(DepartmentPages,{deptType:\"leadership\"})})]})]})});}export default App;", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "Navigation", "MainPage", "DepartmentPages", "LibraryPages", "ClassroomPage", "UnifiedDashboard", "jsx", "_jsx", "jsxs", "_jsxs", "App", "children", "className", "path", "element", "deptType"], "sources": ["/Users/<USER>/Documents/FatbeamU/react-app/src/App.js"], "sourcesContent": ["import React from 'react';\nimport { BrowserRouter as Router, Routes, Route } from 'react-router-dom';\nimport Navigation from './components/Navigation';\nimport MainPage from './pages/MainPage';\nimport DepartmentPages from './pages/DepartmentPages';\nimport LibraryPages from './pages/LibraryPages';\nimport ClassroomPage from './pages/ClassroomPage';\nimport UnifiedDashboard from './components/UnifiedDashboard';\nimport './App.css';\n\nfunction App() {\n  return (\n    <Router>\n      <div className=\"App\">\n        <Navigation />\n        <Routes>\n          <Route path=\"/\" element={<MainPage />} />\n          <Route path=\"/departments\" element={<DepartmentPages />} />\n          <Route path=\"/departments/:deptName\" element={<DepartmentPages />} />\n          <Route path=\"/library\" element={<LibraryPages />} />\n          <Route path=\"/classroom\" element={<ClassroomPage />} />\n          <Route path=\"/dashboard\" element={<UnifiedDashboard />} />\n          {/* Department-specific routes */}\n          <Route path=\"/FinanceDept\" element={<DepartmentPages deptType=\"financial\" />} />\n          <Route path=\"/ConstructionDept\" element={<DepartmentPages deptType=\"construction\" />} />\n          <Route path=\"/TowerTechnicians\" element={<DepartmentPages deptType=\"tower\" />} />\n          <Route path=\"/HumanRelationsDept\" element={<DepartmentPages deptType=\"hr\" />} />\n          <Route path=\"/SalesDept\" element={<DepartmentPages deptType=\"sales\" />} />\n          <Route path=\"/NetworkOperations\" element={<DepartmentPages deptType=\"network\" />} />\n          <Route path=\"/LeadershipTeam\" element={<DepartmentPages deptType=\"leadership\" />} />\n        </Routes>\n      </div>\n    </Router>\n  );\n}\n\nexport default App;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OAASC,aAAa,GAAI,CAAAC,MAAM,CAAEC,MAAM,CAAEC,KAAK,KAAQ,kBAAkB,CACzE,MAAO,CAAAC,UAAU,KAAM,yBAAyB,CAChD,MAAO,CAAAC,QAAQ,KAAM,kBAAkB,CACvC,MAAO,CAAAC,eAAe,KAAM,yBAAyB,CACrD,MAAO,CAAAC,YAAY,KAAM,sBAAsB,CAC/C,MAAO,CAAAC,aAAa,KAAM,uBAAuB,CACjD,MAAO,CAAAC,gBAAgB,KAAM,+BAA+B,CAC5D,MAAO,WAAW,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEnB,QAAS,CAAAC,GAAGA,CAAA,CAAG,CACb,mBACEH,IAAA,CAACV,MAAM,EAAAc,QAAA,cACLF,KAAA,QAAKG,SAAS,CAAC,KAAK,CAAAD,QAAA,eAClBJ,IAAA,CAACP,UAAU,GAAE,CAAC,cACdS,KAAA,CAACX,MAAM,EAAAa,QAAA,eACLJ,IAAA,CAACR,KAAK,EAACc,IAAI,CAAC,GAAG,CAACC,OAAO,cAAEP,IAAA,CAACN,QAAQ,GAAE,CAAE,CAAE,CAAC,cACzCM,IAAA,CAACR,KAAK,EAACc,IAAI,CAAC,cAAc,CAACC,OAAO,cAAEP,IAAA,CAACL,eAAe,GAAE,CAAE,CAAE,CAAC,cAC3DK,IAAA,CAACR,KAAK,EAACc,IAAI,CAAC,wBAAwB,CAACC,OAAO,cAAEP,IAAA,CAACL,eAAe,GAAE,CAAE,CAAE,CAAC,cACrEK,IAAA,CAACR,KAAK,EAACc,IAAI,CAAC,UAAU,CAACC,OAAO,cAAEP,IAAA,CAACJ,YAAY,GAAE,CAAE,CAAE,CAAC,cACpDI,IAAA,CAACR,KAAK,EAACc,IAAI,CAAC,YAAY,CAACC,OAAO,cAAEP,IAAA,CAACH,aAAa,GAAE,CAAE,CAAE,CAAC,cACvDG,IAAA,CAACR,KAAK,EAACc,IAAI,CAAC,YAAY,CAACC,OAAO,cAAEP,IAAA,CAACF,gBAAgB,GAAE,CAAE,CAAE,CAAC,cAE1DE,IAAA,CAACR,KAAK,EAACc,IAAI,CAAC,cAAc,CAACC,OAAO,cAAEP,IAAA,CAACL,eAAe,EAACa,QAAQ,CAAC,WAAW,CAAE,CAAE,CAAE,CAAC,cAChFR,IAAA,CAACR,KAAK,EAACc,IAAI,CAAC,mBAAmB,CAACC,OAAO,cAAEP,IAAA,CAACL,eAAe,EAACa,QAAQ,CAAC,cAAc,CAAE,CAAE,CAAE,CAAC,cACxFR,IAAA,CAACR,KAAK,EAACc,IAAI,CAAC,mBAAmB,CAACC,OAAO,cAAEP,IAAA,CAACL,eAAe,EAACa,QAAQ,CAAC,OAAO,CAAE,CAAE,CAAE,CAAC,cACjFR,IAAA,CAACR,KAAK,EAACc,IAAI,CAAC,qBAAqB,CAACC,OAAO,cAAEP,IAAA,CAACL,eAAe,EAACa,QAAQ,CAAC,IAAI,CAAE,CAAE,CAAE,CAAC,cAChFR,IAAA,CAACR,KAAK,EAACc,IAAI,CAAC,YAAY,CAACC,OAAO,cAAEP,IAAA,CAACL,eAAe,EAACa,QAAQ,CAAC,OAAO,CAAE,CAAE,CAAE,CAAC,cAC1ER,IAAA,CAACR,KAAK,EAACc,IAAI,CAAC,oBAAoB,CAACC,OAAO,cAAEP,IAAA,CAACL,eAAe,EAACa,QAAQ,CAAC,SAAS,CAAE,CAAE,CAAE,CAAC,cACpFR,IAAA,CAACR,KAAK,EAACc,IAAI,CAAC,iBAAiB,CAACC,OAAO,cAAEP,IAAA,CAACL,eAAe,EAACa,QAAQ,CAAC,YAAY,CAAE,CAAE,CAAE,CAAC,EAC9E,CAAC,EACN,CAAC,CACA,CAAC,CAEb,CAEA,cAAe,CAAAL,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}