{"ast": null, "code": "import React,{useState,useEffect}from'react';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const LibraryPages=()=>{const[bookmarkedItems,setBookmarkedItems]=useState([]);const[checkedOutItems,setCheckedOutItems]=useState([]);const[selectedCategory,setSelectedCategory]=useState('all');const[selectedDifficulty,setSelectedDifficulty]=useState('all');const[searchQuery,setSearchQuery]=useState('');const[showBookDetails,setShowBookDetails]=useState(false);const[selectedBook,setSelectedBook]=useState(null);const[filteredBooks,setFilteredBooks]=useState([]);const[hoveredBook,setHoveredBook]=useState(null);useEffect(()=>{const handleClickOutside=()=>{setHoveredBook(null);};document.addEventListener('click',handleClickOutside);return()=>{document.removeEventListener('click',handleClickOutside);};},[]);// Add styles for book animation\nuseEffect(()=>{const style=document.createElement('style');style.textContent=\"\\n    .transform-origin-left {\\n      transform-origin: left;\\n    }\\n    \";document.head.appendChild(style);return()=>{document.head.removeChild(style);};},[]);const allBooks=[{id:'b1',title:'Network Protocols Overview',author:'Professor Elara Waveweaver',description:'A comprehensive guide to network protocols and their implementations in enterprise networks.',difficulty:'intermediate',category:'protocols',wikiLink:'/networking/protocols/overview',isUnique:false,spineColor:'bg-gradient-to-r from-[#3E2723] via-[#4E342E] to-[#3E2723]',textColor:'text-[#C0C0C0]',coverImage:'https://readdy.ai/api/search-image?query=Ancient%20magical%20book%20cover%20with%20network%20patterns%20and%20ethereal%20glowing%20symbols%20floating%20on%20a%20rich%20leather%20background%2C%20professional%20photography%20with%20dramatic%20lighting&width=400&height=600&seq=book1&orientation=portrait'},{id:'b5',title:'Fatbeam Rural Network Solutions',author:'Dr. Mystic Bytecaster',description:'Unique case study on implementing high-speed networks in rural communities with Fatbeam solutions.',difficulty:'advanced',category:'protocols',wikiLink:'/fatbeam/case-studies/rural-networks',isUnique:true,spineColor:'bg-gradient-to-r from-[#8B7355] via-[#A0856E] to-[#8B7355]',textColor:'text-[#D4AF37]',coverImage:'https://readdy.ai/api/search-image?query=Mystical%20book%20cover%20featuring%20flowing%20data%20streams%20and%20magical%20energy%20patterns%20on%20aged%20leather%20with%20gold%20accents%2C%20professional%20studio%20lighting&width=400&height=600&seq=book5&orientation=portrait'},{id:'b6',title:'Quantum Enchantment',author:'Sage Quantumweaver',description:'Advanced concepts in quantum networking and magical entanglement.',difficulty:'advanced',category:'fundamentals',spineColor:'bg-gradient-to-r from-[#5D4037] via-[#6D4C41] to-[#5D4037]',textColor:'text-[#C0C0C0]',coverImage:'https://readdy.ai/api/search-image?query=Quantum%20physics%20inspired%20magical%20book%20cover%20with%20intricate%20geometric%20patterns%20on%20deep%20purple%20leather%2C%20professional%20photography&width=400&height=600&seq=book6&orientation=portrait'},{id:'b7',title:'Runic Network Defense',author:'Guardian Shieldmaster',description:'Essential protection spells for magical network security.',difficulty:'intermediate',category:'security',spineColor:'bg-gradient-to-r from-[#4E342E] via-[#5D4037] to-[#4E342E]',textColor:'text-[#C0C0C0]',coverImage:'https://readdy.ai/api/search-image?query=Ancient%20protective%20runes%20and%20magical%20shields%20on%20dark%20green%20leather%20book%20cover%20with%20metallic%20embellishments%2C%20professional%20studio%20lighting&width=400&height=600&seq=book7&orientation=portrait'},{id:'b8',title:'Celestial Connections',author:'Astral Networker',description:'Establishing and maintaining connections through celestial planes.',difficulty:'advanced',category:'protocols',spineColor:'bg-gradient-to-r from-[#3E2723] via-[#4E342E] to-[#3E2723]',textColor:'text-[#C0C0C0]',coverImage:'https://readdy.ai/api/search-image?query=Celestial%20map%20and%20constellation%20patterns%20on%20midnight%20blue%20leather%20book%20cover%20with%20silver%20details%2C%20professional%20photography%20with%20dramatic%20lighting&width=400&height=600&seq=book8&orientation=portrait'},{id:'b2',title:'Troubleshooting the Ethereal Web',author:'Dr. Thorne Cablemancer',description:'Learn the art of identifying and resolving network disruptions across magical connections.',difficulty:'intermediate',category:'troubleshooting',spineColor:'bg-[#2A1B0E]',textColor:'text-[#C0C0C0]',coverImage:'https://readdy.ai/api/search-image?query=Mystical%20book%20cover%20featuring%20intricate%20network%20troubleshooting%20diagrams%20with%20magical%20aura%2C%20ancient%20leather%20texture%20background%20with%20professional%20studio%20lighting&width=400&height=600&seq=book2&orientation=portrait'},{id:'b3',title:'Fundamentals of Magical Networking',author:'Sage Bytewarden',description:'Essential concepts and principles of magical network architecture for beginners.',difficulty:'beginner',category:'fundamentals',spineColor:'bg-[#2A1B0E]',textColor:'text-[#C0C0C0]',coverImage:'https://readdy.ai/api/search-image?query=Enchanted%20book%20cover%20with%20basic%20networking%20symbols%20and%20magical%20runes%2C%20elegant%20leather%20binding%20with%20soft%20mystical%20glow%2C%20professional%20product%20photography&width=400&height=600&seq=book3&orientation=portrait'},{id:'b4',title:'Advanced Spellbound Security',author:'Master Cryptkeeper',description:'Advanced techniques for securing magical networks against dark forces.',difficulty:'advanced',category:'security',spineColor:'bg-[#2A1B0E]',textColor:'text-[#C0C0C0]',coverImage:'https://readdy.ai/api/search-image?query=Dark%20mysterious%20book%20cover%20with%20security%20sigils%20and%20protective%20magical%20barriers%2C%20ancient%20leather%20texture%20with%20metallic%20accents%2C%20professional%20studio%20lighting&width=400&height=600&seq=book4&orientation=portrait'}];useEffect(()=>{let results=allBooks;if(searchQuery){const query=searchQuery.toLowerCase();results=results.filter(book=>book.title.toLowerCase().includes(query)||book.author.toLowerCase().includes(query)||book.description.toLowerCase().includes(query));}if(selectedCategory!=='all'){results=results.filter(book=>book.category===selectedCategory);}if(selectedDifficulty!=='all'){results=results.filter(book=>book.difficulty===selectedDifficulty);}setFilteredBooks(results);},[searchQuery,selectedCategory,selectedDifficulty]);const BookSpine=_ref=>{let{book}=_ref;return/*#__PURE__*/_jsx(\"div\",{className:\"h-[300px] w-[60px] \".concat(book.spineColor,\" cursor-pointer transition-all duration-500 hover:scale-105 hover:z-10 relative shadow-xl overflow-hidden group\"),onMouseEnter:()=>setHoveredBook(book),onMouseLeave:()=>setHoveredBook(null),onClick:()=>{setSelectedBook(book);setShowBookDetails(true);},children:/*#__PURE__*/_jsx(\"div\",{className:\"absolute inset-0 flex items-center justify-center\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"absolute inset-0\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"absolute top-0 left-0 right-0 h-[8px] bg-gradient-to-b from-black/50 to-transparent\"}),/*#__PURE__*/_jsx(\"div\",{className:\"absolute bottom-0 left-0 right-0 h-[8px] bg-gradient-to-t from-black/50 to-transparent\"}),/*#__PURE__*/_jsx(\"div\",{className:\"absolute inset-y-0 left-0 w-[4px] bg-gradient-to-r from-black/50 to-transparent\"}),/*#__PURE__*/_jsx(\"div\",{className:\"absolute inset-y-0 right-0 w-[4px] bg-gradient-to-l from-black/50 to-transparent\"}),/*#__PURE__*/_jsx(\"div\",{className:\"absolute inset-0 flex items-center justify-center\",children:/*#__PURE__*/_jsx(\"span\",{className:\"\".concat(book.textColor,\" text-sm font-serif vertical-text whitespace-nowrap transform -rotate-90 px-4 z-10 group-hover:scale-105 transition-transform duration-300 drop-shadow-lg tracking-wider\"),children:book.title})})]})})});};return/*#__PURE__*/_jsxs(\"div\",{className:\"min-h-screen bg-[#0a0500] pt-20\",children:[/*#__PURE__*/_jsx(\"header\",{className:\"bg-[#1a0f00] border-b border-[#3a2a15] p-6\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"container mx-auto flex justify-between items-center\",children:[/*#__PURE__*/_jsx(\"h1\",{className:\"text-3xl text-amber-100 font-bold\",children:\"Fatbeam University Library\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center gap-4\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"relative\",children:[/*#__PURE__*/_jsx(\"input\",{type:\"text\",placeholder:\"Search books...\",className:\"bg-[#2a1a05] text-amber-100 px-4 py-2 rounded-lg pl-10 focus:outline-none focus:ring-2 focus:ring-amber-500\",value:searchQuery,onChange:e=>setSearchQuery(e.target.value)}),/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-amber-500\"})]}),/*#__PURE__*/_jsxs(\"button\",{onClick:()=>window.open('/learning/mod/wiki/index.php','_blank'),className:\"bg-blue-700 text-blue-100 px-4 py-2 rounded-lg hover:bg-blue-600 transition-colors rounded-button\",title:\"Access Moodle Wiki - Knowledge Base & Documentation\",children:[/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-wiki-w mr-2\"}),\"Wiki\"]}),/*#__PURE__*/_jsxs(\"button\",{className:\"bg-amber-700 text-amber-100 px-4 py-2 rounded-lg hover:bg-amber-600 transition-colors rounded-button\",children:[/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-bookmark mr-2\"}),\"Bookmarked (\",bookmarkedItems.length,\")\"]})]})]})}),/*#__PURE__*/_jsxs(\"main\",{className:\"container mx-auto p-8\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex gap-6 mb-8\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1\",children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-amber-200 mb-2\",children:\"Category\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"relative\",children:[/*#__PURE__*/_jsxs(\"select\",{className:\"w-full bg-[#2a1a05] text-amber-100 px-4 py-2 rounded-lg appearance-none cursor-pointer\",value:selectedCategory,onChange:e=>setSelectedCategory(e.target.value),children:[/*#__PURE__*/_jsx(\"option\",{value:\"all\",children:\"All Categories\"}),/*#__PURE__*/_jsx(\"option\",{value:\"protocols\",children:\"Protocols\"}),/*#__PURE__*/_jsx(\"option\",{value:\"troubleshooting\",children:\"Troubleshooting\"}),/*#__PURE__*/_jsx(\"option\",{value:\"fundamentals\",children:\"Fundamentals\"}),/*#__PURE__*/_jsx(\"option\",{value:\"security\",children:\"Security\"})]}),/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-chevron-down absolute right-4 top-1/2 transform -translate-y-1/2 text-amber-500\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1\",children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-amber-200 mb-2\",children:\"Difficulty\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"relative\",children:[/*#__PURE__*/_jsxs(\"select\",{className:\"w-full bg-[#2a1a05] text-amber-100 px-4 py-2 rounded-lg appearance-none cursor-pointer\",value:selectedDifficulty,onChange:e=>setSelectedDifficulty(e.target.value),children:[/*#__PURE__*/_jsx(\"option\",{value:\"all\",children:\"All Levels\"}),/*#__PURE__*/_jsx(\"option\",{value:\"beginner\",children:\"Beginner\"}),/*#__PURE__*/_jsx(\"option\",{value:\"intermediate\",children:\"Intermediate\"}),/*#__PURE__*/_jsx(\"option\",{value:\"advanced\",children:\"Advanced\"})]}),/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-chevron-down absolute right-4 top-1/2 transform -translate-y-1/2 text-amber-500\"})]})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"bg-gradient-to-r from-blue-900 to-blue-800 p-6 rounded-xl shadow-2xl mb-8 border border-blue-700\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(\"h2\",{className:\"text-2xl font-bold text-blue-100 mb-2\",children:[/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-wiki-w mr-3\"}),\"Knowledge Base & Wiki\"]}),/*#__PURE__*/_jsx(\"p\",{className:\"text-blue-200 mb-4\",children:\"Access our comprehensive wiki for detailed documentation, procedures, and collaborative knowledge sharing. Create, edit, and maintain technical documentation with your team.\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex gap-4\",children:[/*#__PURE__*/_jsxs(\"button\",{onClick:()=>window.open('/learning/mod/wiki/index.php','_blank'),className:\"bg-blue-600 text-blue-100 px-6 py-3 rounded-lg hover:bg-blue-500 transition-colors font-semibold\",children:[/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-external-link-alt mr-2\"}),\"Browse Wiki\"]}),/*#__PURE__*/_jsxs(\"button\",{onClick:()=>window.open('/learning/mod/wiki/create.php?action=new','_blank'),className:\"bg-green-600 text-green-100 px-6 py-3 rounded-lg hover:bg-green-500 transition-colors font-semibold\",children:[/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-plus mr-2\"}),\"Create New Page\"]})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"hidden md:block\",children:/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-book-open text-6xl text-blue-300 opacity-50\"})})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"bg-[url('https://readdy.ai/api/search-image?query=Antique%20wooden%20bookshelf%20with%20intricate%20carvings%20and%20warm%20ambient%20lighting%2C%20rich%20mahogany%20texture%20with%20mystical%20aura%2C%20vintage%20library%20atmosphere&width=1440&height=900&seq=shelf&orientation=landscape')] bg-cover bg-center p-8 rounded-xl shadow-2xl min-h-[900px] flex items-center\",children:/*#__PURE__*/_jsx(\"div\",{className:\"flex gap-2 items-end\",children:allBooks.map(book=>/*#__PURE__*/_jsx(BookSpine,{book:book},book.id))})}),hoveredBook&&/*#__PURE__*/_jsx(\"div\",{className:\"fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-[#1a0f00] p-6 rounded-lg shadow-xl border border-[#3a2a15] max-w-md z-50\",onClick:e=>e.stopPropagation(),children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex gap-6\",children:[/*#__PURE__*/_jsx(\"img\",{src:hoveredBook.coverImage,alt:hoveredBook.title,className:\"w-32 h-48 object-cover rounded-lg shadow-lg\"}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h3\",{className:\"text-xl font-bold text-amber-100 mb-2\",children:hoveredBook.title}),/*#__PURE__*/_jsxs(\"p\",{className:\"text-amber-200 text-sm mb-2\",children:[\"By \",hoveredBook.author]}),/*#__PURE__*/_jsx(\"p\",{className:\"text-amber-300 text-sm mb-4\",children:hoveredBook.description}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex gap-2\",children:[/*#__PURE__*/_jsxs(\"button\",{className:\"bg-amber-700 text-amber-100 px-4 py-2 text-sm rounded-lg hover:bg-amber-600 transition-colors rounded-button\",children:[/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-bookmark mr-2\"}),\"Bookmark\"]}),/*#__PURE__*/_jsxs(\"button\",{onClick:()=>{const overlay=document.createElement('div');overlay.className='fixed inset-0 bg-black/80 z-[60] flex items-center justify-center';const book=document.createElement('div');book.className='w-[300px] h-[400px] relative transform transition-all duration-1000';book.style.perspective='1000px';const cover=document.createElement('div');cover.className=\"absolute inset-0 bg-cover bg-center rounded-r-lg transform-origin-left transition-transform duration-1000\";cover.style.backgroundImage=\"url(\".concat(hoveredBook.coverImage,\")\");cover.style.transformStyle='preserve-3d';book.appendChild(cover);overlay.appendChild(book);document.body.appendChild(overlay);setTimeout(()=>{cover.style.transform='rotateY(-180deg)';},100);setTimeout(()=>{window.location.href=hoveredBook.wikiLink;},1000);},className:\"bg-green-700 text-green-100 px-4 py-2 text-sm rounded-lg hover:bg-green-600 transition-colors rounded-button\",children:[/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-book-reader mr-2\"}),hoveredBook.isUnique?'View Case Study':'Read Article']})]})]})]})})]})]});};export default LibraryPages;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "jsx", "_jsx", "jsxs", "_jsxs", "LibraryPages", "bookmarkedItems", "setBookmarkedItems", "checkedOutItems", "setCheckedOutItems", "selectedCate<PERSON><PERSON>", "setSelectedCategory", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "setSelectedDifficulty", "searchQuery", "setSearch<PERSON>uery", "showBookDetails", "setShowBookDetails", "selected<PERSON><PERSON>", "setSelectedBook", "filteredBooks", "setFilteredBooks", "hoveredBook", "setHoveredBook", "handleClickOutside", "document", "addEventListener", "removeEventListener", "style", "createElement", "textContent", "head", "append<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "allBooks", "id", "title", "author", "description", "difficulty", "category", "wikiLink", "isUnique", "spineColor", "textColor", "coverImage", "results", "query", "toLowerCase", "filter", "book", "includes", "BookSpine", "_ref", "className", "concat", "onMouseEnter", "onMouseLeave", "onClick", "children", "type", "placeholder", "value", "onChange", "e", "target", "window", "open", "length", "map", "stopPropagation", "src", "alt", "overlay", "perspective", "cover", "backgroundImage", "transformStyle", "body", "setTimeout", "transform", "location", "href"], "sources": ["/Users/<USER>/Documents/FatbeamU/react-app/src/pages/LibraryPages.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\n\nconst LibraryPages = () => {\n  const [bookmarkedItems, setBookmarkedItems] = useState([]);\n  const [checkedOutItems, setCheckedOutItems] = useState([]);\n  const [selectedCategory, setSelectedCategory] = useState('all');\n  const [selectedDifficulty, setSelectedDifficulty] = useState('all');\n  const [searchQuery, setSearchQuery] = useState('');\n  const [showBookDetails, setShowBookDetails] = useState(false);\n  const [selectedBook, setSelectedBook] = useState(null);\n  const [filteredBooks, setFilteredBooks] = useState([]);\n  const [hoveredBook, setHoveredBook] = useState(null);\n\n  useEffect(() => {\n    const handleClickOutside = () => {\n      setHoveredBook(null);\n    };\n\n    document.addEventListener('click', handleClickOutside);\n\n    return () => {\n      document.removeEventListener('click', handleClickOutside);\n    };\n  }, []);\n\n  // Add styles for book animation\n  useEffect(() => {\n    const style = document.createElement('style');\n    style.textContent = `\n    .transform-origin-left {\n      transform-origin: left;\n    }\n    `;\n    document.head.appendChild(style);\n    return () => {\n      document.head.removeChild(style);\n    };\n  }, []);\n\n  const allBooks = [\n    {\n      id: 'b1',\n      title: 'Network Protocols Overview',\n      author: 'Professor Elara Waveweaver',\n      description: 'A comprehensive guide to network protocols and their implementations in enterprise networks.',\n      difficulty: 'intermediate',\n      category: 'protocols',\n      wikiLink: '/networking/protocols/overview',\n      isUnique: false,\n      spineColor: 'bg-gradient-to-r from-[#3E2723] via-[#4E342E] to-[#3E2723]',\n      textColor: 'text-[#C0C0C0]',\n      coverImage: 'https://readdy.ai/api/search-image?query=Ancient%20magical%20book%20cover%20with%20network%20patterns%20and%20ethereal%20glowing%20symbols%20floating%20on%20a%20rich%20leather%20background%2C%20professional%20photography%20with%20dramatic%20lighting&width=400&height=600&seq=book1&orientation=portrait'\n    },\n    {\n      id: 'b5',\n      title: 'Fatbeam Rural Network Solutions',\n      author: 'Dr. Mystic Bytecaster',\n      description: 'Unique case study on implementing high-speed networks in rural communities with Fatbeam solutions.',\n      difficulty: 'advanced',\n      category: 'protocols',\n      wikiLink: '/fatbeam/case-studies/rural-networks',\n      isUnique: true,\n      spineColor: 'bg-gradient-to-r from-[#8B7355] via-[#A0856E] to-[#8B7355]',\n      textColor: 'text-[#D4AF37]',\n      coverImage: 'https://readdy.ai/api/search-image?query=Mystical%20book%20cover%20featuring%20flowing%20data%20streams%20and%20magical%20energy%20patterns%20on%20aged%20leather%20with%20gold%20accents%2C%20professional%20studio%20lighting&width=400&height=600&seq=book5&orientation=portrait'\n    },\n    {\n      id: 'b6',\n      title: 'Quantum Enchantment',\n      author: 'Sage Quantumweaver',\n      description: 'Advanced concepts in quantum networking and magical entanglement.',\n      difficulty: 'advanced',\n      category: 'fundamentals',\n      spineColor: 'bg-gradient-to-r from-[#5D4037] via-[#6D4C41] to-[#5D4037]',\n      textColor: 'text-[#C0C0C0]',\n      coverImage: 'https://readdy.ai/api/search-image?query=Quantum%20physics%20inspired%20magical%20book%20cover%20with%20intricate%20geometric%20patterns%20on%20deep%20purple%20leather%2C%20professional%20photography&width=400&height=600&seq=book6&orientation=portrait'\n    },\n    {\n      id: 'b7',\n      title: 'Runic Network Defense',\n      author: 'Guardian Shieldmaster',\n      description: 'Essential protection spells for magical network security.',\n      difficulty: 'intermediate',\n      category: 'security',\n      spineColor: 'bg-gradient-to-r from-[#4E342E] via-[#5D4037] to-[#4E342E]',\n      textColor: 'text-[#C0C0C0]',\n      coverImage: 'https://readdy.ai/api/search-image?query=Ancient%20protective%20runes%20and%20magical%20shields%20on%20dark%20green%20leather%20book%20cover%20with%20metallic%20embellishments%2C%20professional%20studio%20lighting&width=400&height=600&seq=book7&orientation=portrait'\n    },\n    {\n      id: 'b8',\n      title: 'Celestial Connections',\n      author: 'Astral Networker',\n      description: 'Establishing and maintaining connections through celestial planes.',\n      difficulty: 'advanced',\n      category: 'protocols',\n      spineColor: 'bg-gradient-to-r from-[#3E2723] via-[#4E342E] to-[#3E2723]',\n      textColor: 'text-[#C0C0C0]',\n      coverImage: 'https://readdy.ai/api/search-image?query=Celestial%20map%20and%20constellation%20patterns%20on%20midnight%20blue%20leather%20book%20cover%20with%20silver%20details%2C%20professional%20photography%20with%20dramatic%20lighting&width=400&height=600&seq=book8&orientation=portrait'\n    },\n    {\n      id: 'b2',\n      title: 'Troubleshooting the Ethereal Web',\n      author: 'Dr. Thorne Cablemancer',\n      description: 'Learn the art of identifying and resolving network disruptions across magical connections.',\n      difficulty: 'intermediate',\n      category: 'troubleshooting',\n      spineColor: 'bg-[#2A1B0E]',\n      textColor: 'text-[#C0C0C0]',\n      coverImage: 'https://readdy.ai/api/search-image?query=Mystical%20book%20cover%20featuring%20intricate%20network%20troubleshooting%20diagrams%20with%20magical%20aura%2C%20ancient%20leather%20texture%20background%20with%20professional%20studio%20lighting&width=400&height=600&seq=book2&orientation=portrait'\n    },\n    {\n      id: 'b3',\n      title: 'Fundamentals of Magical Networking',\n      author: 'Sage Bytewarden',\n      description: 'Essential concepts and principles of magical network architecture for beginners.',\n      difficulty: 'beginner',\n      category: 'fundamentals',\n      spineColor: 'bg-[#2A1B0E]',\n      textColor: 'text-[#C0C0C0]',\n      coverImage: 'https://readdy.ai/api/search-image?query=Enchanted%20book%20cover%20with%20basic%20networking%20symbols%20and%20magical%20runes%2C%20elegant%20leather%20binding%20with%20soft%20mystical%20glow%2C%20professional%20product%20photography&width=400&height=600&seq=book3&orientation=portrait'\n    },\n    {\n      id: 'b4',\n      title: 'Advanced Spellbound Security',\n      author: 'Master Cryptkeeper',\n      description: 'Advanced techniques for securing magical networks against dark forces.',\n      difficulty: 'advanced',\n      category: 'security',\n      spineColor: 'bg-[#2A1B0E]',\n      textColor: 'text-[#C0C0C0]',\n      coverImage: 'https://readdy.ai/api/search-image?query=Dark%20mysterious%20book%20cover%20with%20security%20sigils%20and%20protective%20magical%20barriers%2C%20ancient%20leather%20texture%20with%20metallic%20accents%2C%20professional%20studio%20lighting&width=400&height=600&seq=book4&orientation=portrait'\n    }\n  ];\n\n  useEffect(() => {\n    let results = allBooks;\n    if (searchQuery) {\n      const query = searchQuery.toLowerCase();\n      results = results.filter(book =>\n        book.title.toLowerCase().includes(query) ||\n        book.author.toLowerCase().includes(query) ||\n        book.description.toLowerCase().includes(query)\n      );\n    }\n    if (selectedCategory !== 'all') {\n      results = results.filter(book => book.category === selectedCategory);\n    }\n    if (selectedDifficulty !== 'all') {\n      results = results.filter(book => book.difficulty === selectedDifficulty);\n    }\n    setFilteredBooks(results);\n  }, [searchQuery, selectedCategory, selectedDifficulty]);\n\n  const BookSpine = ({ book }) => {\n    return (\n      <div\n        className={`h-[300px] w-[60px] ${book.spineColor} cursor-pointer transition-all duration-500 hover:scale-105 hover:z-10 relative shadow-xl overflow-hidden group`}\n        onMouseEnter={() => setHoveredBook(book)}\n        onMouseLeave={() => setHoveredBook(null)}\n        onClick={() => {\n          setSelectedBook(book);\n          setShowBookDetails(true);\n        }}\n      >\n        <div className=\"absolute inset-0 flex items-center justify-center\">\n          <div className=\"absolute inset-0\">\n            {/* Enhanced spine edges and embossing */}\n            <div className=\"absolute top-0 left-0 right-0 h-[8px] bg-gradient-to-b from-black/50 to-transparent\"></div>\n            <div className=\"absolute bottom-0 left-0 right-0 h-[8px] bg-gradient-to-t from-black/50 to-transparent\"></div>\n            <div className=\"absolute inset-y-0 left-0 w-[4px] bg-gradient-to-r from-black/50 to-transparent\"></div>\n            <div className=\"absolute inset-y-0 right-0 w-[4px] bg-gradient-to-l from-black/50 to-transparent\"></div>\n\n            {/* Book title with enhanced styling */}\n            <div className=\"absolute inset-0 flex items-center justify-center\">\n              <span className={`${book.textColor} text-sm font-serif vertical-text whitespace-nowrap transform -rotate-90 px-4 z-10 group-hover:scale-105 transition-transform duration-300 drop-shadow-lg tracking-wider`}>\n                {book.title}\n              </span>\n            </div>\n          </div>\n        </div>\n      </div>\n    );\n  };\n\n  return (\n    <div className=\"min-h-screen bg-[#0a0500] pt-20\">\n      <header className=\"bg-[#1a0f00] border-b border-[#3a2a15] p-6\">\n        <div className=\"container mx-auto flex justify-between items-center\">\n          <h1 className=\"text-3xl text-amber-100 font-bold\">Fatbeam University Library</h1>\n          <div className=\"flex items-center gap-4\">\n            <div className=\"relative\">\n              <input\n                type=\"text\"\n                placeholder=\"Search books...\"\n                className=\"bg-[#2a1a05] text-amber-100 px-4 py-2 rounded-lg pl-10 focus:outline-none focus:ring-2 focus:ring-amber-500\"\n                value={searchQuery}\n                onChange={(e) => setSearchQuery(e.target.value)}\n              />\n              <i className=\"fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-amber-500\"></i>\n            </div>\n            <button\n              onClick={() => window.open('/learning/mod/wiki/index.php', '_blank')}\n              className=\"bg-blue-700 text-blue-100 px-4 py-2 rounded-lg hover:bg-blue-600 transition-colors rounded-button\"\n              title=\"Access Moodle Wiki - Knowledge Base & Documentation\"\n            >\n              <i className=\"fas fa-wiki-w mr-2\"></i>\n              Wiki\n            </button>\n            <button className=\"bg-amber-700 text-amber-100 px-4 py-2 rounded-lg hover:bg-amber-600 transition-colors rounded-button\">\n              <i className=\"fas fa-bookmark mr-2\"></i>\n              Bookmarked ({bookmarkedItems.length})\n            </button>\n          </div>\n        </div>\n      </header>\n\n      <main className=\"container mx-auto p-8\">\n        <div className=\"flex gap-6 mb-8\">\n          <div className=\"flex-1\">\n            <label className=\"block text-amber-200 mb-2\">Category</label>\n            <div className=\"relative\">\n              <select\n                className=\"w-full bg-[#2a1a05] text-amber-100 px-4 py-2 rounded-lg appearance-none cursor-pointer\"\n                value={selectedCategory}\n                onChange={(e) => setSelectedCategory(e.target.value)}\n              >\n                <option value=\"all\">All Categories</option>\n                <option value=\"protocols\">Protocols</option>\n                <option value=\"troubleshooting\">Troubleshooting</option>\n                <option value=\"fundamentals\">Fundamentals</option>\n                <option value=\"security\">Security</option>\n              </select>\n              <i className=\"fas fa-chevron-down absolute right-4 top-1/2 transform -translate-y-1/2 text-amber-500\"></i>\n            </div>\n          </div>\n          <div className=\"flex-1\">\n            <label className=\"block text-amber-200 mb-2\">Difficulty</label>\n            <div className=\"relative\">\n              <select\n                className=\"w-full bg-[#2a1a05] text-amber-100 px-4 py-2 rounded-lg appearance-none cursor-pointer\"\n                value={selectedDifficulty}\n                onChange={(e) => setSelectedDifficulty(e.target.value)}\n              >\n                <option value=\"all\">All Levels</option>\n                <option value=\"beginner\">Beginner</option>\n                <option value=\"intermediate\">Intermediate</option>\n                <option value=\"advanced\">Advanced</option>\n              </select>\n              <i className=\"fas fa-chevron-down absolute right-4 top-1/2 transform -translate-y-1/2 text-amber-500\"></i>\n            </div>\n          </div>\n        </div>\n\n        {/* Wiki Knowledge Base Section */}\n        <div className=\"bg-gradient-to-r from-blue-900 to-blue-800 p-6 rounded-xl shadow-2xl mb-8 border border-blue-700\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <h2 className=\"text-2xl font-bold text-blue-100 mb-2\">\n                <i className=\"fas fa-wiki-w mr-3\"></i>\n                Knowledge Base & Wiki\n              </h2>\n              <p className=\"text-blue-200 mb-4\">\n                Access our comprehensive wiki for detailed documentation, procedures, and collaborative knowledge sharing.\n                Create, edit, and maintain technical documentation with your team.\n              </p>\n              <div className=\"flex gap-4\">\n                <button\n                  onClick={() => window.open('/learning/mod/wiki/index.php', '_blank')}\n                  className=\"bg-blue-600 text-blue-100 px-6 py-3 rounded-lg hover:bg-blue-500 transition-colors font-semibold\"\n                >\n                  <i className=\"fas fa-external-link-alt mr-2\"></i>\n                  Browse Wiki\n                </button>\n                <button\n                  onClick={() => window.open('/learning/mod/wiki/create.php?action=new', '_blank')}\n                  className=\"bg-green-600 text-green-100 px-6 py-3 rounded-lg hover:bg-green-500 transition-colors font-semibold\"\n                >\n                  <i className=\"fas fa-plus mr-2\"></i>\n                  Create New Page\n                </button>\n              </div>\n            </div>\n            <div className=\"hidden md:block\">\n              <i className=\"fas fa-book-open text-6xl text-blue-300 opacity-50\"></i>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-[url('https://readdy.ai/api/search-image?query=Antique%20wooden%20bookshelf%20with%20intricate%20carvings%20and%20warm%20ambient%20lighting%2C%20rich%20mahogany%20texture%20with%20mystical%20aura%2C%20vintage%20library%20atmosphere&width=1440&height=900&seq=shelf&orientation=landscape')] bg-cover bg-center p-8 rounded-xl shadow-2xl min-h-[900px] flex items-center\">\n          <div className=\"flex gap-2 items-end\">\n            {allBooks.map(book => (\n              <BookSpine key={book.id} book={book} />\n            ))}\n          </div>\n        </div>\n\n        {hoveredBook && (\n          <div\n            className=\"fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-[#1a0f00] p-6 rounded-lg shadow-xl border border-[#3a2a15] max-w-md z-50\"\n            onClick={(e) => e.stopPropagation()}\n          >\n            <div className=\"flex gap-6\">\n              <img src={hoveredBook.coverImage} alt={hoveredBook.title} className=\"w-32 h-48 object-cover rounded-lg shadow-lg\" />\n              <div>\n                <h3 className=\"text-xl font-bold text-amber-100 mb-2\">{hoveredBook.title}</h3>\n                <p className=\"text-amber-200 text-sm mb-2\">By {hoveredBook.author}</p>\n                <p className=\"text-amber-300 text-sm mb-4\">{hoveredBook.description}</p>\n                <div className=\"flex gap-2\">\n                  <button className=\"bg-amber-700 text-amber-100 px-4 py-2 text-sm rounded-lg hover:bg-amber-600 transition-colors rounded-button\">\n                    <i className=\"fas fa-bookmark mr-2\"></i>\n                    Bookmark\n                  </button>\n                  <button\n                    onClick={() => {\n                      const overlay = document.createElement('div');\n                      overlay.className = 'fixed inset-0 bg-black/80 z-[60] flex items-center justify-center';\n                      const book = document.createElement('div');\n                      book.className = 'w-[300px] h-[400px] relative transform transition-all duration-1000';\n                      book.style.perspective = '1000px';\n                      const cover = document.createElement('div');\n                      cover.className = `absolute inset-0 bg-cover bg-center rounded-r-lg transform-origin-left transition-transform duration-1000`;\n                      cover.style.backgroundImage = `url(${hoveredBook.coverImage})`;\n                      cover.style.transformStyle = 'preserve-3d';\n                      book.appendChild(cover);\n                      overlay.appendChild(book);\n                      document.body.appendChild(overlay);\n                      setTimeout(() => {\n                        cover.style.transform = 'rotateY(-180deg)';\n                      }, 100);\n                      setTimeout(() => {\n                        window.location.href = hoveredBook.wikiLink;\n                      }, 1000);\n                    }}\n                    className=\"bg-green-700 text-green-100 px-4 py-2 text-sm rounded-lg hover:bg-green-600 transition-colors rounded-button\"\n                  >\n                    <i className=\"fas fa-book-reader mr-2\"></i>\n                    {hoveredBook.isUnique ? 'View Case Study' : 'Read Article'}\n                  </button>\n                </div>\n              </div>\n            </div>\n          </div>\n        )}\n      </main>\n    </div>\n  );\n};\n\nexport default LibraryPages;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEnD,KAAM,CAAAC,YAAY,CAAGA,CAAA,GAAM,CACzB,KAAM,CAACC,eAAe,CAAEC,kBAAkB,CAAC,CAAGR,QAAQ,CAAC,EAAE,CAAC,CAC1D,KAAM,CAACS,eAAe,CAAEC,kBAAkB,CAAC,CAAGV,QAAQ,CAAC,EAAE,CAAC,CAC1D,KAAM,CAACW,gBAAgB,CAAEC,mBAAmB,CAAC,CAAGZ,QAAQ,CAAC,KAAK,CAAC,CAC/D,KAAM,CAACa,kBAAkB,CAAEC,qBAAqB,CAAC,CAAGd,QAAQ,CAAC,KAAK,CAAC,CACnE,KAAM,CAACe,WAAW,CAAEC,cAAc,CAAC,CAAGhB,QAAQ,CAAC,EAAE,CAAC,CAClD,KAAM,CAACiB,eAAe,CAAEC,kBAAkB,CAAC,CAAGlB,QAAQ,CAAC,KAAK,CAAC,CAC7D,KAAM,CAACmB,YAAY,CAAEC,eAAe,CAAC,CAAGpB,QAAQ,CAAC,IAAI,CAAC,CACtD,KAAM,CAACqB,aAAa,CAAEC,gBAAgB,CAAC,CAAGtB,QAAQ,CAAC,EAAE,CAAC,CACtD,KAAM,CAACuB,WAAW,CAAEC,cAAc,CAAC,CAAGxB,QAAQ,CAAC,IAAI,CAAC,CAEpDC,SAAS,CAAC,IAAM,CACd,KAAM,CAAAwB,kBAAkB,CAAGA,CAAA,GAAM,CAC/BD,cAAc,CAAC,IAAI,CAAC,CACtB,CAAC,CAEDE,QAAQ,CAACC,gBAAgB,CAAC,OAAO,CAAEF,kBAAkB,CAAC,CAEtD,MAAO,IAAM,CACXC,QAAQ,CAACE,mBAAmB,CAAC,OAAO,CAAEH,kBAAkB,CAAC,CAC3D,CAAC,CACH,CAAC,CAAE,EAAE,CAAC,CAEN;AACAxB,SAAS,CAAC,IAAM,CACd,KAAM,CAAA4B,KAAK,CAAGH,QAAQ,CAACI,aAAa,CAAC,OAAO,CAAC,CAC7CD,KAAK,CAACE,WAAW,6EAIhB,CACDL,QAAQ,CAACM,IAAI,CAACC,WAAW,CAACJ,KAAK,CAAC,CAChC,MAAO,IAAM,CACXH,QAAQ,CAACM,IAAI,CAACE,WAAW,CAACL,KAAK,CAAC,CAClC,CAAC,CACH,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAM,QAAQ,CAAG,CACf,CACEC,EAAE,CAAE,IAAI,CACRC,KAAK,CAAE,4BAA4B,CACnCC,MAAM,CAAE,4BAA4B,CACpCC,WAAW,CAAE,8FAA8F,CAC3GC,UAAU,CAAE,cAAc,CAC1BC,QAAQ,CAAE,WAAW,CACrBC,QAAQ,CAAE,gCAAgC,CAC1CC,QAAQ,CAAE,KAAK,CACfC,UAAU,CAAE,4DAA4D,CACxEC,SAAS,CAAE,gBAAgB,CAC3BC,UAAU,CAAE,+SACd,CAAC,CACD,CACEV,EAAE,CAAE,IAAI,CACRC,KAAK,CAAE,iCAAiC,CACxCC,MAAM,CAAE,uBAAuB,CAC/BC,WAAW,CAAE,oGAAoG,CACjHC,UAAU,CAAE,UAAU,CACtBC,QAAQ,CAAE,WAAW,CACrBC,QAAQ,CAAE,sCAAsC,CAChDC,QAAQ,CAAE,IAAI,CACdC,UAAU,CAAE,4DAA4D,CACxEC,SAAS,CAAE,gBAAgB,CAC3BC,UAAU,CAAE,qRACd,CAAC,CACD,CACEV,EAAE,CAAE,IAAI,CACRC,KAAK,CAAE,qBAAqB,CAC5BC,MAAM,CAAE,oBAAoB,CAC5BC,WAAW,CAAE,mEAAmE,CAChFC,UAAU,CAAE,UAAU,CACtBC,QAAQ,CAAE,cAAc,CACxBG,UAAU,CAAE,4DAA4D,CACxEC,SAAS,CAAE,gBAAgB,CAC3BC,UAAU,CAAE,6PACd,CAAC,CACD,CACEV,EAAE,CAAE,IAAI,CACRC,KAAK,CAAE,uBAAuB,CAC9BC,MAAM,CAAE,uBAAuB,CAC/BC,WAAW,CAAE,2DAA2D,CACxEC,UAAU,CAAE,cAAc,CAC1BC,QAAQ,CAAE,UAAU,CACpBG,UAAU,CAAE,4DAA4D,CACxEC,SAAS,CAAE,gBAAgB,CAC3BC,UAAU,CAAE,2QACd,CAAC,CACD,CACEV,EAAE,CAAE,IAAI,CACRC,KAAK,CAAE,uBAAuB,CAC9BC,MAAM,CAAE,kBAAkB,CAC1BC,WAAW,CAAE,oEAAoE,CACjFC,UAAU,CAAE,UAAU,CACtBC,QAAQ,CAAE,WAAW,CACrBG,UAAU,CAAE,4DAA4D,CACxEC,SAAS,CAAE,gBAAgB,CAC3BC,UAAU,CAAE,sRACd,CAAC,CACD,CACEV,EAAE,CAAE,IAAI,CACRC,KAAK,CAAE,kCAAkC,CACzCC,MAAM,CAAE,wBAAwB,CAChCC,WAAW,CAAE,4FAA4F,CACzGC,UAAU,CAAE,cAAc,CAC1BC,QAAQ,CAAE,iBAAiB,CAC3BG,UAAU,CAAE,cAAc,CAC1BC,SAAS,CAAE,gBAAgB,CAC3BC,UAAU,CAAE,qSACd,CAAC,CACD,CACEV,EAAE,CAAE,IAAI,CACRC,KAAK,CAAE,oCAAoC,CAC3CC,MAAM,CAAE,iBAAiB,CACzBC,WAAW,CAAE,kFAAkF,CAC/FC,UAAU,CAAE,UAAU,CACtBC,QAAQ,CAAE,cAAc,CACxBG,UAAU,CAAE,cAAc,CAC1BC,SAAS,CAAE,gBAAgB,CAC3BC,UAAU,CAAE,gSACd,CAAC,CACD,CACEV,EAAE,CAAE,IAAI,CACRC,KAAK,CAAE,8BAA8B,CACrCC,MAAM,CAAE,oBAAoB,CAC5BC,WAAW,CAAE,wEAAwE,CACrFC,UAAU,CAAE,UAAU,CACtBC,QAAQ,CAAE,UAAU,CACpBG,UAAU,CAAE,cAAc,CAC1BC,SAAS,CAAE,gBAAgB,CAC3BC,UAAU,CAAE,qSACd,CAAC,CACF,CAED7C,SAAS,CAAC,IAAM,CACd,GAAI,CAAA8C,OAAO,CAAGZ,QAAQ,CACtB,GAAIpB,WAAW,CAAE,CACf,KAAM,CAAAiC,KAAK,CAAGjC,WAAW,CAACkC,WAAW,CAAC,CAAC,CACvCF,OAAO,CAAGA,OAAO,CAACG,MAAM,CAACC,IAAI,EAC3BA,IAAI,CAACd,KAAK,CAACY,WAAW,CAAC,CAAC,CAACG,QAAQ,CAACJ,KAAK,CAAC,EACxCG,IAAI,CAACb,MAAM,CAACW,WAAW,CAAC,CAAC,CAACG,QAAQ,CAACJ,KAAK,CAAC,EACzCG,IAAI,CAACZ,WAAW,CAACU,WAAW,CAAC,CAAC,CAACG,QAAQ,CAACJ,KAAK,CAC/C,CAAC,CACH,CACA,GAAIrC,gBAAgB,GAAK,KAAK,CAAE,CAC9BoC,OAAO,CAAGA,OAAO,CAACG,MAAM,CAACC,IAAI,EAAIA,IAAI,CAACV,QAAQ,GAAK9B,gBAAgB,CAAC,CACtE,CACA,GAAIE,kBAAkB,GAAK,KAAK,CAAE,CAChCkC,OAAO,CAAGA,OAAO,CAACG,MAAM,CAACC,IAAI,EAAIA,IAAI,CAACX,UAAU,GAAK3B,kBAAkB,CAAC,CAC1E,CACAS,gBAAgB,CAACyB,OAAO,CAAC,CAC3B,CAAC,CAAE,CAAChC,WAAW,CAAEJ,gBAAgB,CAAEE,kBAAkB,CAAC,CAAC,CAEvD,KAAM,CAAAwC,SAAS,CAAGC,IAAA,EAAc,IAAb,CAAEH,IAAK,CAAC,CAAAG,IAAA,CACzB,mBACEnD,IAAA,QACEoD,SAAS,uBAAAC,MAAA,CAAwBL,IAAI,CAACP,UAAU,mHAAkH,CAClKa,YAAY,CAAEA,CAAA,GAAMjC,cAAc,CAAC2B,IAAI,CAAE,CACzCO,YAAY,CAAEA,CAAA,GAAMlC,cAAc,CAAC,IAAI,CAAE,CACzCmC,OAAO,CAAEA,CAAA,GAAM,CACbvC,eAAe,CAAC+B,IAAI,CAAC,CACrBjC,kBAAkB,CAAC,IAAI,CAAC,CAC1B,CAAE,CAAA0C,QAAA,cAEFzD,IAAA,QAAKoD,SAAS,CAAC,mDAAmD,CAAAK,QAAA,cAChEvD,KAAA,QAAKkD,SAAS,CAAC,kBAAkB,CAAAK,QAAA,eAE/BzD,IAAA,QAAKoD,SAAS,CAAC,qFAAqF,CAAM,CAAC,cAC3GpD,IAAA,QAAKoD,SAAS,CAAC,wFAAwF,CAAM,CAAC,cAC9GpD,IAAA,QAAKoD,SAAS,CAAC,iFAAiF,CAAM,CAAC,cACvGpD,IAAA,QAAKoD,SAAS,CAAC,kFAAkF,CAAM,CAAC,cAGxGpD,IAAA,QAAKoD,SAAS,CAAC,mDAAmD,CAAAK,QAAA,cAChEzD,IAAA,SAAMoD,SAAS,IAAAC,MAAA,CAAKL,IAAI,CAACN,SAAS,4KAA2K,CAAAe,QAAA,CAC1MT,IAAI,CAACd,KAAK,CACP,CAAC,CACJ,CAAC,EACH,CAAC,CACH,CAAC,CACH,CAAC,CAEV,CAAC,CAED,mBACEhC,KAAA,QAAKkD,SAAS,CAAC,iCAAiC,CAAAK,QAAA,eAC9CzD,IAAA,WAAQoD,SAAS,CAAC,4CAA4C,CAAAK,QAAA,cAC5DvD,KAAA,QAAKkD,SAAS,CAAC,qDAAqD,CAAAK,QAAA,eAClEzD,IAAA,OAAIoD,SAAS,CAAC,mCAAmC,CAAAK,QAAA,CAAC,4BAA0B,CAAI,CAAC,cACjFvD,KAAA,QAAKkD,SAAS,CAAC,yBAAyB,CAAAK,QAAA,eACtCvD,KAAA,QAAKkD,SAAS,CAAC,UAAU,CAAAK,QAAA,eACvBzD,IAAA,UACE0D,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,iBAAiB,CAC7BP,SAAS,CAAC,6GAA6G,CACvHQ,KAAK,CAAEhD,WAAY,CACnBiD,QAAQ,CAAGC,CAAC,EAAKjD,cAAc,CAACiD,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CACjD,CAAC,cACF5D,IAAA,MAAGoD,SAAS,CAAC,iFAAiF,CAAI,CAAC,EAChG,CAAC,cACNlD,KAAA,WACEsD,OAAO,CAAEA,CAAA,GAAMQ,MAAM,CAACC,IAAI,CAAC,8BAA8B,CAAE,QAAQ,CAAE,CACrEb,SAAS,CAAC,mGAAmG,CAC7GlB,KAAK,CAAC,qDAAqD,CAAAuB,QAAA,eAE3DzD,IAAA,MAAGoD,SAAS,CAAC,oBAAoB,CAAI,CAAC,OAExC,EAAQ,CAAC,cACTlD,KAAA,WAAQkD,SAAS,CAAC,sGAAsG,CAAAK,QAAA,eACtHzD,IAAA,MAAGoD,SAAS,CAAC,sBAAsB,CAAI,CAAC,eAC5B,CAAChD,eAAe,CAAC8D,MAAM,CAAC,GACtC,EAAQ,CAAC,EACN,CAAC,EACH,CAAC,CACA,CAAC,cAEThE,KAAA,SAAMkD,SAAS,CAAC,uBAAuB,CAAAK,QAAA,eACrCvD,KAAA,QAAKkD,SAAS,CAAC,iBAAiB,CAAAK,QAAA,eAC9BvD,KAAA,QAAKkD,SAAS,CAAC,QAAQ,CAAAK,QAAA,eACrBzD,IAAA,UAAOoD,SAAS,CAAC,2BAA2B,CAAAK,QAAA,CAAC,UAAQ,CAAO,CAAC,cAC7DvD,KAAA,QAAKkD,SAAS,CAAC,UAAU,CAAAK,QAAA,eACvBvD,KAAA,WACEkD,SAAS,CAAC,wFAAwF,CAClGQ,KAAK,CAAEpD,gBAAiB,CACxBqD,QAAQ,CAAGC,CAAC,EAAKrD,mBAAmB,CAACqD,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CAAAH,QAAA,eAErDzD,IAAA,WAAQ4D,KAAK,CAAC,KAAK,CAAAH,QAAA,CAAC,gBAAc,CAAQ,CAAC,cAC3CzD,IAAA,WAAQ4D,KAAK,CAAC,WAAW,CAAAH,QAAA,CAAC,WAAS,CAAQ,CAAC,cAC5CzD,IAAA,WAAQ4D,KAAK,CAAC,iBAAiB,CAAAH,QAAA,CAAC,iBAAe,CAAQ,CAAC,cACxDzD,IAAA,WAAQ4D,KAAK,CAAC,cAAc,CAAAH,QAAA,CAAC,cAAY,CAAQ,CAAC,cAClDzD,IAAA,WAAQ4D,KAAK,CAAC,UAAU,CAAAH,QAAA,CAAC,UAAQ,CAAQ,CAAC,EACpC,CAAC,cACTzD,IAAA,MAAGoD,SAAS,CAAC,wFAAwF,CAAI,CAAC,EACvG,CAAC,EACH,CAAC,cACNlD,KAAA,QAAKkD,SAAS,CAAC,QAAQ,CAAAK,QAAA,eACrBzD,IAAA,UAAOoD,SAAS,CAAC,2BAA2B,CAAAK,QAAA,CAAC,YAAU,CAAO,CAAC,cAC/DvD,KAAA,QAAKkD,SAAS,CAAC,UAAU,CAAAK,QAAA,eACvBvD,KAAA,WACEkD,SAAS,CAAC,wFAAwF,CAClGQ,KAAK,CAAElD,kBAAmB,CAC1BmD,QAAQ,CAAGC,CAAC,EAAKnD,qBAAqB,CAACmD,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CAAAH,QAAA,eAEvDzD,IAAA,WAAQ4D,KAAK,CAAC,KAAK,CAAAH,QAAA,CAAC,YAAU,CAAQ,CAAC,cACvCzD,IAAA,WAAQ4D,KAAK,CAAC,UAAU,CAAAH,QAAA,CAAC,UAAQ,CAAQ,CAAC,cAC1CzD,IAAA,WAAQ4D,KAAK,CAAC,cAAc,CAAAH,QAAA,CAAC,cAAY,CAAQ,CAAC,cAClDzD,IAAA,WAAQ4D,KAAK,CAAC,UAAU,CAAAH,QAAA,CAAC,UAAQ,CAAQ,CAAC,EACpC,CAAC,cACTzD,IAAA,MAAGoD,SAAS,CAAC,wFAAwF,CAAI,CAAC,EACvG,CAAC,EACH,CAAC,EACH,CAAC,cAGNpD,IAAA,QAAKoD,SAAS,CAAC,kGAAkG,CAAAK,QAAA,cAC/GvD,KAAA,QAAKkD,SAAS,CAAC,mCAAmC,CAAAK,QAAA,eAChDvD,KAAA,QAAAuD,QAAA,eACEvD,KAAA,OAAIkD,SAAS,CAAC,uCAAuC,CAAAK,QAAA,eACnDzD,IAAA,MAAGoD,SAAS,CAAC,oBAAoB,CAAI,CAAC,wBAExC,EAAI,CAAC,cACLpD,IAAA,MAAGoD,SAAS,CAAC,oBAAoB,CAAAK,QAAA,CAAC,+KAGlC,CAAG,CAAC,cACJvD,KAAA,QAAKkD,SAAS,CAAC,YAAY,CAAAK,QAAA,eACzBvD,KAAA,WACEsD,OAAO,CAAEA,CAAA,GAAMQ,MAAM,CAACC,IAAI,CAAC,8BAA8B,CAAE,QAAQ,CAAE,CACrEb,SAAS,CAAC,kGAAkG,CAAAK,QAAA,eAE5GzD,IAAA,MAAGoD,SAAS,CAAC,+BAA+B,CAAI,CAAC,cAEnD,EAAQ,CAAC,cACTlD,KAAA,WACEsD,OAAO,CAAEA,CAAA,GAAMQ,MAAM,CAACC,IAAI,CAAC,0CAA0C,CAAE,QAAQ,CAAE,CACjFb,SAAS,CAAC,qGAAqG,CAAAK,QAAA,eAE/GzD,IAAA,MAAGoD,SAAS,CAAC,kBAAkB,CAAI,CAAC,kBAEtC,EAAQ,CAAC,EACN,CAAC,EACH,CAAC,cACNpD,IAAA,QAAKoD,SAAS,CAAC,iBAAiB,CAAAK,QAAA,cAC9BzD,IAAA,MAAGoD,SAAS,CAAC,oDAAoD,CAAI,CAAC,CACnE,CAAC,EACH,CAAC,CACH,CAAC,cAENpD,IAAA,QAAKoD,SAAS,CAAC,kXAAkX,CAAAK,QAAA,cAC/XzD,IAAA,QAAKoD,SAAS,CAAC,sBAAsB,CAAAK,QAAA,CAClCzB,QAAQ,CAACmC,GAAG,CAACnB,IAAI,eAChBhD,IAAA,CAACkD,SAAS,EAAeF,IAAI,CAAEA,IAAK,EAApBA,IAAI,CAACf,EAAiB,CACvC,CAAC,CACC,CAAC,CACH,CAAC,CAELb,WAAW,eACVpB,IAAA,QACEoD,SAAS,CAAC,gJAAgJ,CAC1JI,OAAO,CAAGM,CAAC,EAAKA,CAAC,CAACM,eAAe,CAAC,CAAE,CAAAX,QAAA,cAEpCvD,KAAA,QAAKkD,SAAS,CAAC,YAAY,CAAAK,QAAA,eACzBzD,IAAA,QAAKqE,GAAG,CAAEjD,WAAW,CAACuB,UAAW,CAAC2B,GAAG,CAAElD,WAAW,CAACc,KAAM,CAACkB,SAAS,CAAC,6CAA6C,CAAE,CAAC,cACpHlD,KAAA,QAAAuD,QAAA,eACEzD,IAAA,OAAIoD,SAAS,CAAC,uCAAuC,CAAAK,QAAA,CAAErC,WAAW,CAACc,KAAK,CAAK,CAAC,cAC9EhC,KAAA,MAAGkD,SAAS,CAAC,6BAA6B,CAAAK,QAAA,EAAC,KAAG,CAACrC,WAAW,CAACe,MAAM,EAAI,CAAC,cACtEnC,IAAA,MAAGoD,SAAS,CAAC,6BAA6B,CAAAK,QAAA,CAAErC,WAAW,CAACgB,WAAW,CAAI,CAAC,cACxElC,KAAA,QAAKkD,SAAS,CAAC,YAAY,CAAAK,QAAA,eACzBvD,KAAA,WAAQkD,SAAS,CAAC,8GAA8G,CAAAK,QAAA,eAC9HzD,IAAA,MAAGoD,SAAS,CAAC,sBAAsB,CAAI,CAAC,WAE1C,EAAQ,CAAC,cACTlD,KAAA,WACEsD,OAAO,CAAEA,CAAA,GAAM,CACb,KAAM,CAAAe,OAAO,CAAGhD,QAAQ,CAACI,aAAa,CAAC,KAAK,CAAC,CAC7C4C,OAAO,CAACnB,SAAS,CAAG,mEAAmE,CACvF,KAAM,CAAAJ,IAAI,CAAGzB,QAAQ,CAACI,aAAa,CAAC,KAAK,CAAC,CAC1CqB,IAAI,CAACI,SAAS,CAAG,qEAAqE,CACtFJ,IAAI,CAACtB,KAAK,CAAC8C,WAAW,CAAG,QAAQ,CACjC,KAAM,CAAAC,KAAK,CAAGlD,QAAQ,CAACI,aAAa,CAAC,KAAK,CAAC,CAC3C8C,KAAK,CAACrB,SAAS,4GAA8G,CAC7HqB,KAAK,CAAC/C,KAAK,CAACgD,eAAe,QAAArB,MAAA,CAAUjC,WAAW,CAACuB,UAAU,KAAG,CAC9D8B,KAAK,CAAC/C,KAAK,CAACiD,cAAc,CAAG,aAAa,CAC1C3B,IAAI,CAAClB,WAAW,CAAC2C,KAAK,CAAC,CACvBF,OAAO,CAACzC,WAAW,CAACkB,IAAI,CAAC,CACzBzB,QAAQ,CAACqD,IAAI,CAAC9C,WAAW,CAACyC,OAAO,CAAC,CAClCM,UAAU,CAAC,IAAM,CACfJ,KAAK,CAAC/C,KAAK,CAACoD,SAAS,CAAG,kBAAkB,CAC5C,CAAC,CAAE,GAAG,CAAC,CACPD,UAAU,CAAC,IAAM,CACfb,MAAM,CAACe,QAAQ,CAACC,IAAI,CAAG5D,WAAW,CAACmB,QAAQ,CAC7C,CAAC,CAAE,IAAI,CAAC,CACV,CAAE,CACFa,SAAS,CAAC,8GAA8G,CAAAK,QAAA,eAExHzD,IAAA,MAAGoD,SAAS,CAAC,yBAAyB,CAAI,CAAC,CAC1ChC,WAAW,CAACoB,QAAQ,CAAG,iBAAiB,CAAG,cAAc,EACpD,CAAC,EACN,CAAC,EACH,CAAC,EACH,CAAC,CACH,CACN,EACG,CAAC,EACJ,CAAC,CAEV,CAAC,CAED,cAAe,CAAArC,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}