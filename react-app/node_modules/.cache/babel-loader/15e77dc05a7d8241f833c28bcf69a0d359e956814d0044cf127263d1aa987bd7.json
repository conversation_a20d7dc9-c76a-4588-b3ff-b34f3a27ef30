{"ast": null, "code": "import axios from './lib/axios.js';\n\n// This module is intended to unwrap Axios default export as named.\n// Keep top-level export same with static properties\n// so that it can keep same with es module or cjs\nconst {\n  Axios,\n  AxiosError,\n  CanceledError,\n  isCancel,\n  CancelToken,\n  VERSION,\n  all,\n  Cancel,\n  isAxiosError,\n  spread,\n  toFormData,\n  AxiosHeaders,\n  HttpStatusCode,\n  formToJSON,\n  getAdapter,\n  mergeConfig\n} = axios;\nexport { axios as default, Axios, AxiosError, CanceledError, isCancel, CancelToken, VERSION, all, Cancel, isAxiosError, spread, toFormData, AxiosHeaders, HttpStatusCode, formToJSON, getAdapter, mergeConfig };", "map": {"version": 3, "names": ["axios", "A<PERSON>os", "AxiosError", "CanceledError", "isCancel", "CancelToken", "VERSION", "all", "Cancel", "isAxiosError", "spread", "toFormData", "AxiosHeaders", "HttpStatusCode", "formToJSON", "getAdapter", "mergeConfig", "default"], "sources": ["/Users/<USER>/Documents/FatbeamU/react-app/node_modules/axios/index.js"], "sourcesContent": ["import axios from './lib/axios.js';\n\n// This module is intended to unwrap Axios default export as named.\n// Keep top-level export same with static properties\n// so that it can keep same with es module or cjs\nconst {\n  Axios,\n  AxiosError,\n  CanceledError,\n  isCancel,\n  CancelToken,\n  VERSION,\n  all,\n  Cancel,\n  isAxiosError,\n  spread,\n  toFormData,\n  AxiosHeaders,\n  HttpStatusCode,\n  formToJSON,\n  getAdapter,\n  mergeConfig\n} = axios;\n\nexport {\n  axios as default,\n  Axios,\n  AxiosError,\n  CanceledError,\n  isCancel,\n  CancelToken,\n  VERSION,\n  all,\n  Cancel,\n  isAxiosError,\n  spread,\n  toFormData,\n  AxiosHeaders,\n  HttpStatusCode,\n  formToJSON,\n  getAdapter,\n  mergeConfig\n}\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,gBAAgB;;AAElC;AACA;AACA;AACA,MAAM;EACJC,KAAK;EACLC,UAAU;EACVC,aAAa;EACbC,QAAQ;EACRC,WAAW;EACXC,OAAO;EACPC,GAAG;EACHC,MAAM;EACNC,YAAY;EACZC,MAAM;EACNC,UAAU;EACVC,YAAY;EACZC,cAAc;EACdC,UAAU;EACVC,UAAU;EACVC;AACF,CAAC,GAAGhB,KAAK;AAET,SACEA,KAAK,IAAIiB,OAAO,EAChBhB,KAAK,EACLC,UAAU,EACVC,aAAa,EACbC,QAAQ,EACRC,WAAW,EACXC,OAAO,EACPC,GAAG,EACHC,MAAM,EACNC,YAAY,EACZC,MAAM,EACNC,UAAU,EACVC,YAAY,EACZC,cAAc,EACdC,UAAU,EACVC,UAAU,EACVC,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}