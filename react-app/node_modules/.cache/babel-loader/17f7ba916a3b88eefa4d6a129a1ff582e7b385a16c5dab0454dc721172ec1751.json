{"ast": null, "code": "import React from'react';import{<PERSON>}from'react-router-dom';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const NetworkOperationsDeptPage=()=>{return/*#__PURE__*/_jsx(\"div\",{className:\"min-h-screen bg-white text-gray-800 font-sans pt-20\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"container mx-auto px-6 py-12\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-center mb-12\",children:[/*#__PURE__*/_jsxs(\"h1\",{className:\"text-4xl md:text-5xl font-serif font-bold mb-6\",children:[\"Network Operations \",/*#__PURE__*/_jsx(\"span\",{className:\"text-indigo-600\",children:\"(<PERSON>)\"})]}),/*#__PURE__*/_jsx(\"p\",{className:\"text-xl text-gray-600 max-w-3xl mx-auto\",children:\"Craft the invisible pathways of data, ensuring seamless communication across the vast distances of our kingdom.\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"bg-indigo-50 rounded-lg p-8 text-center\",children:[/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-diagram-project text-6xl text-indigo-600 mb-4\"}),/*#__PURE__*/_jsx(\"h2\",{className:\"text-2xl font-bold text-gray-800 mb-4\",children:\"Department Page Coming Soon\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-600 mb-6\",children:\"Our data weavers are currently weaving the digital threads to manifest this page.\"}),/*#__PURE__*/_jsxs(Link,{to:\"/departments\",className:\"inline-flex items-center px-6 py-3 bg-indigo-600 text-white rounded-full hover:bg-indigo-700 transition-colors duration-300\",children:[/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-arrow-left mr-2\"}),\"Back to Departments\"]})]})]})});};export default NetworkOperationsDeptPage;", "map": {"version": 3, "names": ["React", "Link", "jsx", "_jsx", "jsxs", "_jsxs", "NetworkOperationsDeptPage", "className", "children", "to"], "sources": ["/Users/<USER>/Documents/FatbeamU/react-app/src/pages/departments/NetworkOperationsDeptPage.js"], "sourcesContent": ["import React from 'react';\nimport { Link } from 'react-router-dom';\n\nconst NetworkOperationsDeptPage = () => {\n  return (\n    <div className=\"min-h-screen bg-white text-gray-800 font-sans pt-20\">\n      <div className=\"container mx-auto px-6 py-12\">\n        <div className=\"text-center mb-12\">\n          <h1 className=\"text-4xl md:text-5xl font-serif font-bold mb-6\">\n            Network Operations <span className=\"text-indigo-600\">(Data Weavers)</span>\n          </h1>\n          <p className=\"text-xl text-gray-600 max-w-3xl mx-auto\">\n            Craft the invisible pathways of data, ensuring seamless communication across the vast distances of our kingdom.\n          </p>\n        </div>\n        \n        <div className=\"bg-indigo-50 rounded-lg p-8 text-center\">\n          <i className=\"fas fa-diagram-project text-6xl text-indigo-600 mb-4\"></i>\n          <h2 className=\"text-2xl font-bold text-gray-800 mb-4\">Department Page Coming Soon</h2>\n          <p className=\"text-gray-600 mb-6\">\n            Our data weavers are currently weaving the digital threads to manifest this page.\n          </p>\n          <Link to=\"/departments\" className=\"inline-flex items-center px-6 py-3 bg-indigo-600 text-white rounded-full hover:bg-indigo-700 transition-colors duration-300\">\n            <i className=\"fas fa-arrow-left mr-2\"></i>\n            Back to Departments\n          </Link>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default NetworkOperationsDeptPage;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OAASC,IAAI,KAAQ,kBAAkB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAExC,KAAM,CAAAC,yBAAyB,CAAGA,CAAA,GAAM,CACtC,mBACEH,IAAA,QAAKI,SAAS,CAAC,qDAAqD,CAAAC,QAAA,cAClEH,KAAA,QAAKE,SAAS,CAAC,8BAA8B,CAAAC,QAAA,eAC3CH,KAAA,QAAKE,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChCH,KAAA,OAAIE,SAAS,CAAC,gDAAgD,CAAAC,QAAA,EAAC,qBAC1C,cAAAL,IAAA,SAAMI,SAAS,CAAC,iBAAiB,CAAAC,QAAA,CAAC,gBAAc,CAAM,CAAC,EACxE,CAAC,cACLL,IAAA,MAAGI,SAAS,CAAC,yCAAyC,CAAAC,QAAA,CAAC,iHAEvD,CAAG,CAAC,EACD,CAAC,cAENH,KAAA,QAAKE,SAAS,CAAC,yCAAyC,CAAAC,QAAA,eACtDL,IAAA,MAAGI,SAAS,CAAC,sDAAsD,CAAI,CAAC,cACxEJ,IAAA,OAAII,SAAS,CAAC,uCAAuC,CAAAC,QAAA,CAAC,6BAA2B,CAAI,CAAC,cACtFL,IAAA,MAAGI,SAAS,CAAC,oBAAoB,CAAAC,QAAA,CAAC,mFAElC,CAAG,CAAC,cACJH,KAAA,CAACJ,IAAI,EAACQ,EAAE,CAAC,cAAc,CAACF,SAAS,CAAC,6HAA6H,CAAAC,QAAA,eAC7JL,IAAA,MAAGI,SAAS,CAAC,wBAAwB,CAAI,CAAC,sBAE5C,EAAM,CAAC,EACJ,CAAC,EACH,CAAC,CACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAAD,yBAAyB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}