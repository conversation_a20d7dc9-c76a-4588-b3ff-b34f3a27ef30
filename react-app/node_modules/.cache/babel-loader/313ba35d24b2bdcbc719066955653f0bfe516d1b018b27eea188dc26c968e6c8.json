{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/FatbeamU/react-app/src/pages/MainPage.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Link } from 'react-router-dom';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst MainPage = ({\n  user,\n  moodleData\n}) => {\n  _s();\n  const [activeTab, setActiveTab] = useState('home');\n  useEffect(() => {\n    // Particle animation\n    const createParticles = () => {\n      const particleContainer = document.getElementById('particle-container');\n      if (!particleContainer) return;\n      for (let i = 0; i < 30; i++) {\n        const particle = document.createElement('div');\n        particle.className = 'absolute w-1 h-1 rounded-full bg-blue-500 opacity-0';\n        // Random position\n        particle.style.left = `${Math.random() * 100}%`;\n        particle.style.top = `${Math.random() * 100}%`;\n        // Random animation duration\n        const duration = 3 + Math.random() * 5;\n        particle.style.animation = `float ${duration}s ease-in-out infinite`;\n        particle.style.animationDelay = `${Math.random() * 5}s`;\n        particleContainer.appendChild(particle);\n      }\n    };\n    createParticles();\n  }, []);\n  const scrollToSection = sectionId => {\n    const section = document.getElementById(sectionId);\n    if (section) {\n      section.scrollIntoView({\n        behavior: 'smooth'\n      });\n      setActiveTab(sectionId);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-white text-gray-800 font-sans\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      id: \"particle-container\",\n      className: \"fixed inset-0 pointer-events-none z-0\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 41,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      id: \"home\",\n      className: \"relative min-h-screen flex items-center pt-20\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute inset-0 z-0 overflow-hidden\",\n        children: [/*#__PURE__*/_jsxDEV(\"img\", {\n          src: \"https://readdy.ai/api/search-image?query=A%20majestic%20magical%20castle%20with%20tall%20spires%20and%20towers%20against%20a%20dramatic%20sunset%20sky.%20The%20central%20tower%20features%20glowing%20blue%20magical%20energy%20flowing%20through%20crystalline%20formations.%20Multiple%20magical%20circular%20platforms%20with%20blue%20energy%20rings%20hover%20around%20the%20castle.%20Stone%20pathways%20lead%20to%20the%20grand%20entrance%2C%20while%20mystical%20blue%20banners%20flutter%20in%20the%20wind.%20Mountains%20and%20waterfalls%20frame%20the%20scene%20with%20ethereal%20lighting&width=1920&height=1080&seq=hero2&orientation=landscape\",\n          alt: \"Magical Castle\",\n          className: \"w-full h-full object-cover object-center animate-gentle-zoom\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 46,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute inset-0 bg-gradient-to-r from-[#201f1f]/60 via-transparent to-transparent\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 51,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute inset-0\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute bottom-0 left-0 right-0 h-32 bg-gradient-to-t from-[#201f1f]/40 to-transparent\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 53,\n            columnNumber: 13\n          }, this), Array.from({\n            length: 5\n          }).map((_, i) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute w-32 h-32 rounded-full\",\n            style: {\n              left: `${Math.random() * 100}%`,\n              top: `${Math.random() * 100}%`,\n              background: 'radial-gradient(circle, rgba(21, 167, 221, 0.2) 0%, rgba(21, 167, 221, 0) 70%)',\n              animation: `pulse ${2 + Math.random() * 2}s infinite`,\n              transform: 'translate(-50%, -50%)'\n            }\n          }, i, false, {\n            fileName: _jsxFileName,\n            lineNumber: 55,\n            columnNumber: 15\n          }, this))]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 52,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 45,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container mx-auto px-6 flex flex-col md:flex-row items-center relative z-10\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-full md:w-1/2 text-white mb-10 md:mb-0\",\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"text-4xl md:text-5xl lg:text-6xl font-serif font-bold mb-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-[#15a7dd]\",\n              children: \"Fiber Optic\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 73,\n              columnNumber: 15\n            }, this), \" Technomancy\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 72,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-lg md:text-xl mb-8 text-gray-100\",\n            children: \"Welcome to Fatbeam Fiber University, where the ancient art of magical networking meets modern fiber technology. Begin your journey into the mystical realm of high-speed connectivity.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 75,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-col sm:flex-row space-y-4 sm:space-y-0 sm:space-x-4\",\n            children: [/*#__PURE__*/_jsxDEV(Link, {\n              to: \"/library\",\n              className: \"px-8 py-3 bg-[#15a7dd] text-white rounded-full hover:bg-[#1397c7] transition-all duration-300 flex items-center justify-center rounded-button whitespace-nowrap cursor-pointer\",\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-book-open mr-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 83,\n                columnNumber: 17\n              }, this), \"Arcane Library\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 79,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/classroom\",\n              className: \"px-8 py-3 bg-transparent border-2 border-[#15a7dd] text-white rounded-full hover:bg-[#15a7dd]/20 transition-all duration-300 flex items-center justify-center rounded-button whitespace-nowrap cursor-pointer\",\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-hat-wizard mr-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 90,\n                columnNumber: 17\n              }, this), \"Training Hall\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 86,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 78,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 71,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute top-4 right-4 z-20\",\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => {\n              const heroImage = document.querySelector('.animate-gentle-zoom');\n              if (heroImage) {\n                heroImage.style.animationPlayState = heroImage.style.animationPlayState === 'paused' ? 'running' : 'paused';\n              }\n            },\n            className: \"w-12 h-12 rounded-full bg-[#15a7dd]/20 flex items-center justify-center cursor-pointer hover:bg-[#15a7dd]/30 transition-colors duration-300 rounded-button whitespace-nowrap animate-continuous-fade\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-8 h-8 rounded-full bg-[#15a7dd]/30 flex items-center justify-center\",\n              children: /*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-play text-white text-sm\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 107,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 106,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 97,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 96,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute -bottom-5 -right-5 bg-[#6a3293] text-white p-4 rounded-lg shadow-lg transform rotate-3\",\n          children: /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm font-medium\",\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fas fa-star mr-1\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 114,\n              columnNumber: 15\n            }, this), \"New Courses Added Weekly\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 113,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 112,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 70,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute bottom-10 left-1/2 transform -translate-x-1/2 text-white animate-bounce\",\n        children: /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => scrollToSection('about'),\n          className: \"flex flex-col items-center rounded-button whitespace-nowrap cursor-pointer\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-sm mb-2\",\n            children: \"Scroll to Discover\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 122,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-chevron-down\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 123,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 121,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 120,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 44,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      id: \"about\",\n      className: \"py-20 bg-[#f2f2f3]\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container mx-auto px-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center mb-16\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-3xl md:text-4xl font-serif font-bold mb-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-[#15a7dd]\",\n              children: \"About\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 133,\n              columnNumber: 15\n            }, this), \" Our Mystical Academy\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 132,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-20 h-1 bg-[#15a7dd] mx-auto\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 135,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 131,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 md:grid-cols-2 gap-12 items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-2xl font-serif font-bold mb-6 text-[#475467]\",\n              children: \"Where Technology Meets Magic\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 140,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-[#475467] mb-6\",\n              children: \"Fatbeam Fiber University was founded in the ancient year of 2010 by a council of network sorcerers who sought to blend the arcane arts with cutting-edge fiber technology. Our institution stands as a beacon of knowledge in the realm of digital enchantment.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 141,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-[#475467] mb-6\",\n              children: \"Our mystical instructors harness the power of light itself, channeling it through glass threads to connect the farthest reaches of our kingdom. Students learn to weave spells of connectivity, cast protective wards against cyber threats, and summon data from the ethereal cloud.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 144,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-12 h-12 rounded-full bg-[#15a7dd] flex items-center justify-center\",\n                children: /*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"fas fa-graduation-cap text-white\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 150,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 149,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  className: \"font-bold text-[#475467]\",\n                  children: \"500+ Graduates\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 153,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm text-[#475467]\",\n                  children: \"Master Technomancers in the field\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 154,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 152,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 148,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 139,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"relative\",\n            children: [/*#__PURE__*/_jsxDEV(\"img\", {\n              src: \"https://readdy.ai/api/search-image?query=A%20mystical%20library%20with%20floating%20holographic%20screens%20displaying%20network%20diagrams.%20Ancient%20tomes%20with%20glowing%20blue%20runes%20sit%20on%20shelves%20alongside%20modern%20server%20equipment.%20Blue%20magical%20energy%20flows%20between%20books%20and%20digital%20displays.%20Fiber%20optic%20cables%20emit%20blue%20light%20across%20the%20room&width=600&height=600&seq=about1&orientation=squarish\",\n              alt: \"Mystical Academy\",\n              className: \"w-full h-auto rounded-lg shadow-xl\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 160,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute -bottom-6 -left-6 bg-white p-4 rounded-lg shadow-lg\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-10 h-10 rounded-full bg-[#6a3293] flex items-center justify-center\",\n                  children: /*#__PURE__*/_jsxDEV(\"i\", {\n                    className: \"fas fa-magic text-white\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 168,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 167,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: \"font-bold text-[#475467]\",\n                    children: \"15 Years\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 171,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-xs text-[#475467]\",\n                    children: \"Of Magical Innovation\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 172,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 170,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 166,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 165,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 159,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 138,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 130,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 129,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      id: \"departments\",\n      className: \"py-20 bg-white\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container mx-auto px-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center mb-16\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-3xl md:text-4xl font-serif font-bold mb-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-[#15a7dd]\",\n              children: \"Mystical\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 186,\n              columnNumber: 15\n            }, this), \" Departments\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 185,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-[#475467] max-w-2xl mx-auto\",\n            children: \"Explore our specialized schools of technomancy, each focusing on a different aspect of the magical fiber arts.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 188,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-20 h-1 bg-[#15a7dd] mx-auto mt-4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 191,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 184,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\",\n          children: [{\n            title: \"Finance Alchemy\",\n            icon: \"fa-coins\",\n            route: \"/FinanceDept\",\n            image: \"https://readdy.ai/api/search-image?query=A%20mystical%20treasury%20room%20with%20floating%20gold%20coins%20and%20glowing%20blue%20financial%20charts.%20Ancient%20ledgers%20with%20magical%20runes%20sit%20beside%20modern%20computers.%20Blue%20magical%20energy%20connects%20ledgers%20to%20digital%20displays.%20Wizards%20in%20business%20attire%20cast%20financial%20spells&width=400&height=300&seq=dept1&orientation=landscape\",\n            description: \"Transform raw resources into financial prosperity through the ancient arts of budget management and revenue enchantment.\"\n          }, {\n            title: \"Tower Levitation\",\n            icon: \"fa-tower-broadcast\",\n            route: \"/TowerTechnicians\",\n            image: \"https://readdy.ai/api/search-image?query=Magical%20technicians%20working%20on%20a%20tall%20communications%20tower%20with%20glowing%20blue%20fiber%20optic%20connections.%20Wizards%20in%20safety%20gear%20casting%20maintenance%20spells.%20Blue%20energy%20flows%20through%20the%20tower%20structure.%20Technical%20diagrams%20float%20as%20magical%20holograms%20around%20the%20workers&width=400&height=300&seq=dept2&orientation=landscape\",\n            description: \"Master the heights of our magical infrastructure, ensuring the uninterrupted flow of arcane data through our network.\"\n          }, {\n            title: \"Sales Sorcery\",\n            icon: \"fa-handshake\",\n            route: \"/SalesDept\",\n            image: \"https://readdy.ai/api/search-image?query=Wizards%20in%20business%20attire%20demonstrating%20magical%20fiber%20optic%20products%20to%20clients.%20Glowing%20blue%20holograms%20show%20network%20capabilities.%20Magical%20contracts%20with%20glowing%20signatures.%20Enchanted%20presentation%20room%20with%20fiber%20optic%20decorations%20and%20blue%20energy%20flowing%20through%20displays&width=400&height=300&seq=dept3&orientation=landscape\",\n            description: \"Learn the mystical arts of persuasion and relationship building to spread our magical connectivity throughout the realm.\"\n          }, {\n            title: \"Construction Earth Magic\",\n            icon: \"fa-shovel\",\n            route: \"/ConstructionDept\",\n            image: \"https://readdy.ai/api/search-image?query=Wizards%20using%20magical%20tools%20to%20install%20underground%20fiber%20optic%20cables.%20Earth%20elementals%20helping%20to%20move%20soil.%20Blue%20glowing%20trenches%20with%20fiber%20lines%20being%20laid.%20Magical%20mapping%20tools%20projecting%20underground%20pathways.%20Construction%20site%20with%20both%20magical%20and%20modern%20equipment&width=400&height=300&seq=dept4&orientation=landscape\",\n            description: \"Harness the elements to lay the physical foundations of our network, connecting the material world to the digital realm.\"\n          }, {\n            title: \"Human Relations\",\n            icon: \"fa-brain\",\n            route: \"/HumanRelationsDept\",\n            image: \"https://readdy.ai/api/search-image?query=A%20serene%20office%20space%20with%20floating%20crystal%20orbs%20showing%20employee%20auras.%20Psychic%20HR%20wizards%20conducting%20telepathic%20interviews.%20Glowing%20blue%20filing%20cabinets%20with%20magical%20employee%20records.%20Meditation%20areas%20with%20floating%20comfort%20crystals.%20Ethereal%20blue%20energy%20flows%20connecting%20minds&width=400&height=300&seq=dept5&orientation=landscape\",\n            description: \"Harness psychic energies to maintain harmony among our magical workforce, managing personnel records with telepathic precision and empathic understanding.\"\n          }, {\n            title: \"Network Weavers\",\n            icon: \"fa-diagram-project\",\n            route: \"/NetworkOperations\",\n            image: \"https://readdy.ai/api/search-image?query=Technical%20wizards%20working%20with%20complex%20floating%20network%20diagrams%20made%20of%20blue%20light.%20Magical%20server%20rooms%20with%20glowing%20fiber%20connections.%20Engineers%20casting%20spells%20to%20optimize%20data%20flow.%20Enchanted%20tools%20analyzing%20network%20performance%20with%20magical%20visualizations&width=400&height=300&seq=dept6&orientation=landscape\",\n            description: \"Craft the invisible pathways of data, ensuring seamless communication across the vast distances of our kingdom.\"\n          }].map((dept, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-lg overflow-hidden shadow-md hover:shadow-xl transition-all duration-300 group cursor-pointer\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative h-48 overflow-hidden\",\n              children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                src: dept.image,\n                alt: dept.title,\n                className: \"w-full h-full object-cover transition-transform duration-500 group-hover:scale-110\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 241,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute inset-0 bg-gradient-to-t from-[#201f1f] to-transparent opacity-70 transition-opacity duration-300 group-hover:opacity-50\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 246,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute bottom-4 left-4 text-white\",\n                children: /*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"text-xl font-serif font-bold\",\n                  children: dept.title\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 248,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 247,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute top-4 right-4 w-10 h-10 rounded-full bg-[#15a7dd]/90 flex items-center justify-center shadow-lg backdrop-blur-sm ring-2 ring-white/30\",\n                children: /*#__PURE__*/_jsxDEV(\"i\", {\n                  className: `fas ${dept.icon} text-white text-lg drop-shadow-[0_2px_4px_rgba(255,255,255,0.3)]`\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 251,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 250,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 240,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-[#475467] mb-4\",\n                children: dept.description\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 255,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Link, {\n                to: dept.route,\n                className: \"text-[#15a7dd] font-medium flex items-center rounded-button whitespace-nowrap cursor-pointer\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"Explore Department\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 260,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"fas fa-arrow-right ml-2 transition-transform duration-300 group-hover:translate-x-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 261,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 256,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 254,\n              columnNumber: 17\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 239,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 194,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-16 text-center\",\n          children: /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/departments\",\n            className: \"px-8 py-3 bg-[#15a7dd] text-white rounded-full hover:bg-[#1397c7] transition-all duration-300 rounded-button whitespace-nowrap cursor-pointer inline-flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fas fa-book mr-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 270,\n              columnNumber: 15\n            }, this), \"View All Departments\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 269,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 268,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 183,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 182,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 39,\n    columnNumber: 5\n  }, this);\n};\n_s(MainPage, \"EVO1U2BM8qaC0GYgH2cTSj0mwhk=\");\n_c = MainPage;\nexport default MainPage;\nvar _c;\n$RefreshReg$(_c, \"MainPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Link", "jsxDEV", "_jsxDEV", "MainPage", "user", "moodleData", "_s", "activeTab", "setActiveTab", "createParticles", "particleContainer", "document", "getElementById", "i", "particle", "createElement", "className", "style", "left", "Math", "random", "top", "duration", "animation", "animationDelay", "append<PERSON><PERSON><PERSON>", "scrollToSection", "sectionId", "section", "scrollIntoView", "behavior", "children", "id", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "src", "alt", "Array", "from", "length", "map", "_", "background", "transform", "to", "onClick", "heroImage", "querySelector", "animationPlayState", "title", "icon", "route", "image", "description", "dept", "index", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/FatbeamU/react-app/src/pages/MainPage.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Link } from 'react-router-dom';\n\nconst MainPage = ({ user, moodleData }) => {\n  const [activeTab, setActiveTab] = useState('home');\n\n  useEffect(() => {\n    // Particle animation\n    const createParticles = () => {\n      const particleContainer = document.getElementById('particle-container');\n      if (!particleContainer) return;\n\n      for (let i = 0; i < 30; i++) {\n        const particle = document.createElement('div');\n        particle.className = 'absolute w-1 h-1 rounded-full bg-blue-500 opacity-0';\n        // Random position\n        particle.style.left = `${Math.random() * 100}%`;\n        particle.style.top = `${Math.random() * 100}%`;\n        // Random animation duration\n        const duration = 3 + Math.random() * 5;\n        particle.style.animation = `float ${duration}s ease-in-out infinite`;\n        particle.style.animationDelay = `${Math.random() * 5}s`;\n        particleContainer.appendChild(particle);\n      }\n    };\n\n    createParticles();\n  }, []);\n\n  const scrollToSection = (sectionId) => {\n    const section = document.getElementById(sectionId);\n    if (section) {\n      section.scrollIntoView({ behavior: 'smooth' });\n      setActiveTab(sectionId);\n    }\n  };\n\n  return (\n    <div className=\"min-h-screen bg-white text-gray-800 font-sans\">\n      {/* Particle container for magical effects */}\n      <div id=\"particle-container\" className=\"fixed inset-0 pointer-events-none z-0\"></div>\n\n      {/* Hero Section */}\n      <section id=\"home\" className=\"relative min-h-screen flex items-center pt-20\">\n        <div className=\"absolute inset-0 z-0 overflow-hidden\">\n          <img\n            src=\"https://readdy.ai/api/search-image?query=A%20majestic%20magical%20castle%20with%20tall%20spires%20and%20towers%20against%20a%20dramatic%20sunset%20sky.%20The%20central%20tower%20features%20glowing%20blue%20magical%20energy%20flowing%20through%20crystalline%20formations.%20Multiple%20magical%20circular%20platforms%20with%20blue%20energy%20rings%20hover%20around%20the%20castle.%20Stone%20pathways%20lead%20to%20the%20grand%20entrance%2C%20while%20mystical%20blue%20banners%20flutter%20in%20the%20wind.%20Mountains%20and%20waterfalls%20frame%20the%20scene%20with%20ethereal%20lighting&width=1920&height=1080&seq=hero2&orientation=landscape\"\n            alt=\"Magical Castle\"\n            className=\"w-full h-full object-cover object-center animate-gentle-zoom\"\n          />\n          <div className=\"absolute inset-0 bg-gradient-to-r from-[#201f1f]/60 via-transparent to-transparent\"></div>\n          <div className=\"absolute inset-0\">\n            <div className=\"absolute bottom-0 left-0 right-0 h-32 bg-gradient-to-t from-[#201f1f]/40 to-transparent\"></div>\n            {Array.from({ length: 5 }).map((_, i) => (\n              <div\n                key={i}\n                className=\"absolute w-32 h-32 rounded-full\"\n                style={{\n                  left: `${Math.random() * 100}%`,\n                  top: `${Math.random() * 100}%`,\n                  background: 'radial-gradient(circle, rgba(21, 167, 221, 0.2) 0%, rgba(21, 167, 221, 0) 70%)',\n                  animation: `pulse ${2 + Math.random() * 2}s infinite`,\n                  transform: 'translate(-50%, -50%)'\n                }}\n              />\n            ))}\n          </div>\n        </div>\n\n        <div className=\"container mx-auto px-6 flex flex-col md:flex-row items-center relative z-10\">\n          <div className=\"w-full md:w-1/2 text-white mb-10 md:mb-0\">\n            <h1 className=\"text-4xl md:text-5xl lg:text-6xl font-serif font-bold mb-6\">\n              <span className=\"text-[#15a7dd]\">Fiber Optic</span> Technomancy\n            </h1>\n            <p className=\"text-lg md:text-xl mb-8 text-gray-100\">\n              Welcome to Fatbeam Fiber University, where the ancient art of magical networking meets modern fiber technology. Begin your journey into the mystical realm of high-speed connectivity.\n            </p>\n            <div className=\"flex flex-col sm:flex-row space-y-4 sm:space-y-0 sm:space-x-4\">\n              <Link\n                to=\"/library\"\n                className=\"px-8 py-3 bg-[#15a7dd] text-white rounded-full hover:bg-[#1397c7] transition-all duration-300 flex items-center justify-center rounded-button whitespace-nowrap cursor-pointer\"\n              >\n                <i className=\"fas fa-book-open mr-2\"></i>\n                Arcane Library\n              </Link>\n              <Link\n                to=\"/classroom\"\n                className=\"px-8 py-3 bg-transparent border-2 border-[#15a7dd] text-white rounded-full hover:bg-[#15a7dd]/20 transition-all duration-300 flex items-center justify-center rounded-button whitespace-nowrap cursor-pointer\"\n              >\n                <i className=\"fas fa-hat-wizard mr-2\"></i>\n                Training Hall\n              </Link>\n            </div>\n          </div>\n\n          <div className=\"absolute top-4 right-4 z-20\">\n            <button\n              onClick={() => {\n                const heroImage = document.querySelector('.animate-gentle-zoom');\n                if (heroImage) {\n                  heroImage.style.animationPlayState = heroImage.style.animationPlayState === 'paused' ? 'running' : 'paused';\n                }\n              }}\n              className=\"w-12 h-12 rounded-full bg-[#15a7dd]/20 flex items-center justify-center cursor-pointer hover:bg-[#15a7dd]/30 transition-colors duration-300 rounded-button whitespace-nowrap animate-continuous-fade\"\n            >\n              <div className=\"w-8 h-8 rounded-full bg-[#15a7dd]/30 flex items-center justify-center\">\n                <i className=\"fas fa-play text-white text-sm\"></i>\n              </div>\n            </button>\n          </div>\n\n          <div className=\"absolute -bottom-5 -right-5 bg-[#6a3293] text-white p-4 rounded-lg shadow-lg transform rotate-3\">\n            <p className=\"text-sm font-medium\">\n              <i className=\"fas fa-star mr-1\"></i>\n              New Courses Added Weekly\n            </p>\n          </div>\n        </div>\n\n        <div className=\"absolute bottom-10 left-1/2 transform -translate-x-1/2 text-white animate-bounce\">\n          <button onClick={() => scrollToSection('about')} className=\"flex flex-col items-center rounded-button whitespace-nowrap cursor-pointer\">\n            <span className=\"text-sm mb-2\">Scroll to Discover</span>\n            <i className=\"fas fa-chevron-down\"></i>\n          </button>\n        </div>\n      </section>\n\n      {/* About Section */}\n      <section id=\"about\" className=\"py-20 bg-[#f2f2f3]\">\n        <div className=\"container mx-auto px-6\">\n          <div className=\"text-center mb-16\">\n            <h2 className=\"text-3xl md:text-4xl font-serif font-bold mb-4\">\n              <span className=\"text-[#15a7dd]\">About</span> Our Mystical Academy\n            </h2>\n            <div className=\"w-20 h-1 bg-[#15a7dd] mx-auto\"></div>\n          </div>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-12 items-center\">\n            <div>\n              <h3 className=\"text-2xl font-serif font-bold mb-6 text-[#475467]\">Where Technology Meets Magic</h3>\n              <p className=\"text-[#475467] mb-6\">\n                Fatbeam Fiber University was founded in the ancient year of 2010 by a council of network sorcerers who sought to blend the arcane arts with cutting-edge fiber technology. Our institution stands as a beacon of knowledge in the realm of digital enchantment.\n              </p>\n              <p className=\"text-[#475467] mb-6\">\n                Our mystical instructors harness the power of light itself, channeling it through glass threads to connect the farthest reaches of our kingdom. Students learn to weave spells of connectivity, cast protective wards against cyber threats, and summon data from the ethereal cloud.\n              </p>\n\n              <div className=\"flex items-center space-x-4\">\n                <div className=\"w-12 h-12 rounded-full bg-[#15a7dd] flex items-center justify-center\">\n                  <i className=\"fas fa-graduation-cap text-white\"></i>\n                </div>\n                <div>\n                  <h4 className=\"font-bold text-[#475467]\">500+ Graduates</h4>\n                  <p className=\"text-sm text-[#475467]\">Master Technomancers in the field</p>\n                </div>\n              </div>\n            </div>\n\n            <div className=\"relative\">\n              <img\n                src=\"https://readdy.ai/api/search-image?query=A%20mystical%20library%20with%20floating%20holographic%20screens%20displaying%20network%20diagrams.%20Ancient%20tomes%20with%20glowing%20blue%20runes%20sit%20on%20shelves%20alongside%20modern%20server%20equipment.%20Blue%20magical%20energy%20flows%20between%20books%20and%20digital%20displays.%20Fiber%20optic%20cables%20emit%20blue%20light%20across%20the%20room&width=600&height=600&seq=about1&orientation=squarish\"\n                alt=\"Mystical Academy\"\n                className=\"w-full h-auto rounded-lg shadow-xl\"\n              />\n              <div className=\"absolute -bottom-6 -left-6 bg-white p-4 rounded-lg shadow-lg\">\n                <div className=\"flex items-center space-x-2\">\n                  <div className=\"w-10 h-10 rounded-full bg-[#6a3293] flex items-center justify-center\">\n                    <i className=\"fas fa-magic text-white\"></i>\n                  </div>\n                  <div>\n                    <h4 className=\"font-bold text-[#475467]\">15 Years</h4>\n                    <p className=\"text-xs text-[#475467]\">Of Magical Innovation</p>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Departments Section */}\n      <section id=\"departments\" className=\"py-20 bg-white\">\n        <div className=\"container mx-auto px-6\">\n          <div className=\"text-center mb-16\">\n            <h2 className=\"text-3xl md:text-4xl font-serif font-bold mb-4\">\n              <span className=\"text-[#15a7dd]\">Mystical</span> Departments\n            </h2>\n            <p className=\"text-[#475467] max-w-2xl mx-auto\">\n              Explore our specialized schools of technomancy, each focusing on a different aspect of the magical fiber arts.\n            </p>\n            <div className=\"w-20 h-1 bg-[#15a7dd] mx-auto mt-4\"></div>\n          </div>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\">\n            {[\n              {\n                title: \"Finance Alchemy\",\n                icon: \"fa-coins\",\n                route: \"/FinanceDept\",\n                image: \"https://readdy.ai/api/search-image?query=A%20mystical%20treasury%20room%20with%20floating%20gold%20coins%20and%20glowing%20blue%20financial%20charts.%20Ancient%20ledgers%20with%20magical%20runes%20sit%20beside%20modern%20computers.%20Blue%20magical%20energy%20connects%20ledgers%20to%20digital%20displays.%20Wizards%20in%20business%20attire%20cast%20financial%20spells&width=400&height=300&seq=dept1&orientation=landscape\",\n                description: \"Transform raw resources into financial prosperity through the ancient arts of budget management and revenue enchantment.\"\n              },\n              {\n                title: \"Tower Levitation\",\n                icon: \"fa-tower-broadcast\",\n                route: \"/TowerTechnicians\",\n                image: \"https://readdy.ai/api/search-image?query=Magical%20technicians%20working%20on%20a%20tall%20communications%20tower%20with%20glowing%20blue%20fiber%20optic%20connections.%20Wizards%20in%20safety%20gear%20casting%20maintenance%20spells.%20Blue%20energy%20flows%20through%20the%20tower%20structure.%20Technical%20diagrams%20float%20as%20magical%20holograms%20around%20the%20workers&width=400&height=300&seq=dept2&orientation=landscape\",\n                description: \"Master the heights of our magical infrastructure, ensuring the uninterrupted flow of arcane data through our network.\"\n              },\n              {\n                title: \"Sales Sorcery\",\n                icon: \"fa-handshake\",\n                route: \"/SalesDept\",\n                image: \"https://readdy.ai/api/search-image?query=Wizards%20in%20business%20attire%20demonstrating%20magical%20fiber%20optic%20products%20to%20clients.%20Glowing%20blue%20holograms%20show%20network%20capabilities.%20Magical%20contracts%20with%20glowing%20signatures.%20Enchanted%20presentation%20room%20with%20fiber%20optic%20decorations%20and%20blue%20energy%20flowing%20through%20displays&width=400&height=300&seq=dept3&orientation=landscape\",\n                description: \"Learn the mystical arts of persuasion and relationship building to spread our magical connectivity throughout the realm.\"\n              },\n              {\n                title: \"Construction Earth Magic\",\n                icon: \"fa-shovel\",\n                route: \"/ConstructionDept\",\n                image: \"https://readdy.ai/api/search-image?query=Wizards%20using%20magical%20tools%20to%20install%20underground%20fiber%20optic%20cables.%20Earth%20elementals%20helping%20to%20move%20soil.%20Blue%20glowing%20trenches%20with%20fiber%20lines%20being%20laid.%20Magical%20mapping%20tools%20projecting%20underground%20pathways.%20Construction%20site%20with%20both%20magical%20and%20modern%20equipment&width=400&height=300&seq=dept4&orientation=landscape\",\n                description: \"Harness the elements to lay the physical foundations of our network, connecting the material world to the digital realm.\"\n              },\n              {\n                title: \"Human Relations\",\n                icon: \"fa-brain\",\n                route: \"/HumanRelationsDept\",\n                image: \"https://readdy.ai/api/search-image?query=A%20serene%20office%20space%20with%20floating%20crystal%20orbs%20showing%20employee%20auras.%20Psychic%20HR%20wizards%20conducting%20telepathic%20interviews.%20Glowing%20blue%20filing%20cabinets%20with%20magical%20employee%20records.%20Meditation%20areas%20with%20floating%20comfort%20crystals.%20Ethereal%20blue%20energy%20flows%20connecting%20minds&width=400&height=300&seq=dept5&orientation=landscape\",\n                description: \"Harness psychic energies to maintain harmony among our magical workforce, managing personnel records with telepathic precision and empathic understanding.\"\n              },\n              {\n                title: \"Network Weavers\",\n                icon: \"fa-diagram-project\",\n                route: \"/NetworkOperations\",\n                image: \"https://readdy.ai/api/search-image?query=Technical%20wizards%20working%20with%20complex%20floating%20network%20diagrams%20made%20of%20blue%20light.%20Magical%20server%20rooms%20with%20glowing%20fiber%20connections.%20Engineers%20casting%20spells%20to%20optimize%20data%20flow.%20Enchanted%20tools%20analyzing%20network%20performance%20with%20magical%20visualizations&width=400&height=300&seq=dept6&orientation=landscape\",\n                description: \"Craft the invisible pathways of data, ensuring seamless communication across the vast distances of our kingdom.\"\n              }\n            ].map((dept, index) => (\n              <div key={index} className=\"bg-white rounded-lg overflow-hidden shadow-md hover:shadow-xl transition-all duration-300 group cursor-pointer\">\n                <div className=\"relative h-48 overflow-hidden\">\n                  <img\n                    src={dept.image}\n                    alt={dept.title}\n                    className=\"w-full h-full object-cover transition-transform duration-500 group-hover:scale-110\"\n                  />\n                  <div className=\"absolute inset-0 bg-gradient-to-t from-[#201f1f] to-transparent opacity-70 transition-opacity duration-300 group-hover:opacity-50\"></div>\n                  <div className=\"absolute bottom-4 left-4 text-white\">\n                    <h3 className=\"text-xl font-serif font-bold\">{dept.title}</h3>\n                  </div>\n                  <div className=\"absolute top-4 right-4 w-10 h-10 rounded-full bg-[#15a7dd]/90 flex items-center justify-center shadow-lg backdrop-blur-sm ring-2 ring-white/30\">\n                    <i className={`fas ${dept.icon} text-white text-lg drop-shadow-[0_2px_4px_rgba(255,255,255,0.3)]`}></i>\n                  </div>\n                </div>\n                <div className=\"p-6\">\n                  <p className=\"text-[#475467] mb-4\">{dept.description}</p>\n                  <Link\n                    to={dept.route}\n                    className=\"text-[#15a7dd] font-medium flex items-center rounded-button whitespace-nowrap cursor-pointer\"\n                  >\n                    <span>Explore Department</span>\n                    <i className=\"fas fa-arrow-right ml-2 transition-transform duration-300 group-hover:translate-x-2\"></i>\n                  </Link>\n                </div>\n              </div>\n            ))}\n          </div>\n\n          <div className=\"mt-16 text-center\">\n            <Link to=\"/departments\" className=\"px-8 py-3 bg-[#15a7dd] text-white rounded-full hover:bg-[#1397c7] transition-all duration-300 rounded-button whitespace-nowrap cursor-pointer inline-flex items-center\">\n              <i className=\"fas fa-book mr-2\"></i>\n              View All Departments\n            </Link>\n          </div>\n        </div>\n      </section>\n    </div>\n  );\n};\n\nexport default MainPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,IAAI,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExC,MAAMC,QAAQ,GAAGA,CAAC;EAAEC,IAAI;EAAEC;AAAW,CAAC,KAAK;EAAAC,EAAA;EACzC,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGV,QAAQ,CAAC,MAAM,CAAC;EAElDC,SAAS,CAAC,MAAM;IACd;IACA,MAAMU,eAAe,GAAGA,CAAA,KAAM;MAC5B,MAAMC,iBAAiB,GAAGC,QAAQ,CAACC,cAAc,CAAC,oBAAoB,CAAC;MACvE,IAAI,CAACF,iBAAiB,EAAE;MAExB,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE;QAC3B,MAAMC,QAAQ,GAAGH,QAAQ,CAACI,aAAa,CAAC,KAAK,CAAC;QAC9CD,QAAQ,CAACE,SAAS,GAAG,qDAAqD;QAC1E;QACAF,QAAQ,CAACG,KAAK,CAACC,IAAI,GAAG,GAAGC,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG;QAC/CN,QAAQ,CAACG,KAAK,CAACI,GAAG,GAAG,GAAGF,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG;QAC9C;QACA,MAAME,QAAQ,GAAG,CAAC,GAAGH,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,CAAC;QACtCN,QAAQ,CAACG,KAAK,CAACM,SAAS,GAAG,SAASD,QAAQ,wBAAwB;QACpER,QAAQ,CAACG,KAAK,CAACO,cAAc,GAAG,GAAGL,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG;QACvDV,iBAAiB,CAACe,WAAW,CAACX,QAAQ,CAAC;MACzC;IACF,CAAC;IAEDL,eAAe,CAAC,CAAC;EACnB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMiB,eAAe,GAAIC,SAAS,IAAK;IACrC,MAAMC,OAAO,GAAGjB,QAAQ,CAACC,cAAc,CAACe,SAAS,CAAC;IAClD,IAAIC,OAAO,EAAE;MACXA,OAAO,CAACC,cAAc,CAAC;QAAEC,QAAQ,EAAE;MAAS,CAAC,CAAC;MAC9CtB,YAAY,CAACmB,SAAS,CAAC;IACzB;EACF,CAAC;EAED,oBACEzB,OAAA;IAAKc,SAAS,EAAC,+CAA+C;IAAAe,QAAA,gBAE5D7B,OAAA;MAAK8B,EAAE,EAAC,oBAAoB;MAAChB,SAAS,EAAC;IAAuC;MAAAiB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,eAGrFlC,OAAA;MAAS8B,EAAE,EAAC,MAAM;MAAChB,SAAS,EAAC,+CAA+C;MAAAe,QAAA,gBAC1E7B,OAAA;QAAKc,SAAS,EAAC,sCAAsC;QAAAe,QAAA,gBACnD7B,OAAA;UACEmC,GAAG,EAAC,ioBAAioB;UACroBC,GAAG,EAAC,gBAAgB;UACpBtB,SAAS,EAAC;QAA8D;UAAAiB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzE,CAAC,eACFlC,OAAA;UAAKc,SAAS,EAAC;QAAoF;UAAAiB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC1GlC,OAAA;UAAKc,SAAS,EAAC,kBAAkB;UAAAe,QAAA,gBAC/B7B,OAAA;YAAKc,SAAS,EAAC;UAAyF;YAAAiB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,EAC9GG,KAAK,CAACC,IAAI,CAAC;YAAEC,MAAM,EAAE;UAAE,CAAC,CAAC,CAACC,GAAG,CAAC,CAACC,CAAC,EAAE9B,CAAC,kBAClCX,OAAA;YAEEc,SAAS,EAAC,iCAAiC;YAC3CC,KAAK,EAAE;cACLC,IAAI,EAAE,GAAGC,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG;cAC/BC,GAAG,EAAE,GAAGF,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG;cAC9BwB,UAAU,EAAE,gFAAgF;cAC5FrB,SAAS,EAAE,SAAS,CAAC,GAAGJ,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,CAAC,YAAY;cACrDyB,SAAS,EAAE;YACb;UAAE,GARGhC,CAAC;YAAAoB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OASP,CACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENlC,OAAA;QAAKc,SAAS,EAAC,6EAA6E;QAAAe,QAAA,gBAC1F7B,OAAA;UAAKc,SAAS,EAAC,0CAA0C;UAAAe,QAAA,gBACvD7B,OAAA;YAAIc,SAAS,EAAC,4DAA4D;YAAAe,QAAA,gBACxE7B,OAAA;cAAMc,SAAS,EAAC,gBAAgB;cAAAe,QAAA,EAAC;YAAW;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,gBACrD;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLlC,OAAA;YAAGc,SAAS,EAAC,uCAAuC;YAAAe,QAAA,EAAC;UAErD;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJlC,OAAA;YAAKc,SAAS,EAAC,+DAA+D;YAAAe,QAAA,gBAC5E7B,OAAA,CAACF,IAAI;cACH8C,EAAE,EAAC,UAAU;cACb9B,SAAS,EAAC,gLAAgL;cAAAe,QAAA,gBAE1L7B,OAAA;gBAAGc,SAAS,EAAC;cAAuB;gBAAAiB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,kBAE3C;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACPlC,OAAA,CAACF,IAAI;cACH8C,EAAE,EAAC,YAAY;cACf9B,SAAS,EAAC,+MAA+M;cAAAe,QAAA,gBAEzN7B,OAAA;gBAAGc,SAAS,EAAC;cAAwB;gBAAAiB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,iBAE5C;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENlC,OAAA;UAAKc,SAAS,EAAC,6BAA6B;UAAAe,QAAA,eAC1C7B,OAAA;YACE6C,OAAO,EAAEA,CAAA,KAAM;cACb,MAAMC,SAAS,GAAGrC,QAAQ,CAACsC,aAAa,CAAC,sBAAsB,CAAC;cAChE,IAAID,SAAS,EAAE;gBACbA,SAAS,CAAC/B,KAAK,CAACiC,kBAAkB,GAAGF,SAAS,CAAC/B,KAAK,CAACiC,kBAAkB,KAAK,QAAQ,GAAG,SAAS,GAAG,QAAQ;cAC7G;YACF,CAAE;YACFlC,SAAS,EAAC,sMAAsM;YAAAe,QAAA,eAEhN7B,OAAA;cAAKc,SAAS,EAAC,uEAAuE;cAAAe,QAAA,eACpF7B,OAAA;gBAAGc,SAAS,EAAC;cAAgC;gBAAAiB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/C;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAENlC,OAAA;UAAKc,SAAS,EAAC,iGAAiG;UAAAe,QAAA,eAC9G7B,OAAA;YAAGc,SAAS,EAAC,qBAAqB;YAAAe,QAAA,gBAChC7B,OAAA;cAAGc,SAAS,EAAC;YAAkB;cAAAiB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,4BAEtC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENlC,OAAA;QAAKc,SAAS,EAAC,kFAAkF;QAAAe,QAAA,eAC/F7B,OAAA;UAAQ6C,OAAO,EAAEA,CAAA,KAAMrB,eAAe,CAAC,OAAO,CAAE;UAACV,SAAS,EAAC,4EAA4E;UAAAe,QAAA,gBACrI7B,OAAA;YAAMc,SAAS,EAAC,cAAc;YAAAe,QAAA,EAAC;UAAkB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACxDlC,OAAA;YAAGc,SAAS,EAAC;UAAqB;YAAAiB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVlC,OAAA;MAAS8B,EAAE,EAAC,OAAO;MAAChB,SAAS,EAAC,oBAAoB;MAAAe,QAAA,eAChD7B,OAAA;QAAKc,SAAS,EAAC,wBAAwB;QAAAe,QAAA,gBACrC7B,OAAA;UAAKc,SAAS,EAAC,mBAAmB;UAAAe,QAAA,gBAChC7B,OAAA;YAAIc,SAAS,EAAC,gDAAgD;YAAAe,QAAA,gBAC5D7B,OAAA;cAAMc,SAAS,EAAC,gBAAgB;cAAAe,QAAA,EAAC;YAAK;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,yBAC/C;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLlC,OAAA;YAAKc,SAAS,EAAC;UAA+B;YAAAiB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClD,CAAC,eAENlC,OAAA;UAAKc,SAAS,EAAC,qDAAqD;UAAAe,QAAA,gBAClE7B,OAAA;YAAA6B,QAAA,gBACE7B,OAAA;cAAIc,SAAS,EAAC,mDAAmD;cAAAe,QAAA,EAAC;YAA4B;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACnGlC,OAAA;cAAGc,SAAS,EAAC,qBAAqB;cAAAe,QAAA,EAAC;YAEnC;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACJlC,OAAA;cAAGc,SAAS,EAAC,qBAAqB;cAAAe,QAAA,EAAC;YAEnC;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAEJlC,OAAA;cAAKc,SAAS,EAAC,6BAA6B;cAAAe,QAAA,gBAC1C7B,OAAA;gBAAKc,SAAS,EAAC,sEAAsE;gBAAAe,QAAA,eACnF7B,OAAA;kBAAGc,SAAS,EAAC;gBAAkC;kBAAAiB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjD,CAAC,eACNlC,OAAA;gBAAA6B,QAAA,gBACE7B,OAAA;kBAAIc,SAAS,EAAC,0BAA0B;kBAAAe,QAAA,EAAC;gBAAc;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC5DlC,OAAA;kBAAGc,SAAS,EAAC,wBAAwB;kBAAAe,QAAA,EAAC;gBAAiC;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENlC,OAAA;YAAKc,SAAS,EAAC,UAAU;YAAAe,QAAA,gBACvB7B,OAAA;cACEmC,GAAG,EAAC,0cAA0c;cAC9cC,GAAG,EAAC,kBAAkB;cACtBtB,SAAS,EAAC;YAAoC;cAAAiB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/C,CAAC,eACFlC,OAAA;cAAKc,SAAS,EAAC,8DAA8D;cAAAe,QAAA,eAC3E7B,OAAA;gBAAKc,SAAS,EAAC,6BAA6B;gBAAAe,QAAA,gBAC1C7B,OAAA;kBAAKc,SAAS,EAAC,sEAAsE;kBAAAe,QAAA,eACnF7B,OAAA;oBAAGc,SAAS,EAAC;kBAAyB;oBAAAiB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxC,CAAC,eACNlC,OAAA;kBAAA6B,QAAA,gBACE7B,OAAA;oBAAIc,SAAS,EAAC,0BAA0B;oBAAAe,QAAA,EAAC;kBAAQ;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACtDlC,OAAA;oBAAGc,SAAS,EAAC,wBAAwB;oBAAAe,QAAA,EAAC;kBAAqB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5D,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVlC,OAAA;MAAS8B,EAAE,EAAC,aAAa;MAAChB,SAAS,EAAC,gBAAgB;MAAAe,QAAA,eAClD7B,OAAA;QAAKc,SAAS,EAAC,wBAAwB;QAAAe,QAAA,gBACrC7B,OAAA;UAAKc,SAAS,EAAC,mBAAmB;UAAAe,QAAA,gBAChC7B,OAAA;YAAIc,SAAS,EAAC,gDAAgD;YAAAe,QAAA,gBAC5D7B,OAAA;cAAMc,SAAS,EAAC,gBAAgB;cAAAe,QAAA,EAAC;YAAQ;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,gBAClD;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLlC,OAAA;YAAGc,SAAS,EAAC,kCAAkC;YAAAe,QAAA,EAAC;UAEhD;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJlC,OAAA;YAAKc,SAAS,EAAC;UAAoC;YAAAiB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvD,CAAC,eAENlC,OAAA;UAAKc,SAAS,EAAC,sDAAsD;UAAAe,QAAA,EAClE,CACC;YACEoB,KAAK,EAAE,iBAAiB;YACxBC,IAAI,EAAE,UAAU;YAChBC,KAAK,EAAE,cAAc;YACrBC,KAAK,EAAE,uaAAua;YAC9aC,WAAW,EAAE;UACf,CAAC,EACD;YACEJ,KAAK,EAAE,kBAAkB;YACzBC,IAAI,EAAE,oBAAoB;YAC1BC,KAAK,EAAE,mBAAmB;YAC1BC,KAAK,EAAE,gbAAgb;YACvbC,WAAW,EAAE;UACf,CAAC,EACD;YACEJ,KAAK,EAAE,eAAe;YACtBC,IAAI,EAAE,cAAc;YACpBC,KAAK,EAAE,YAAY;YACnBC,KAAK,EAAE,obAAob;YAC3bC,WAAW,EAAE;UACf,CAAC,EACD;YACEJ,KAAK,EAAE,0BAA0B;YACjCC,IAAI,EAAE,WAAW;YACjBC,KAAK,EAAE,mBAAmB;YAC1BC,KAAK,EAAE,0bAA0b;YACjcC,WAAW,EAAE;UACf,CAAC,EACD;YACEJ,KAAK,EAAE,iBAAiB;YACxBC,IAAI,EAAE,UAAU;YAChBC,KAAK,EAAE,qBAAqB;YAC5BC,KAAK,EAAE,8bAA8b;YACrcC,WAAW,EAAE;UACf,CAAC,EACD;YACEJ,KAAK,EAAE,iBAAiB;YACxBC,IAAI,EAAE,oBAAoB;YAC1BC,KAAK,EAAE,oBAAoB;YAC3BC,KAAK,EAAE,qaAAqa;YAC5aC,WAAW,EAAE;UACf,CAAC,CACF,CAACb,GAAG,CAAC,CAACc,IAAI,EAAEC,KAAK,kBAChBvD,OAAA;YAAiBc,SAAS,EAAC,gHAAgH;YAAAe,QAAA,gBACzI7B,OAAA;cAAKc,SAAS,EAAC,+BAA+B;cAAAe,QAAA,gBAC5C7B,OAAA;gBACEmC,GAAG,EAAEmB,IAAI,CAACF,KAAM;gBAChBhB,GAAG,EAAEkB,IAAI,CAACL,KAAM;gBAChBnC,SAAS,EAAC;cAAoF;gBAAAiB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/F,CAAC,eACFlC,OAAA;gBAAKc,SAAS,EAAC;cAAmI;gBAAAiB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACzJlC,OAAA;gBAAKc,SAAS,EAAC,qCAAqC;gBAAAe,QAAA,eAClD7B,OAAA;kBAAIc,SAAS,EAAC,8BAA8B;kBAAAe,QAAA,EAAEyB,IAAI,CAACL;gBAAK;kBAAAlB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3D,CAAC,eACNlC,OAAA;gBAAKc,SAAS,EAAC,gJAAgJ;gBAAAe,QAAA,eAC7J7B,OAAA;kBAAGc,SAAS,EAAE,OAAOwC,IAAI,CAACJ,IAAI;gBAAoE;kBAAAnB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNlC,OAAA;cAAKc,SAAS,EAAC,KAAK;cAAAe,QAAA,gBAClB7B,OAAA;gBAAGc,SAAS,EAAC,qBAAqB;gBAAAe,QAAA,EAAEyB,IAAI,CAACD;cAAW;gBAAAtB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACzDlC,OAAA,CAACF,IAAI;gBACH8C,EAAE,EAAEU,IAAI,CAACH,KAAM;gBACfrC,SAAS,EAAC,8FAA8F;gBAAAe,QAAA,gBAExG7B,OAAA;kBAAA6B,QAAA,EAAM;gBAAkB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC/BlC,OAAA;kBAAGc,SAAS,EAAC;gBAAqF;kBAAAiB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA,GAxBEqB,KAAK;YAAAxB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAyBV,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAENlC,OAAA;UAAKc,SAAS,EAAC,mBAAmB;UAAAe,QAAA,eAChC7B,OAAA,CAACF,IAAI;YAAC8C,EAAE,EAAC,cAAc;YAAC9B,SAAS,EAAC,wKAAwK;YAAAe,QAAA,gBACxM7B,OAAA;cAAGc,SAAS,EAAC;YAAkB;cAAAiB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,wBAEtC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV,CAAC;AAAC9B,EAAA,CAlRIH,QAAQ;AAAAuD,EAAA,GAARvD,QAAQ;AAoRd,eAAeA,QAAQ;AAAC,IAAAuD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}