{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/FatbeamU/react-app/src/pages/ClassroomPage.js\",\n  _s = $RefreshSig$();\n// Converted from ClassroomSplash-Page.tsx - The exported code uses Tailwind CSS\nimport React, { useState, useEffect, useRef } from 'react';\nimport { Link } from 'react-router-dom';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ClassroomPage = ({\n  user,\n  moodleData\n}) => {\n  _s();\n  const [sideMenuOpen, setSideMenuOpen] = useState(false);\n  const [activeTab, setActiveTab] = useState('dashboard');\n  const [hoverCourse, setHoverCourse] = useState(null);\n  const previewPlayerRef = useRef(null);\n  const progressChartRef = useRef(null);\n  useEffect(() => {\n    // Simplified chart implementation for now\n    if (progressChartRef.current) {\n      // Chart initialization would go here\n      // For now, we'll use a simple implementation\n    }\n  }, []);\n  const toggleSideMenu = () => {\n    setSideMenuOpen(!sideMenuOpen);\n  };\n  const handleCourseHover = index => {\n    setHoverCourse(index);\n    if (previewPlayerRef.current && index !== null) {\n      previewPlayerRef.current.play().catch(e => console.log(\"Autoplay prevented:\", e));\n    }\n  };\n  const featuredCourses = [{\n    id: 1,\n    title: \"Advanced Network Enchantments\",\n    instructor: \"Professor Elara Waveweaver\",\n    progress: 65,\n    image: \"https://readdy.ai/api/search-image?query=Magical%2520network%2520operations%2520center%2520with%2520glowing%2520blue%2520and%2520purple%2520holographic%2520displays%2520showing%2520network%2520traffic%2520visualizations%252C%2520staffed%2520by%2520professionals%2520in%2520tech-inspired%2520robes%252C%2520with%2520floating%2520data%2520streams%2520and%2520ethereal%2520connections%2520between%2520server%2520racks%252C%2520high%2520quality%2520digital%2520art&width=400&height=225&seq=course1&orientation=landscape\",\n    isSeminar: false,\n    description: \"Master the art of network enchantment with advanced techniques for optimizing magical data flows.\"\n  }, {\n    id: 2,\n    title: \"Packet Manipulation Arts\",\n    instructor: \"Dr. Thorne Cablemancer\",\n    progress: 30,\n    image: \"https://readdy.ai/api/search-image?query=Ethereal%2520hands%2520manipulating%2520glowing%2520network%2520packets%2520floating%2520in%2520air%252C%2520with%2520streams%2520of%2520data%2520flowing%2520between%2520crystalline%2520nodes%252C%2520against%2520a%2520backdrop%2520of%2520magical%2520server%2520infrastructure%2520with%2520blue%2520and%2520purple%2520lighting%252C%2520high%2520quality%2520digital%2520art&width=400&height=225&seq=course2&orientation=landscape\",\n    isSeminar: false,\n    description: \"Learn to shape and direct network packets with precision and efficiency.\"\n  }, {\n    id: 3,\n    title: \"Network Defense Against Dark Arts\",\n    instructor: \"Master Orion Signalkeeper\",\n    progress: 0,\n    image: \"https://readdy.ai/api/search-image?query=Magical%2520shield%2520protecting%2520network%2520infrastructure%2520from%2520shadowy%2520attacks%252C%2520with%2520glowing%2520blue%2520defensive%2520runes%2520and%2520sigils%252C%2520ethereal%2520guardians%2520monitoring%2520for%2520intrusions%252C%2520dark%2520background%2520with%2520contrasting%2520magical%2520light%2520effects%252C%2520high%2520quality%2520digital%2520art&width=400&height=225&seq=course3&orientation=landscape\",\n    isSeminar: false,\n    description: \"Protect your networks from malicious attacks with advanced defensive techniques.\"\n  }, {\n    id: 4,\n    title: \"Live Seminar: Future of Magical Networks\",\n    instructor: \"Professor Iris Dataweave\",\n    date: \"May 10, 2025 • 2:00 PM\",\n    image: \"https://readdy.ai/api/search-image?query=Futuristic%2520magical%2520auditorium%2520with%2520a%2520presenter%2520on%2520stage%2520demonstrating%2520advanced%2520network%2520concepts%2520with%2520floating%2520holographic%2520displays%252C%2520audience%2520of%2520diverse%2520students%2520in%2520tech-magical%2520attire%252C%2520glowing%2520blue%2520and%2520purple%2520lighting%252C%2520high%2520quality%2520digital%2520art&width=400&height=225&seq=seminar1&orientation=landscape\",\n    isSeminar: true,\n    seats: \"43/100 seats available\",\n    description: \"Join us for a live discussion on emerging technologies and future trends in magical networking.\"\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gray-900 text-white font-sans\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: `fixed top-0 left-0 h-full w-80 bg-indigo-900 z-50 transform transition-transform duration-300 ease-in-out shadow-2xl ${sideMenuOpen ? 'translate-x-0' : '-translate-x-full'}`,\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex justify-between items-center mb-8\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-2xl font-bold text-white\",\n            children: \"Student Dashboard\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 77,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: toggleSideMenu,\n            className: \"text-white hover:text-blue-300 cursor-pointer\",\n            children: /*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fa-solid fa-times text-xl\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 79,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 78,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 76,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center mb-8 border-b border-indigo-700 pb-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"h-16 w-16 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center text-2xl font-bold\",\n            children: \"SW\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 83,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"ml-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-semibold\",\n              children: \"Student Wizard\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 87,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-blue-300\",\n              children: \"Network Operations\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 88,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 86,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 82,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"nav\", {\n          className: \"mb-8\",\n          children: /*#__PURE__*/_jsxDEV(\"ul\", {\n            className: \"space-y-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"li\", {\n              children: /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => setActiveTab('dashboard'),\n                className: `w-full text-left px-4 py-3 rounded-lg flex items-center ${activeTab === 'dashboard' ? 'bg-indigo-800 text-white' : 'text-blue-200 hover:bg-indigo-800'}`,\n                children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"fa-solid fa-gauge-high mr-3\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 98,\n                  columnNumber: 19\n                }, this), \"Dashboard\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 94,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 93,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => setActiveTab('courses'),\n                className: `w-full text-left px-4 py-3 rounded-lg flex items-center ${activeTab === 'courses' ? 'bg-indigo-800 text-white' : 'text-blue-200 hover:bg-indigo-800'}`,\n                children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"fa-solid fa-graduation-cap mr-3\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 107,\n                  columnNumber: 19\n                }, this), \"My Courses\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 103,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 102,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => setActiveTab('schedule'),\n                className: `w-full text-left px-4 py-3 rounded-lg flex items-center ${activeTab === 'schedule' ? 'bg-indigo-800 text-white' : 'text-blue-200 hover:bg-indigo-800'}`,\n                children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"fa-solid fa-calendar-alt mr-3\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 116,\n                  columnNumber: 19\n                }, this), \"Schedule\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 112,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 111,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 92,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 91,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"border-t border-indigo-700 pt-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            className: \"text-lg font-semibold mb-4\",\n            children: \"Course Progress\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 123,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            ref: progressChartRef,\n            className: \"h-48 w-full bg-gray-800 rounded-lg flex items-center justify-center\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-4xl font-bold text-blue-400 mb-2\",\n                children: \"65%\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 126,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-sm text-gray-400\",\n                children: \"Overall Progress\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 127,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 125,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 124,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mt-4 grid grid-cols-3 gap-2 text-center text-sm\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"font-semibold\",\n                children: \"Completed\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 132,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-blue-300\",\n                children: \"35%\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 133,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 131,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"font-semibold\",\n                children: \"In Progress\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 136,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-blue-300\",\n                children: \"20%\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 137,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 135,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"font-semibold\",\n                children: \"Not Started\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 140,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-blue-300\",\n                children: \"45%\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 141,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 139,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 130,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 122,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-8\",\n          children: /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/departments\",\n            className: \"block w-full px-4 py-3 bg-gradient-to-r from-purple-600 to-indigo-600 text-white rounded-lg text-center hover:from-purple-700 hover:to-indigo-700 transition-all duration-300 font-medium shadow-md\",\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fa-solid fa-arrow-left mr-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 150,\n              columnNumber: 15\n            }, this), \"Back to Department\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 146,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 145,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 75,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 74,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen relative\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute inset-0 z-0\",\n        children: /*#__PURE__*/_jsxDEV(\"img\", {\n          src: \"https://readdy.ai/api/search-image?query=Abstract%20digital%20network%20visualization%20with%20glowing%20blue%20and%20purple%20nodes%20connected%20by%20light%20streams%2C%20creating%20an%20intricate%20weaving%20pattern%20across%20a%20dark%20background%2C%20modern%20tech%20aesthetic%20with%20depth%20and%20dimension%2C%20professional%20enterprise%20grade%20network%20visualization&width=1920&height=1080&seq=bg1&orientation=landscape\",\n          alt: \"Network Background\",\n          className: \"w-full h-full object-cover opacity-10\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 160,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 159,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"relative z-10\",\n        children: [/*#__PURE__*/_jsxDEV(\"header\", {\n          className: \"bg-indigo-900/90 shadow-lg backdrop-blur-sm\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"container mx-auto px-6 py-4\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex justify-between items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: toggleSideMenu,\n                  className: \"mr-4 text-white hover:text-blue-300 cursor-pointer\",\n                  children: /*#__PURE__*/_jsxDEV(\"i\", {\n                    className: \"fa-solid fa-bars text-xl\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 173,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 172,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n                  className: \"text-2xl font-bold text-white\",\n                  children: \"Network Operations Classroom\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 175,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 171,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"relative\",\n                  children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    placeholder: \"Search courses...\",\n                    className: \"px-4 py-2 bg-indigo-800 bg-opacity-50 rounded-lg text-white placeholder-blue-300 border-none focus:outline-none focus:ring-2 focus:ring-blue-500 w-64 text-sm\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 179,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"i\", {\n                    className: \"fa-solid fa-search absolute right-3 top-1/2 transform -translate-y-1/2 text-blue-300\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 184,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 178,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"relative text-white hover:text-blue-300\",\n                  children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                    className: \"fa-solid fa-bell text-xl\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 187,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"absolute -top-1 -right-1 h-4 w-4 bg-red-500 rounded-full text-xs flex items-center justify-center\",\n                    children: \"2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 188,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 186,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"h-10 w-10 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center text-lg font-bold cursor-pointer\",\n                  children: \"SW\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 190,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 177,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 170,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 169,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 168,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"relative h-[70vh] overflow-hidden\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute inset-0 bg-gradient-to-b from-indigo-900/90 to-gray-900/90 backdrop-blur-sm\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 200,\n            columnNumber: 13\n          }, this), hoverCourse !== null && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute inset-0 z-0\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute inset-0 bg-gradient-to-r from-indigo-900 via-indigo-900/80 to-transparent z-10\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 203,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"video\", {\n              ref: previewPlayerRef,\n              className: \"w-full h-full object-cover opacity-50\",\n              loop: true,\n              muted: true,\n              playsInline: true,\n              children: /*#__PURE__*/_jsxDEV(\"source\", {\n                src: \"https://assets.mixkit.co/videos/preview/mixkit-digital-network-connection-over-blue-background-97.mp4\",\n                type: \"video/mp4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 211,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 204,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 202,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute inset-0 flex items-center z-10\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"container mx-auto px-6\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 gap-12 items-center\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: hoverCourse !== null ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"mb-2 text-blue-400 font-medium\",\n                      children: featuredCourses[hoverCourse].isSeminar ? 'LIVE SEMINAR' : 'FEATURED COURSE'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 221,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n                      className: \"text-4xl font-bold mb-4\",\n                      children: featuredCourses[hoverCourse].title\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 224,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-blue-200 mb-2\",\n                      children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                        className: \"fa-solid fa-user-tie mr-2\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 226,\n                        columnNumber: 27\n                      }, this), featuredCourses[hoverCourse].instructor]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 225,\n                      columnNumber: 25\n                    }, this), featuredCourses[hoverCourse].isSeminar ? /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-blue-200 mb-4\",\n                      children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                        className: \"fa-solid fa-calendar-day mr-2\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 231,\n                        columnNumber: 29\n                      }, this), featuredCourses[hoverCourse].date]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 230,\n                      columnNumber: 27\n                    }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"mb-4\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex items-center mb-1\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"text-sm text-blue-200 mr-2\",\n                          children: \"Progress:\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 237,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"text-sm text-blue-200\",\n                          children: [featuredCourses[hoverCourse].progress, \"%\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 238,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 236,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"w-full bg-gray-700 rounded-full h-2\",\n                        children: /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"bg-gradient-to-r from-blue-500 to-purple-600 h-2 rounded-full\",\n                          style: {\n                            width: `${featuredCourses[hoverCourse].progress}%`\n                          }\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 241,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 240,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 235,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-lg text-gray-300 mb-6\",\n                      children: featuredCourses[hoverCourse].description\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 248,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex flex-col sm:flex-row gap-4\",\n                      children: [/*#__PURE__*/_jsxDEV(Link, {\n                        to: \"/learning\",\n                        className: \"px-6 py-3 bg-gradient-to-r from-purple-600 to-blue-600 rounded-lg hover:from-purple-700 hover:to-blue-700 transition-all duration-300 font-medium shadow-lg text-center\",\n                        children: featuredCourses[hoverCourse].isSeminar ? 'Register for Seminar' : featuredCourses[hoverCourse].progress > 0 ? 'Continue Learning' : 'Start Course'\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 252,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                        className: \"px-6 py-3 bg-gray-800 rounded-lg hover:bg-gray-700 transition-all duration-300 font-medium\",\n                        children: \"View Details\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 259,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 251,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n                    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                      className: \"text-4xl font-bold mb-4\",\n                      children: \"Welcome to the Classroom\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 266,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-lg text-gray-300 mb-6\",\n                      children: \"Explore your courses, track your progress, and enhance your network operations skills. Hover over a course below to see more details.\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 267,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                      className: \"px-6 py-3 bg-gradient-to-r from-purple-600 to-blue-600 rounded-lg hover:from-purple-700 hover:to-blue-700 transition-all duration-300 font-medium shadow-lg\",\n                      children: \"Browse All Courses\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 270,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 218,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 217,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 216,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 215,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 199,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"container mx-auto px-6 py-12\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-16\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-2xl font-bold mb-6\",\n              children: \"Continue Learning\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 285,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n              children: featuredCourses.filter(course => !course.isSeminar).map((course, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-gray-800 rounded-xl overflow-hidden shadow-lg hover:shadow-blue-500/10 transition-all duration-300 cursor-pointer h-full\",\n                onMouseEnter: () => handleCourseHover(index),\n                onMouseLeave: () => handleCourseHover(null),\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"h-48 overflow-hidden\",\n                  children: /*#__PURE__*/_jsxDEV(\"img\", {\n                    src: course.image,\n                    alt: course.title,\n                    className: \"w-full h-full object-cover object-top transform hover:scale-105 transition-transform duration-300\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 295,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 294,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"p-6\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                    className: \"text-xl font-bold mb-2\",\n                    children: course.title\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 302,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-blue-300 text-sm mb-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                      className: \"fa-solid fa-user-tie mr-2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 304,\n                      columnNumber: 25\n                    }, this), course.instructor]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 303,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"mb-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex justify-between text-sm mb-1\",\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        children: \"Progress\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 309,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        children: [course.progress, \"%\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 310,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 308,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"w-full bg-gray-700 rounded-full h-2\",\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"bg-gradient-to-r from-blue-500 to-purple-600 h-2 rounded-full\",\n                        style: {\n                          width: `${course.progress}%`\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 313,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 312,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 307,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex justify-between items-center\",\n                    children: [/*#__PURE__*/_jsxDEV(Link, {\n                      to: \"/learning\",\n                      className: \"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-300 font-medium text-center\",\n                      children: course.progress > 0 ? 'Continue' : 'Start'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 320,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                      className: \"px-4 py-2 bg-gray-700 text-white rounded-lg hover:bg-gray-600 transition-colors duration-300 font-medium\",\n                      children: \"Details\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 326,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 319,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 301,\n                  columnNumber: 21\n                }, this)]\n              }, course.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 288,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 286,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 284,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-16\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-2xl font-bold mb-6\",\n              children: \"Live Seminars\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 338,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n              children: featuredCourses.filter(course => course.isSeminar).map((course, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-gradient-to-r from-purple-900 to-indigo-900 rounded-xl overflow-hidden shadow-lg hover:shadow-purple-500/20 transition-all duration-300 cursor-pointer\",\n                onMouseEnter: () => handleCourseHover(featuredCourses.indexOf(course)),\n                onMouseLeave: () => handleCourseHover(null),\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"h-48 overflow-hidden\",\n                  children: /*#__PURE__*/_jsxDEV(\"img\", {\n                    src: course.image,\n                    alt: course.title,\n                    className: \"w-full h-full object-cover object-top transform hover:scale-105 transition-transform duration-300\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 348,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 347,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"p-6\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center mb-2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"bg-red-500 text-white text-xs px-2 py-1 rounded-full mr-2\",\n                      children: \"LIVE\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 356,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-purple-300 text-sm\",\n                      children: course.date\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 357,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 355,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                    className: \"text-xl font-bold mb-2\",\n                    children: course.title\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 359,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-blue-300 text-sm mb-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                      className: \"fa-solid fa-user-tie mr-2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 361,\n                      columnNumber: 25\n                    }, this), course.instructor]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 360,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-gray-300 text-sm mb-4\",\n                    children: course.description\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 364,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex justify-between items-center\",\n                    children: [/*#__PURE__*/_jsxDEV(Link, {\n                      to: \"/learning\",\n                      className: \"px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors duration-300 font-medium text-center\",\n                      children: \"Register\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 366,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-purple-300 text-sm\",\n                      children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                        className: \"fa-solid fa-users mr-1\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 373,\n                        columnNumber: 27\n                      }, this), course.seats]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 372,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 365,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 354,\n                  columnNumber: 21\n                }, this)]\n              }, course.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 341,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 339,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 337,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 282,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 166,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 158,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 72,\n    columnNumber: 5\n  }, this);\n};\n_s(ClassroomPage, \"Wg9mCUzJpMw1zJyZ5yuZR3PSRxQ=\");\n_c = ClassroomPage;\nexport default ClassroomPage;\nvar _c;\n$RefreshReg$(_c, \"ClassroomPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "Link", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ClassroomPage", "user", "moodleData", "_s", "sideMenuOpen", "setSideMenuOpen", "activeTab", "setActiveTab", "hoverCourse", "setHoverCourse", "previewPlayerRef", "progressChartRef", "current", "toggleSideMenu", "handleCourseHover", "index", "play", "catch", "e", "console", "log", "featuredCourses", "id", "title", "instructor", "progress", "image", "isSeminar", "description", "date", "seats", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "ref", "to", "src", "alt", "type", "placeholder", "loop", "muted", "playsInline", "style", "width", "filter", "course", "map", "onMouseEnter", "onMouseLeave", "indexOf", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/FatbeamU/react-app/src/pages/ClassroomPage.js"], "sourcesContent": ["// Converted from ClassroomSplash-Page.tsx - The exported code uses Tailwind CSS\nimport React, { useState, useEffect, useRef } from 'react';\nimport { Link } from 'react-router-dom';\n\nconst ClassroomPage = ({ user, moodleData }) => {\n  const [sideMenuOpen, setSideMenuOpen] = useState(false);\n  const [activeTab, setActiveTab] = useState('dashboard');\n  const [hoverCourse, setHoverCourse] = useState(null);\n  const previewPlayerRef = useRef(null);\n  const progressChartRef = useRef(null);\n\n  useEffect(() => {\n    // Simplified chart implementation for now\n    if (progressChartRef.current) {\n      // Chart initialization would go here\n      // For now, we'll use a simple implementation\n    }\n  }, []);\n\n  const toggleSideMenu = () => {\n    setSideMenuOpen(!sideMenuOpen);\n  };\n\n  const handleCourseHover = (index) => {\n    setHoverCourse(index);\n    if (previewPlayerRef.current && index !== null) {\n      previewPlayerRef.current.play().catch(e => console.log(\"Autoplay prevented:\", e));\n    }\n  };\n\n  const featuredCourses = [\n    {\n      id: 1,\n      title: \"Advanced Network Enchantments\",\n      instructor: \"Professor Elara Waveweaver\",\n      progress: 65,\n      image: \"https://readdy.ai/api/search-image?query=Magical%2520network%2520operations%2520center%2520with%2520glowing%2520blue%2520and%2520purple%2520holographic%2520displays%2520showing%2520network%2520traffic%2520visualizations%252C%2520staffed%2520by%2520professionals%2520in%2520tech-inspired%2520robes%252C%2520with%2520floating%2520data%2520streams%2520and%2520ethereal%2520connections%2520between%2520server%2520racks%252C%2520high%2520quality%2520digital%2520art&width=400&height=225&seq=course1&orientation=landscape\",\n      isSeminar: false,\n      description: \"Master the art of network enchantment with advanced techniques for optimizing magical data flows.\"\n    },\n    {\n      id: 2,\n      title: \"Packet Manipulation Arts\",\n      instructor: \"Dr. Thorne Cablemancer\",\n      progress: 30,\n      image: \"https://readdy.ai/api/search-image?query=Ethereal%2520hands%2520manipulating%2520glowing%2520network%2520packets%2520floating%2520in%2520air%252C%2520with%2520streams%2520of%2520data%2520flowing%2520between%2520crystalline%2520nodes%252C%2520against%2520a%2520backdrop%2520of%2520magical%2520server%2520infrastructure%2520with%2520blue%2520and%2520purple%2520lighting%252C%2520high%2520quality%2520digital%2520art&width=400&height=225&seq=course2&orientation=landscape\",\n      isSeminar: false,\n      description: \"Learn to shape and direct network packets with precision and efficiency.\"\n    },\n    {\n      id: 3,\n      title: \"Network Defense Against Dark Arts\",\n      instructor: \"Master Orion Signalkeeper\",\n      progress: 0,\n      image: \"https://readdy.ai/api/search-image?query=Magical%2520shield%2520protecting%2520network%2520infrastructure%2520from%2520shadowy%2520attacks%252C%2520with%2520glowing%2520blue%2520defensive%2520runes%2520and%2520sigils%252C%2520ethereal%2520guardians%2520monitoring%2520for%2520intrusions%252C%2520dark%2520background%2520with%2520contrasting%2520magical%2520light%2520effects%252C%2520high%2520quality%2520digital%2520art&width=400&height=225&seq=course3&orientation=landscape\",\n      isSeminar: false,\n      description: \"Protect your networks from malicious attacks with advanced defensive techniques.\"\n    },\n    {\n      id: 4,\n      title: \"Live Seminar: Future of Magical Networks\",\n      instructor: \"Professor Iris Dataweave\",\n      date: \"May 10, 2025 • 2:00 PM\",\n      image: \"https://readdy.ai/api/search-image?query=Futuristic%2520magical%2520auditorium%2520with%2520a%2520presenter%2520on%2520stage%2520demonstrating%2520advanced%2520network%2520concepts%2520with%2520floating%2520holographic%2520displays%252C%2520audience%2520of%2520diverse%2520students%2520in%2520tech-magical%2520attire%252C%2520glowing%2520blue%2520and%2520purple%2520lighting%252C%2520high%2520quality%2520digital%2520art&width=400&height=225&seq=seminar1&orientation=landscape\",\n      isSeminar: true,\n      seats: \"43/100 seats available\",\n      description: \"Join us for a live discussion on emerging technologies and future trends in magical networking.\"\n    }\n  ];\n\n  return (\n    <div className=\"min-h-screen bg-gray-900 text-white font-sans\">\n      {/* Side Menu */}\n      <div className={`fixed top-0 left-0 h-full w-80 bg-indigo-900 z-50 transform transition-transform duration-300 ease-in-out shadow-2xl ${sideMenuOpen ? 'translate-x-0' : '-translate-x-full'}`}>\n        <div className=\"p-6\">\n          <div className=\"flex justify-between items-center mb-8\">\n            <h2 className=\"text-2xl font-bold text-white\">Student Dashboard</h2>\n            <button onClick={toggleSideMenu} className=\"text-white hover:text-blue-300 cursor-pointer\">\n              <i className=\"fa-solid fa-times text-xl\"></i>\n            </button>\n          </div>\n          <div className=\"flex items-center mb-8 border-b border-indigo-700 pb-6\">\n            <div className=\"h-16 w-16 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center text-2xl font-bold\">\n              SW\n            </div>\n            <div className=\"ml-4\">\n              <h3 className=\"text-lg font-semibold\">Student Wizard</h3>\n              <p className=\"text-blue-300\">Network Operations</p>\n            </div>\n          </div>\n          <nav className=\"mb-8\">\n            <ul className=\"space-y-2\">\n              <li>\n                <button\n                  onClick={() => setActiveTab('dashboard')}\n                  className={`w-full text-left px-4 py-3 rounded-lg flex items-center ${activeTab === 'dashboard' ? 'bg-indigo-800 text-white' : 'text-blue-200 hover:bg-indigo-800'}`}\n                >\n                  <i className=\"fa-solid fa-gauge-high mr-3\"></i>\n                  Dashboard\n                </button>\n              </li>\n              <li>\n                <button\n                  onClick={() => setActiveTab('courses')}\n                  className={`w-full text-left px-4 py-3 rounded-lg flex items-center ${activeTab === 'courses' ? 'bg-indigo-800 text-white' : 'text-blue-200 hover:bg-indigo-800'}`}\n                >\n                  <i className=\"fa-solid fa-graduation-cap mr-3\"></i>\n                  My Courses\n                </button>\n              </li>\n              <li>\n                <button\n                  onClick={() => setActiveTab('schedule')}\n                  className={`w-full text-left px-4 py-3 rounded-lg flex items-center ${activeTab === 'schedule' ? 'bg-indigo-800 text-white' : 'text-blue-200 hover:bg-indigo-800'}`}\n                >\n                  <i className=\"fa-solid fa-calendar-alt mr-3\"></i>\n                  Schedule\n                </button>\n              </li>\n            </ul>\n          </nav>\n          <div className=\"border-t border-indigo-700 pt-6\">\n            <h4 className=\"text-lg font-semibold mb-4\">Course Progress</h4>\n            <div ref={progressChartRef} className=\"h-48 w-full bg-gray-800 rounded-lg flex items-center justify-center\">\n              <div className=\"text-center\">\n                <div className=\"text-4xl font-bold text-blue-400 mb-2\">65%</div>\n                <div className=\"text-sm text-gray-400\">Overall Progress</div>\n              </div>\n            </div>\n            <div className=\"mt-4 grid grid-cols-3 gap-2 text-center text-sm\">\n              <div>\n                <div className=\"font-semibold\">Completed</div>\n                <div className=\"text-blue-300\">35%</div>\n              </div>\n              <div>\n                <div className=\"font-semibold\">In Progress</div>\n                <div className=\"text-blue-300\">20%</div>\n              </div>\n              <div>\n                <div className=\"font-semibold\">Not Started</div>\n                <div className=\"text-blue-300\">45%</div>\n              </div>\n            </div>\n          </div>\n          <div className=\"mt-8\">\n            <Link\n              to=\"/departments\"\n              className=\"block w-full px-4 py-3 bg-gradient-to-r from-purple-600 to-indigo-600 text-white rounded-lg text-center hover:from-purple-700 hover:to-indigo-700 transition-all duration-300 font-medium shadow-md\"\n            >\n              <i className=\"fa-solid fa-arrow-left mr-2\"></i>\n              Back to Department\n            </Link>\n          </div>\n        </div>\n      </div>\n\n      {/* Main Content */}\n      <div className=\"min-h-screen relative\">\n        <div className=\"absolute inset-0 z-0\">\n          <img\n            src=\"https://readdy.ai/api/search-image?query=Abstract%20digital%20network%20visualization%20with%20glowing%20blue%20and%20purple%20nodes%20connected%20by%20light%20streams%2C%20creating%20an%20intricate%20weaving%20pattern%20across%20a%20dark%20background%2C%20modern%20tech%20aesthetic%20with%20depth%20and%20dimension%2C%20professional%20enterprise%20grade%20network%20visualization&width=1920&height=1080&seq=bg1&orientation=landscape\"\n            alt=\"Network Background\"\n            className=\"w-full h-full object-cover opacity-10\"\n          />\n        </div>\n        <div className=\"relative z-10\">\n          {/* Header */}\n          <header className=\"bg-indigo-900/90 shadow-lg backdrop-blur-sm\">\n            <div className=\"container mx-auto px-6 py-4\">\n              <div className=\"flex justify-between items-center\">\n                <div className=\"flex items-center\">\n                  <button onClick={toggleSideMenu} className=\"mr-4 text-white hover:text-blue-300 cursor-pointer\">\n                    <i className=\"fa-solid fa-bars text-xl\"></i>\n                  </button>\n                  <h1 className=\"text-2xl font-bold text-white\">Network Operations Classroom</h1>\n                </div>\n                <div className=\"flex items-center space-x-4\">\n                  <div className=\"relative\">\n                    <input\n                      type=\"text\"\n                      placeholder=\"Search courses...\"\n                      className=\"px-4 py-2 bg-indigo-800 bg-opacity-50 rounded-lg text-white placeholder-blue-300 border-none focus:outline-none focus:ring-2 focus:ring-blue-500 w-64 text-sm\"\n                    />\n                    <i className=\"fa-solid fa-search absolute right-3 top-1/2 transform -translate-y-1/2 text-blue-300\"></i>\n                  </div>\n                  <button className=\"relative text-white hover:text-blue-300\">\n                    <i className=\"fa-solid fa-bell text-xl\"></i>\n                    <span className=\"absolute -top-1 -right-1 h-4 w-4 bg-red-500 rounded-full text-xs flex items-center justify-center\">2</span>\n                  </button>\n                  <div className=\"h-10 w-10 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center text-lg font-bold cursor-pointer\">\n                    SW\n                  </div>\n                </div>\n              </div>\n            </div>\n          </header>\n\n          {/* Featured Course Preview with Video */}\n          <div className=\"relative h-[70vh] overflow-hidden\">\n            <div className=\"absolute inset-0 bg-gradient-to-b from-indigo-900/90 to-gray-900/90 backdrop-blur-sm\"></div>\n            {hoverCourse !== null && (\n              <div className=\"absolute inset-0 z-0\">\n                <div className=\"absolute inset-0 bg-gradient-to-r from-indigo-900 via-indigo-900/80 to-transparent z-10\"></div>\n                <video\n                  ref={previewPlayerRef}\n                  className=\"w-full h-full object-cover opacity-50\"\n                  loop\n                  muted\n                  playsInline\n                >\n                  <source src=\"https://assets.mixkit.co/videos/preview/mixkit-digital-network-connection-over-blue-background-97.mp4\" type=\"video/mp4\" />\n                </video>\n              </div>\n            )}\n            <div className=\"absolute inset-0 flex items-center z-10\">\n              <div className=\"container mx-auto px-6\">\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-12 items-center\">\n                  <div>\n                    {hoverCourse !== null ? (\n                      <>\n                        <div className=\"mb-2 text-blue-400 font-medium\">\n                          {featuredCourses[hoverCourse].isSeminar ? 'LIVE SEMINAR' : 'FEATURED COURSE'}\n                        </div>\n                        <h2 className=\"text-4xl font-bold mb-4\">{featuredCourses[hoverCourse].title}</h2>\n                        <p className=\"text-blue-200 mb-2\">\n                          <i className=\"fa-solid fa-user-tie mr-2\"></i>\n                          {featuredCourses[hoverCourse].instructor}\n                        </p>\n                        {featuredCourses[hoverCourse].isSeminar ? (\n                          <p className=\"text-blue-200 mb-4\">\n                            <i className=\"fa-solid fa-calendar-day mr-2\"></i>\n                            {featuredCourses[hoverCourse].date}\n                          </p>\n                        ) : (\n                          <div className=\"mb-4\">\n                            <div className=\"flex items-center mb-1\">\n                              <div className=\"text-sm text-blue-200 mr-2\">Progress:</div>\n                              <div className=\"text-sm text-blue-200\">{featuredCourses[hoverCourse].progress}%</div>\n                            </div>\n                            <div className=\"w-full bg-gray-700 rounded-full h-2\">\n                              <div\n                                className=\"bg-gradient-to-r from-blue-500 to-purple-600 h-2 rounded-full\"\n                                style={{ width: `${featuredCourses[hoverCourse].progress}%` }}\n                              ></div>\n                            </div>\n                          </div>\n                        )}\n                        <p className=\"text-lg text-gray-300 mb-6\">\n                          {featuredCourses[hoverCourse].description}\n                        </p>\n                        <div className=\"flex flex-col sm:flex-row gap-4\">\n                          <Link\n                            to=\"/learning\"\n                            className=\"px-6 py-3 bg-gradient-to-r from-purple-600 to-blue-600 rounded-lg hover:from-purple-700 hover:to-blue-700 transition-all duration-300 font-medium shadow-lg text-center\"\n                          >\n                            {featuredCourses[hoverCourse].isSeminar ? 'Register for Seminar' :\n                             featuredCourses[hoverCourse].progress > 0 ? 'Continue Learning' : 'Start Course'}\n                          </Link>\n                          <button className=\"px-6 py-3 bg-gray-800 rounded-lg hover:bg-gray-700 transition-all duration-300 font-medium\">\n                            View Details\n                          </button>\n                        </div>\n                      </>\n                    ) : (\n                      <>\n                        <h2 className=\"text-4xl font-bold mb-4\">Welcome to the Classroom</h2>\n                        <p className=\"text-lg text-gray-300 mb-6\">\n                          Explore your courses, track your progress, and enhance your network operations skills. Hover over a course below to see more details.\n                        </p>\n                        <button className=\"px-6 py-3 bg-gradient-to-r from-purple-600 to-blue-600 rounded-lg hover:from-purple-700 hover:to-blue-700 transition-all duration-300 font-medium shadow-lg\">\n                          Browse All Courses\n                        </button>\n                      </>\n                    )}\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* Dynamic Course Categories */}\n          <div className=\"container mx-auto px-6 py-12\">\n            {/* Continue Learning Section */}\n            <div className=\"mb-16\">\n              <h2 className=\"text-2xl font-bold mb-6\">Continue Learning</h2>\n              <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n                {featuredCourses.filter(course => !course.isSeminar).map((course, index) => (\n                  <div\n                    key={course.id}\n                    className=\"bg-gray-800 rounded-xl overflow-hidden shadow-lg hover:shadow-blue-500/10 transition-all duration-300 cursor-pointer h-full\"\n                    onMouseEnter={() => handleCourseHover(index)}\n                    onMouseLeave={() => handleCourseHover(null)}\n                  >\n                    <div className=\"h-48 overflow-hidden\">\n                      <img\n                        src={course.image}\n                        alt={course.title}\n                        className=\"w-full h-full object-cover object-top transform hover:scale-105 transition-transform duration-300\"\n                      />\n                    </div>\n                    <div className=\"p-6\">\n                      <h3 className=\"text-xl font-bold mb-2\">{course.title}</h3>\n                      <p className=\"text-blue-300 text-sm mb-4\">\n                        <i className=\"fa-solid fa-user-tie mr-2\"></i>\n                        {course.instructor}\n                      </p>\n                      <div className=\"mb-4\">\n                        <div className=\"flex justify-between text-sm mb-1\">\n                          <span>Progress</span>\n                          <span>{course.progress}%</span>\n                        </div>\n                        <div className=\"w-full bg-gray-700 rounded-full h-2\">\n                          <div\n                            className=\"bg-gradient-to-r from-blue-500 to-purple-600 h-2 rounded-full\"\n                            style={{ width: `${course.progress}%` }}\n                          ></div>\n                        </div>\n                      </div>\n                      <div className=\"flex justify-between items-center\">\n                        <Link\n                          to=\"/learning\"\n                          className=\"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-300 font-medium text-center\"\n                        >\n                          {course.progress > 0 ? 'Continue' : 'Start'}\n                        </Link>\n                        <button className=\"px-4 py-2 bg-gray-700 text-white rounded-lg hover:bg-gray-600 transition-colors duration-300 font-medium\">\n                          Details\n                        </button>\n                      </div>\n                    </div>\n                  </div>\n                ))}\n              </div>\n            </div>\n\n            {/* Live Seminars Section */}\n            <div className=\"mb-16\">\n              <h2 className=\"text-2xl font-bold mb-6\">Live Seminars</h2>\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                {featuredCourses.filter(course => course.isSeminar).map((course, index) => (\n                  <div\n                    key={course.id}\n                    className=\"bg-gradient-to-r from-purple-900 to-indigo-900 rounded-xl overflow-hidden shadow-lg hover:shadow-purple-500/20 transition-all duration-300 cursor-pointer\"\n                    onMouseEnter={() => handleCourseHover(featuredCourses.indexOf(course))}\n                    onMouseLeave={() => handleCourseHover(null)}\n                  >\n                    <div className=\"h-48 overflow-hidden\">\n                      <img\n                        src={course.image}\n                        alt={course.title}\n                        className=\"w-full h-full object-cover object-top transform hover:scale-105 transition-transform duration-300\"\n                      />\n                    </div>\n                    <div className=\"p-6\">\n                      <div className=\"flex items-center mb-2\">\n                        <span className=\"bg-red-500 text-white text-xs px-2 py-1 rounded-full mr-2\">LIVE</span>\n                        <span className=\"text-purple-300 text-sm\">{course.date}</span>\n                      </div>\n                      <h3 className=\"text-xl font-bold mb-2\">{course.title}</h3>\n                      <p className=\"text-blue-300 text-sm mb-4\">\n                        <i className=\"fa-solid fa-user-tie mr-2\"></i>\n                        {course.instructor}\n                      </p>\n                      <p className=\"text-gray-300 text-sm mb-4\">{course.description}</p>\n                      <div className=\"flex justify-between items-center\">\n                        <Link\n                          to=\"/learning\"\n                          className=\"px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors duration-300 font-medium text-center\"\n                        >\n                          Register\n                        </Link>\n                        <span className=\"text-purple-300 text-sm\">\n                          <i className=\"fa-solid fa-users mr-1\"></i>\n                          {course.seats}\n                        </span>\n                      </div>\n                    </div>\n                  </div>\n                ))}\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default ClassroomPage;\n"], "mappings": ";;AAAA;AACA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SAASC,IAAI,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAExC,MAAMC,aAAa,GAAGA,CAAC;EAAEC,IAAI;EAAEC;AAAW,CAAC,KAAK;EAAAC,EAAA;EAC9C,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGb,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACc,SAAS,EAAEC,YAAY,CAAC,GAAGf,QAAQ,CAAC,WAAW,CAAC;EACvD,MAAM,CAACgB,WAAW,EAAEC,cAAc,CAAC,GAAGjB,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAMkB,gBAAgB,GAAGhB,MAAM,CAAC,IAAI,CAAC;EACrC,MAAMiB,gBAAgB,GAAGjB,MAAM,CAAC,IAAI,CAAC;EAErCD,SAAS,CAAC,MAAM;IACd;IACA,IAAIkB,gBAAgB,CAACC,OAAO,EAAE;MAC5B;MACA;IAAA;EAEJ,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMC,cAAc,GAAGA,CAAA,KAAM;IAC3BR,eAAe,CAAC,CAACD,YAAY,CAAC;EAChC,CAAC;EAED,MAAMU,iBAAiB,GAAIC,KAAK,IAAK;IACnCN,cAAc,CAACM,KAAK,CAAC;IACrB,IAAIL,gBAAgB,CAACE,OAAO,IAAIG,KAAK,KAAK,IAAI,EAAE;MAC9CL,gBAAgB,CAACE,OAAO,CAACI,IAAI,CAAC,CAAC,CAACC,KAAK,CAACC,CAAC,IAAIC,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEF,CAAC,CAAC,CAAC;IACnF;EACF,CAAC;EAED,MAAMG,eAAe,GAAG,CACtB;IACEC,EAAE,EAAE,CAAC;IACLC,KAAK,EAAE,+BAA+B;IACtCC,UAAU,EAAE,4BAA4B;IACxCC,QAAQ,EAAE,EAAE;IACZC,KAAK,EAAE,qgBAAqgB;IAC5gBC,SAAS,EAAE,KAAK;IAChBC,WAAW,EAAE;EACf,CAAC,EACD;IACEN,EAAE,EAAE,CAAC;IACLC,KAAK,EAAE,0BAA0B;IACjCC,UAAU,EAAE,wBAAwB;IACpCC,QAAQ,EAAE,EAAE;IACZC,KAAK,EAAE,sdAAsd;IAC7dC,SAAS,EAAE,KAAK;IAChBC,WAAW,EAAE;EACf,CAAC,EACD;IACEN,EAAE,EAAE,CAAC;IACLC,KAAK,EAAE,mCAAmC;IAC1CC,UAAU,EAAE,2BAA2B;IACvCC,QAAQ,EAAE,CAAC;IACXC,KAAK,EAAE,6dAA6d;IACpeC,SAAS,EAAE,KAAK;IAChBC,WAAW,EAAE;EACf,CAAC,EACD;IACEN,EAAE,EAAE,CAAC;IACLC,KAAK,EAAE,0CAA0C;IACjDC,UAAU,EAAE,0BAA0B;IACtCK,IAAI,EAAE,wBAAwB;IAC9BH,KAAK,EAAE,8dAA8d;IACreC,SAAS,EAAE,IAAI;IACfG,KAAK,EAAE,wBAAwB;IAC/BF,WAAW,EAAE;EACf,CAAC,CACF;EAED,oBACE/B,OAAA;IAAKkC,SAAS,EAAC,+CAA+C;IAAAC,QAAA,gBAE5DnC,OAAA;MAAKkC,SAAS,EAAE,wHAAwH3B,YAAY,GAAG,eAAe,GAAG,mBAAmB,EAAG;MAAA4B,QAAA,eAC7LnC,OAAA;QAAKkC,SAAS,EAAC,KAAK;QAAAC,QAAA,gBAClBnC,OAAA;UAAKkC,SAAS,EAAC,wCAAwC;UAAAC,QAAA,gBACrDnC,OAAA;YAAIkC,SAAS,EAAC,+BAA+B;YAAAC,QAAA,EAAC;UAAiB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACpEvC,OAAA;YAAQwC,OAAO,EAAExB,cAAe;YAACkB,SAAS,EAAC,+CAA+C;YAAAC,QAAA,eACxFnC,OAAA;cAAGkC,SAAS,EAAC;YAA2B;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eACNvC,OAAA;UAAKkC,SAAS,EAAC,wDAAwD;UAAAC,QAAA,gBACrEnC,OAAA;YAAKkC,SAAS,EAAC,yHAAyH;YAAAC,QAAA,EAAC;UAEzI;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACNvC,OAAA;YAAKkC,SAAS,EAAC,MAAM;YAAAC,QAAA,gBACnBnC,OAAA;cAAIkC,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACzDvC,OAAA;cAAGkC,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAkB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNvC,OAAA;UAAKkC,SAAS,EAAC,MAAM;UAAAC,QAAA,eACnBnC,OAAA;YAAIkC,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACvBnC,OAAA;cAAAmC,QAAA,eACEnC,OAAA;gBACEwC,OAAO,EAAEA,CAAA,KAAM9B,YAAY,CAAC,WAAW,CAAE;gBACzCwB,SAAS,EAAE,2DAA2DzB,SAAS,KAAK,WAAW,GAAG,0BAA0B,GAAG,mCAAmC,EAAG;gBAAA0B,QAAA,gBAErKnC,OAAA;kBAAGkC,SAAS,EAAC;gBAA6B;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,aAEjD;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACP,CAAC,eACLvC,OAAA;cAAAmC,QAAA,eACEnC,OAAA;gBACEwC,OAAO,EAAEA,CAAA,KAAM9B,YAAY,CAAC,SAAS,CAAE;gBACvCwB,SAAS,EAAE,2DAA2DzB,SAAS,KAAK,SAAS,GAAG,0BAA0B,GAAG,mCAAmC,EAAG;gBAAA0B,QAAA,gBAEnKnC,OAAA;kBAAGkC,SAAS,EAAC;gBAAiC;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,cAErD;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACP,CAAC,eACLvC,OAAA;cAAAmC,QAAA,eACEnC,OAAA;gBACEwC,OAAO,EAAEA,CAAA,KAAM9B,YAAY,CAAC,UAAU,CAAE;gBACxCwB,SAAS,EAAE,2DAA2DzB,SAAS,KAAK,UAAU,GAAG,0BAA0B,GAAG,mCAAmC,EAAG;gBAAA0B,QAAA,gBAEpKnC,OAAA;kBAAGkC,SAAS,EAAC;gBAA+B;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,YAEnD;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACP,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACNvC,OAAA;UAAKkC,SAAS,EAAC,iCAAiC;UAAAC,QAAA,gBAC9CnC,OAAA;YAAIkC,SAAS,EAAC,4BAA4B;YAAAC,QAAA,EAAC;UAAe;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC/DvC,OAAA;YAAKyC,GAAG,EAAE3B,gBAAiB;YAACoB,SAAS,EAAC,qEAAqE;YAAAC,QAAA,eACzGnC,OAAA;cAAKkC,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1BnC,OAAA;gBAAKkC,SAAS,EAAC,uCAAuC;gBAAAC,QAAA,EAAC;cAAG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAChEvC,OAAA;gBAAKkC,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAAgB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1D;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNvC,OAAA;YAAKkC,SAAS,EAAC,iDAAiD;YAAAC,QAAA,gBAC9DnC,OAAA;cAAAmC,QAAA,gBACEnC,OAAA;gBAAKkC,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC9CvC,OAAA;gBAAKkC,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAAC;cAAG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrC,CAAC,eACNvC,OAAA;cAAAmC,QAAA,gBACEnC,OAAA;gBAAKkC,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAAC;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAChDvC,OAAA;gBAAKkC,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAAC;cAAG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrC,CAAC,eACNvC,OAAA;cAAAmC,QAAA,gBACEnC,OAAA;gBAAKkC,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAAC;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAChDvC,OAAA;gBAAKkC,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAAC;cAAG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNvC,OAAA;UAAKkC,SAAS,EAAC,MAAM;UAAAC,QAAA,eACnBnC,OAAA,CAACF,IAAI;YACH4C,EAAE,EAAC,cAAc;YACjBR,SAAS,EAAC,qMAAqM;YAAAC,QAAA,gBAE/MnC,OAAA;cAAGkC,SAAS,EAAC;YAA6B;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,sBAEjD;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNvC,OAAA;MAAKkC,SAAS,EAAC,uBAAuB;MAAAC,QAAA,gBACpCnC,OAAA;QAAKkC,SAAS,EAAC,sBAAsB;QAAAC,QAAA,eACnCnC,OAAA;UACE2C,GAAG,EAAC,mbAAmb;UACvbC,GAAG,EAAC,oBAAoB;UACxBV,SAAS,EAAC;QAAuC;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eACNvC,OAAA;QAAKkC,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAE5BnC,OAAA;UAAQkC,SAAS,EAAC,6CAA6C;UAAAC,QAAA,eAC7DnC,OAAA;YAAKkC,SAAS,EAAC,6BAA6B;YAAAC,QAAA,eAC1CnC,OAAA;cAAKkC,SAAS,EAAC,mCAAmC;cAAAC,QAAA,gBAChDnC,OAAA;gBAAKkC,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,gBAChCnC,OAAA;kBAAQwC,OAAO,EAAExB,cAAe;kBAACkB,SAAS,EAAC,oDAAoD;kBAAAC,QAAA,eAC7FnC,OAAA;oBAAGkC,SAAS,EAAC;kBAA0B;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtC,CAAC,eACTvC,OAAA;kBAAIkC,SAAS,EAAC,+BAA+B;kBAAAC,QAAA,EAAC;gBAA4B;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5E,CAAC,eACNvC,OAAA;gBAAKkC,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,gBAC1CnC,OAAA;kBAAKkC,SAAS,EAAC,UAAU;kBAAAC,QAAA,gBACvBnC,OAAA;oBACE6C,IAAI,EAAC,MAAM;oBACXC,WAAW,EAAC,mBAAmB;oBAC/BZ,SAAS,EAAC;kBAA+J;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1K,CAAC,eACFvC,OAAA;oBAAGkC,SAAS,EAAC;kBAAsF;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrG,CAAC,eACNvC,OAAA;kBAAQkC,SAAS,EAAC,yCAAyC;kBAAAC,QAAA,gBACzDnC,OAAA;oBAAGkC,SAAS,EAAC;kBAA0B;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC5CvC,OAAA;oBAAMkC,SAAS,EAAC,mGAAmG;oBAAAC,QAAA,EAAC;kBAAC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtH,CAAC,eACTvC,OAAA;kBAAKkC,SAAS,EAAC,uIAAuI;kBAAAC,QAAA,EAAC;gBAEvJ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eAGTvC,OAAA;UAAKkC,SAAS,EAAC,mCAAmC;UAAAC,QAAA,gBAChDnC,OAAA;YAAKkC,SAAS,EAAC;UAAsF;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,EAC3G5B,WAAW,KAAK,IAAI,iBACnBX,OAAA;YAAKkC,SAAS,EAAC,sBAAsB;YAAAC,QAAA,gBACnCnC,OAAA;cAAKkC,SAAS,EAAC;YAAyF;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC/GvC,OAAA;cACEyC,GAAG,EAAE5B,gBAAiB;cACtBqB,SAAS,EAAC,uCAAuC;cACjDa,IAAI;cACJC,KAAK;cACLC,WAAW;cAAAd,QAAA,eAEXnC,OAAA;gBAAQ2C,GAAG,EAAC,uGAAuG;gBAACE,IAAI,EAAC;cAAW;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CACN,eACDvC,OAAA;YAAKkC,SAAS,EAAC,yCAAyC;YAAAC,QAAA,eACtDnC,OAAA;cAAKkC,SAAS,EAAC,wBAAwB;cAAAC,QAAA,eACrCnC,OAAA;gBAAKkC,SAAS,EAAC,qDAAqD;gBAAAC,QAAA,eAClEnC,OAAA;kBAAAmC,QAAA,EACGxB,WAAW,KAAK,IAAI,gBACnBX,OAAA,CAAAE,SAAA;oBAAAiC,QAAA,gBACEnC,OAAA;sBAAKkC,SAAS,EAAC,gCAAgC;sBAAAC,QAAA,EAC5CX,eAAe,CAACb,WAAW,CAAC,CAACmB,SAAS,GAAG,cAAc,GAAG;oBAAiB;sBAAAM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACzE,CAAC,eACNvC,OAAA;sBAAIkC,SAAS,EAAC,yBAAyB;sBAAAC,QAAA,EAAEX,eAAe,CAACb,WAAW,CAAC,CAACe;oBAAK;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACjFvC,OAAA;sBAAGkC,SAAS,EAAC,oBAAoB;sBAAAC,QAAA,gBAC/BnC,OAAA;wBAAGkC,SAAS,EAAC;sBAA2B;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,EAC5Cf,eAAe,CAACb,WAAW,CAAC,CAACgB,UAAU;oBAAA;sBAAAS,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACvC,CAAC,EACHf,eAAe,CAACb,WAAW,CAAC,CAACmB,SAAS,gBACrC9B,OAAA;sBAAGkC,SAAS,EAAC,oBAAoB;sBAAAC,QAAA,gBAC/BnC,OAAA;wBAAGkC,SAAS,EAAC;sBAA+B;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,EAChDf,eAAe,CAACb,WAAW,CAAC,CAACqB,IAAI;oBAAA;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjC,CAAC,gBAEJvC,OAAA;sBAAKkC,SAAS,EAAC,MAAM;sBAAAC,QAAA,gBACnBnC,OAAA;wBAAKkC,SAAS,EAAC,wBAAwB;wBAAAC,QAAA,gBACrCnC,OAAA;0BAAKkC,SAAS,EAAC,4BAA4B;0BAAAC,QAAA,EAAC;wBAAS;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC,eAC3DvC,OAAA;0BAAKkC,SAAS,EAAC,uBAAuB;0BAAAC,QAAA,GAAEX,eAAe,CAACb,WAAW,CAAC,CAACiB,QAAQ,EAAC,GAAC;wBAAA;0BAAAQ,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAClF,CAAC,eACNvC,OAAA;wBAAKkC,SAAS,EAAC,qCAAqC;wBAAAC,QAAA,eAClDnC,OAAA;0BACEkC,SAAS,EAAC,+DAA+D;0BACzEgB,KAAK,EAAE;4BAAEC,KAAK,EAAE,GAAG3B,eAAe,CAACb,WAAW,CAAC,CAACiB,QAAQ;0BAAI;wBAAE;0BAAAQ,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC1D;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACJ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CACN,eACDvC,OAAA;sBAAGkC,SAAS,EAAC,4BAA4B;sBAAAC,QAAA,EACtCX,eAAe,CAACb,WAAW,CAAC,CAACoB;oBAAW;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxC,CAAC,eACJvC,OAAA;sBAAKkC,SAAS,EAAC,iCAAiC;sBAAAC,QAAA,gBAC9CnC,OAAA,CAACF,IAAI;wBACH4C,EAAE,EAAC,WAAW;wBACdR,SAAS,EAAC,yKAAyK;wBAAAC,QAAA,EAElLX,eAAe,CAACb,WAAW,CAAC,CAACmB,SAAS,GAAG,sBAAsB,GAC/DN,eAAe,CAACb,WAAW,CAAC,CAACiB,QAAQ,GAAG,CAAC,GAAG,mBAAmB,GAAG;sBAAc;wBAAAQ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC7E,CAAC,eACPvC,OAAA;wBAAQkC,SAAS,EAAC,4FAA4F;wBAAAC,QAAA,EAAC;sBAE/G;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACN,CAAC;kBAAA,eACN,CAAC,gBAEHvC,OAAA,CAAAE,SAAA;oBAAAiC,QAAA,gBACEnC,OAAA;sBAAIkC,SAAS,EAAC,yBAAyB;sBAAAC,QAAA,EAAC;oBAAwB;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACrEvC,OAAA;sBAAGkC,SAAS,EAAC,4BAA4B;sBAAAC,QAAA,EAAC;oBAE1C;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC,eACJvC,OAAA;sBAAQkC,SAAS,EAAC,6JAA6J;sBAAAC,QAAA,EAAC;oBAEhL;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA,eACT;gBACH;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNvC,OAAA;UAAKkC,SAAS,EAAC,8BAA8B;UAAAC,QAAA,gBAE3CnC,OAAA;YAAKkC,SAAS,EAAC,OAAO;YAAAC,QAAA,gBACpBnC,OAAA;cAAIkC,SAAS,EAAC,yBAAyB;cAAAC,QAAA,EAAC;YAAiB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC9DvC,OAAA;cAAKkC,SAAS,EAAC,sDAAsD;cAAAC,QAAA,EAClEX,eAAe,CAAC4B,MAAM,CAACC,MAAM,IAAI,CAACA,MAAM,CAACvB,SAAS,CAAC,CAACwB,GAAG,CAAC,CAACD,MAAM,EAAEnC,KAAK,kBACrElB,OAAA;gBAEEkC,SAAS,EAAC,6HAA6H;gBACvIqB,YAAY,EAAEA,CAAA,KAAMtC,iBAAiB,CAACC,KAAK,CAAE;gBAC7CsC,YAAY,EAAEA,CAAA,KAAMvC,iBAAiB,CAAC,IAAI,CAAE;gBAAAkB,QAAA,gBAE5CnC,OAAA;kBAAKkC,SAAS,EAAC,sBAAsB;kBAAAC,QAAA,eACnCnC,OAAA;oBACE2C,GAAG,EAAEU,MAAM,CAACxB,KAAM;oBAClBe,GAAG,EAAES,MAAM,CAAC3B,KAAM;oBAClBQ,SAAS,EAAC;kBAAmG;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9G;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACNvC,OAAA;kBAAKkC,SAAS,EAAC,KAAK;kBAAAC,QAAA,gBAClBnC,OAAA;oBAAIkC,SAAS,EAAC,wBAAwB;oBAAAC,QAAA,EAAEkB,MAAM,CAAC3B;kBAAK;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC1DvC,OAAA;oBAAGkC,SAAS,EAAC,4BAA4B;oBAAAC,QAAA,gBACvCnC,OAAA;sBAAGkC,SAAS,EAAC;oBAA2B;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,EAC5Cc,MAAM,CAAC1B,UAAU;kBAAA;oBAAAS,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjB,CAAC,eACJvC,OAAA;oBAAKkC,SAAS,EAAC,MAAM;oBAAAC,QAAA,gBACnBnC,OAAA;sBAAKkC,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,gBAChDnC,OAAA;wBAAAmC,QAAA,EAAM;sBAAQ;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eACrBvC,OAAA;wBAAAmC,QAAA,GAAOkB,MAAM,CAACzB,QAAQ,EAAC,GAAC;sBAAA;wBAAAQ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC5B,CAAC,eACNvC,OAAA;sBAAKkC,SAAS,EAAC,qCAAqC;sBAAAC,QAAA,eAClDnC,OAAA;wBACEkC,SAAS,EAAC,+DAA+D;wBACzEgB,KAAK,EAAE;0BAAEC,KAAK,EAAE,GAAGE,MAAM,CAACzB,QAAQ;wBAAI;sBAAE;wBAAAQ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACpC;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACNvC,OAAA;oBAAKkC,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,gBAChDnC,OAAA,CAACF,IAAI;sBACH4C,EAAE,EAAC,WAAW;sBACdR,SAAS,EAAC,sHAAsH;sBAAAC,QAAA,EAE/HkB,MAAM,CAACzB,QAAQ,GAAG,CAAC,GAAG,UAAU,GAAG;oBAAO;sBAAAQ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACvC,CAAC,eACPvC,OAAA;sBAAQkC,SAAS,EAAC,0GAA0G;sBAAAC,QAAA,EAAC;oBAE7H;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA,GAzCDc,MAAM,CAAC5B,EAAE;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OA0CX,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNvC,OAAA;YAAKkC,SAAS,EAAC,OAAO;YAAAC,QAAA,gBACpBnC,OAAA;cAAIkC,SAAS,EAAC,yBAAyB;cAAAC,QAAA,EAAC;YAAa;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC1DvC,OAAA;cAAKkC,SAAS,EAAC,uCAAuC;cAAAC,QAAA,EACnDX,eAAe,CAAC4B,MAAM,CAACC,MAAM,IAAIA,MAAM,CAACvB,SAAS,CAAC,CAACwB,GAAG,CAAC,CAACD,MAAM,EAAEnC,KAAK,kBACpElB,OAAA;gBAEEkC,SAAS,EAAC,2JAA2J;gBACrKqB,YAAY,EAAEA,CAAA,KAAMtC,iBAAiB,CAACO,eAAe,CAACiC,OAAO,CAACJ,MAAM,CAAC,CAAE;gBACvEG,YAAY,EAAEA,CAAA,KAAMvC,iBAAiB,CAAC,IAAI,CAAE;gBAAAkB,QAAA,gBAE5CnC,OAAA;kBAAKkC,SAAS,EAAC,sBAAsB;kBAAAC,QAAA,eACnCnC,OAAA;oBACE2C,GAAG,EAAEU,MAAM,CAACxB,KAAM;oBAClBe,GAAG,EAAES,MAAM,CAAC3B,KAAM;oBAClBQ,SAAS,EAAC;kBAAmG;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9G;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACNvC,OAAA;kBAAKkC,SAAS,EAAC,KAAK;kBAAAC,QAAA,gBAClBnC,OAAA;oBAAKkC,SAAS,EAAC,wBAAwB;oBAAAC,QAAA,gBACrCnC,OAAA;sBAAMkC,SAAS,EAAC,2DAA2D;sBAAAC,QAAA,EAAC;oBAAI;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACvFvC,OAAA;sBAAMkC,SAAS,EAAC,yBAAyB;sBAAAC,QAAA,EAAEkB,MAAM,CAACrB;oBAAI;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3D,CAAC,eACNvC,OAAA;oBAAIkC,SAAS,EAAC,wBAAwB;oBAAAC,QAAA,EAAEkB,MAAM,CAAC3B;kBAAK;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC1DvC,OAAA;oBAAGkC,SAAS,EAAC,4BAA4B;oBAAAC,QAAA,gBACvCnC,OAAA;sBAAGkC,SAAS,EAAC;oBAA2B;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,EAC5Cc,MAAM,CAAC1B,UAAU;kBAAA;oBAAAS,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjB,CAAC,eACJvC,OAAA;oBAAGkC,SAAS,EAAC,4BAA4B;oBAAAC,QAAA,EAAEkB,MAAM,CAACtB;kBAAW;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAClEvC,OAAA;oBAAKkC,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,gBAChDnC,OAAA,CAACF,IAAI;sBACH4C,EAAE,EAAC,WAAW;sBACdR,SAAS,EAAC,0HAA0H;sBAAAC,QAAA,EACrI;oBAED;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACPvC,OAAA;sBAAMkC,SAAS,EAAC,yBAAyB;sBAAAC,QAAA,gBACvCnC,OAAA;wBAAGkC,SAAS,EAAC;sBAAwB;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,EACzCc,MAAM,CAACpB,KAAK;oBAAA;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACT,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA,GAnCDc,MAAM,CAAC5B,EAAE;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAoCX,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACjC,EAAA,CA9XIH,aAAa;AAAAuD,EAAA,GAAbvD,aAAa;AAgYnB,eAAeA,aAAa;AAAC,IAAAuD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}