{"ast": null, "code": "import React,{useState,useEffect}from'react';import{Link,useLocation}from'react-router-dom';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const Navigation=_ref=>{let{user}=_ref;const location=useLocation();const[isScrolled,setIsScrolled]=useState(false);const[isMenuOpen,setIsMenuOpen]=useState(false);const isActive=path=>location.pathname.startsWith(path);useEffect(()=>{const handleScroll=()=>{if(window.scrollY>50){setIsScrolled(true);}else{setIsScrolled(false);}};window.addEventListener('scroll',handleScroll);return()=>window.removeEventListener('scroll',handleScroll);},[]);return/*#__PURE__*/_jsxs(\"nav\",{className:\"fixed top-0 left-0 right-0 z-50 transition-all duration-300 \".concat(isScrolled?'bg-white shadow-md py-2':'bg-transparent py-4'),children:[/*#__PURE__*/_jsxs(\"div\",{className:\"container mx-auto px-6 flex justify-between items-center\",children:[/*#__PURE__*/_jsxs(Link,{to:\"/\",className:\"flex items-center\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"relative w-16 h-16 overflow-hidden\",children:[/*#__PURE__*/_jsx(\"img\",{src:\"https://www.fatbeamfiber.com/hubfs/site-files/logo-fatbeam-fiber.svg\",alt:\"Fatbeam Fiber\",className:\"w-full h-full object-contain brightness-200\"}),/*#__PURE__*/_jsx(\"div\",{className:\"absolute inset-0 bg-[#15a7dd] opacity-20 animate-pulse rounded-full\"})]}),/*#__PURE__*/_jsxs(\"h1\",{className:\"ml-3 text-2xl font-serif font-bold transition-colors duration-300 drop-shadow-[0_2px_2px_rgba(0,0,0,0.3)] \".concat(isScrolled?'text-[#15a7dd]':'text-white'),children:[/*#__PURE__*/_jsx(\"span\",{className:\"transition-colors duration-300 \".concat(isScrolled?'text-[#15a7dd]':'text-white'),children:\"Fatbeam\"}),\" Fiber University\"]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"hidden md:flex space-x-8\",children:[/*#__PURE__*/_jsx(Link,{to:\"/\",className:\"text-lg font-medium rounded-button whitespace-nowrap cursor-pointer transition-all duration-300 drop-shadow-[0_2px_2px_rgba(0,0,0,0.3)]\\n            \".concat(isScrolled?isActive('/')&&location.pathname==='/'?'text-[#15a7dd] border-b-2 border-[#15a7dd]':'text-[#15a7dd] hover:text-[#1397c7]':isActive('/')&&location.pathname==='/'?'text-white border-b-2 border-white':'text-white hover:text-gray-200'),children:\"Home\"}),/*#__PURE__*/_jsx(Link,{to:\"/departments\",className:\"text-lg font-medium rounded-button whitespace-nowrap cursor-pointer transition-all duration-300 drop-shadow-[0_2px_2px_rgba(0,0,0,0.3)]\\n            \".concat(isScrolled?isActive('/departments')?'text-[#15a7dd] border-b-2 border-[#15a7dd]':'text-[#15a7dd] hover:text-[#1397c7]':isActive('/departments')?'text-white border-b-2 border-white':'text-white hover:text-gray-200'),children:\"Departments\"}),/*#__PURE__*/_jsx(Link,{to:\"/library\",className:\"text-lg font-medium rounded-button whitespace-nowrap cursor-pointer transition-all duration-300 drop-shadow-[0_2px_2px_rgba(0,0,0,0.3)]\\n            \".concat(isScrolled?isActive('/library')?'text-[#15a7dd] border-b-2 border-[#15a7dd]':'text-[#15a7dd] hover:text-[#1397c7]':isActive('/library')?'text-white border-b-2 border-white':'text-white hover:text-gray-200'),children:\"Library\"}),/*#__PURE__*/_jsx(\"a\",{href:\"/learning\",className:\"text-lg font-medium rounded-button whitespace-nowrap cursor-pointer transition-all duration-300 drop-shadow-[0_2px_2px_rgba(0,0,0,0.3)]\\n            \".concat(isScrolled?'text-[#15a7dd] hover:text-[#1397c7]':'text-white hover:text-gray-200'),children:\"Learning\"}),/*#__PURE__*/_jsx(Link,{to:\"/classroom\",className:\"text-lg font-medium rounded-button whitespace-nowrap cursor-pointer transition-all duration-300 drop-shadow-[0_2px_2px_rgba(0,0,0,0.3)]\\n            \".concat(isScrolled?isActive('/classroom')?'text-[#15a7dd] border-b-2 border-[#15a7dd]':'text-[#15a7dd] hover:text-[#1397c7]':isActive('/classroom')?'text-white border-b-2 border-white':'text-white hover:text-gray-200'),children:\"Classroom\"}),user&&/*#__PURE__*/_jsx(Link,{to:\"/dashboard\",className:\"text-lg font-medium rounded-button whitespace-nowrap cursor-pointer transition-all duration-300 drop-shadow-[0_2px_2px_rgba(0,0,0,0.3)]\\n              \".concat(isScrolled?isActive('/dashboard')?'text-[#15a7dd] border-b-2 border-[#15a7dd]':'text-[#15a7dd] hover:text-[#1397c7]':isActive('/dashboard')?'text-white border-b-2 border-white':'text-white hover:text-gray-200'),children:\"Dashboard\"})]}),/*#__PURE__*/_jsx(\"div\",{children:user?/*#__PURE__*/_jsxs(\"span\",{className:\"transition-colors duration-300 \".concat(isScrolled?'text-gray-600':'text-white'),children:[\"Welcome, \",user.firstname]}):/*#__PURE__*/_jsx(\"a\",{href:\"/learning\",className:\"transition-colors duration-300 \".concat(isScrolled?'text-blue-600 hover:text-blue-800':'text-white hover:text-gray-200'),children:\"Login\"})}),/*#__PURE__*/_jsx(\"div\",{className:\"md:hidden\",children:/*#__PURE__*/_jsx(\"button\",{onClick:()=>setIsMenuOpen(!isMenuOpen),className:\"focus:outline-none rounded-button whitespace-nowrap cursor-pointer transition-colors duration-300 \".concat(isScrolled?'text-[#15a7dd]':'text-white',\" drop-shadow-[0_2px_2px_rgba(0,0,0,0.3)]\"),children:/*#__PURE__*/_jsx(\"i\",{className:\"fas \".concat(isMenuOpen?'fa-times':'fa-bars',\" text-2xl\")})})})]}),isMenuOpen&&/*#__PURE__*/_jsx(\"div\",{className:\"md:hidden bg-white shadow-lg absolute top-full left-0 right-0 py-4\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"container mx-auto px-6 flex flex-col space-y-4\",children:[/*#__PURE__*/_jsx(Link,{to:\"/\",className:\"text-base font-medium py-2 rounded-button whitespace-nowrap cursor-pointer text-[#475467]\",children:\"Home\"}),/*#__PURE__*/_jsx(Link,{to:\"/departments\",className:\"text-base font-medium py-2 rounded-button whitespace-nowrap cursor-pointer text-[#475467]\",children:\"Departments\"}),/*#__PURE__*/_jsx(Link,{to:\"/library\",className:\"text-base font-medium py-2 rounded-button whitespace-nowrap cursor-pointer text-[#475467]\",children:\"Library\"}),/*#__PURE__*/_jsx(\"a\",{href:\"/learning\",className:\"text-base font-medium py-2 rounded-button whitespace-nowrap cursor-pointer text-[#475467]\",children:\"Learning\"}),/*#__PURE__*/_jsx(Link,{to:\"/classroom\",className:\"text-base font-medium py-2 rounded-button whitespace-nowrap cursor-pointer text-[#475467]\",children:\"Classroom\"})]})})]});};export default Navigation;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Link", "useLocation", "jsx", "_jsx", "jsxs", "_jsxs", "Navigation", "_ref", "user", "location", "isScrolled", "setIsScrolled", "isMenuOpen", "setIsMenuOpen", "isActive", "path", "pathname", "startsWith", "handleScroll", "window", "scrollY", "addEventListener", "removeEventListener", "className", "concat", "children", "to", "src", "alt", "href", "firstname", "onClick"], "sources": ["/Users/<USER>/Documents/FatbeamU/react-app/src/components/Navigation.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Link, useLocation } from 'react-router-dom';\n\nconst Navigation = ({ user }) => {\n  const location = useLocation();\n  const [isScrolled, setIsScrolled] = useState(false);\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n  \n  const isActive = (path) => location.pathname.startsWith(path);\n  \n  useEffect(() => {\n    const handleScroll = () => {\n      if (window.scrollY > 50) {\n        setIsScrolled(true);\n      } else {\n        setIsScrolled(false);\n      }\n    };\n    \n    window.addEventListener('scroll', handleScroll);\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, []);\n  \n  return (\n    <nav className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${isScrolled ? 'bg-white shadow-md py-2' : 'bg-transparent py-4'}`}>\n      <div className=\"container mx-auto px-6 flex justify-between items-center\">\n        <Link to=\"/\" className=\"flex items-center\">\n          <div className=\"relative w-16 h-16 overflow-hidden\">\n            <img\n              src=\"https://www.fatbeamfiber.com/hubfs/site-files/logo-fatbeam-fiber.svg\"\n              alt=\"Fatbeam Fiber\"\n              className=\"w-full h-full object-contain brightness-200\"\n            />\n            <div className=\"absolute inset-0 bg-[#15a7dd] opacity-20 animate-pulse rounded-full\"></div>\n          </div>\n          <h1 className={`ml-3 text-2xl font-serif font-bold transition-colors duration-300 drop-shadow-[0_2px_2px_rgba(0,0,0,0.3)] ${isScrolled ? 'text-[#15a7dd]' : 'text-white'}`}>\n            <span className={`transition-colors duration-300 ${isScrolled ? 'text-[#15a7dd]' : 'text-white'}`}>Fatbeam</span> Fiber University\n          </h1>\n        </Link>\n        \n        <div className=\"hidden md:flex space-x-8\">\n          <Link \n            to=\"/\" \n            className={`text-lg font-medium rounded-button whitespace-nowrap cursor-pointer transition-all duration-300 drop-shadow-[0_2px_2px_rgba(0,0,0,0.3)]\n            ${isScrolled\n              ? (isActive('/') && location.pathname === '/' ? 'text-[#15a7dd] border-b-2 border-[#15a7dd]' : 'text-[#15a7dd] hover:text-[#1397c7]')\n              : (isActive('/') && location.pathname === '/' ? 'text-white border-b-2 border-white' : 'text-white hover:text-gray-200')\n            }`}\n          >\n            Home\n          </Link>\n          <Link \n            to=\"/departments\" \n            className={`text-lg font-medium rounded-button whitespace-nowrap cursor-pointer transition-all duration-300 drop-shadow-[0_2px_2px_rgba(0,0,0,0.3)]\n            ${isScrolled\n              ? (isActive('/departments') ? 'text-[#15a7dd] border-b-2 border-[#15a7dd]' : 'text-[#15a7dd] hover:text-[#1397c7]')\n              : (isActive('/departments') ? 'text-white border-b-2 border-white' : 'text-white hover:text-gray-200')\n            }`}\n          >\n            Departments\n          </Link>\n          <Link \n            to=\"/library\" \n            className={`text-lg font-medium rounded-button whitespace-nowrap cursor-pointer transition-all duration-300 drop-shadow-[0_2px_2px_rgba(0,0,0,0.3)]\n            ${isScrolled\n              ? (isActive('/library') ? 'text-[#15a7dd] border-b-2 border-[#15a7dd]' : 'text-[#15a7dd] hover:text-[#1397c7]')\n              : (isActive('/library') ? 'text-white border-b-2 border-white' : 'text-white hover:text-gray-200')\n            }`}\n          >\n            Library\n          </Link>\n          <a \n            href=\"/learning\" \n            className={`text-lg font-medium rounded-button whitespace-nowrap cursor-pointer transition-all duration-300 drop-shadow-[0_2px_2px_rgba(0,0,0,0.3)]\n            ${isScrolled ? 'text-[#15a7dd] hover:text-[#1397c7]' : 'text-white hover:text-gray-200'}`}\n          >\n            Learning\n          </a>\n          <Link \n            to=\"/classroom\" \n            className={`text-lg font-medium rounded-button whitespace-nowrap cursor-pointer transition-all duration-300 drop-shadow-[0_2px_2px_rgba(0,0,0,0.3)]\n            ${isScrolled\n              ? (isActive('/classroom') ? 'text-[#15a7dd] border-b-2 border-[#15a7dd]' : 'text-[#15a7dd] hover:text-[#1397c7]')\n              : (isActive('/classroom') ? 'text-white border-b-2 border-white' : 'text-white hover:text-gray-200')\n            }`}\n          >\n            Classroom\n          </Link>\n          {user && (\n            <Link \n              to=\"/dashboard\" \n              className={`text-lg font-medium rounded-button whitespace-nowrap cursor-pointer transition-all duration-300 drop-shadow-[0_2px_2px_rgba(0,0,0,0.3)]\n              ${isScrolled\n                ? (isActive('/dashboard') ? 'text-[#15a7dd] border-b-2 border-[#15a7dd]' : 'text-[#15a7dd] hover:text-[#1397c7]')\n                : (isActive('/dashboard') ? 'text-white border-b-2 border-white' : 'text-white hover:text-gray-200')\n              }`}\n            >\n              Dashboard\n            </Link>\n          )}\n        </div>\n        \n        <div>\n          {user ? (\n            <span className={`transition-colors duration-300 ${isScrolled ? 'text-gray-600' : 'text-white'}`}>\n              Welcome, {user.firstname}\n            </span>\n          ) : (\n            <a href=\"/learning\" className={`transition-colors duration-300 ${isScrolled ? 'text-blue-600 hover:text-blue-800' : 'text-white hover:text-gray-200'}`}>\n              Login\n            </a>\n          )}\n        </div>\n        \n        {/* Mobile Navigation Toggle */}\n        <div className=\"md:hidden\">\n          <button\n            onClick={() => setIsMenuOpen(!isMenuOpen)}\n            className={`focus:outline-none rounded-button whitespace-nowrap cursor-pointer transition-colors duration-300 ${isScrolled ? 'text-[#15a7dd]' : 'text-white'} drop-shadow-[0_2px_2px_rgba(0,0,0,0.3)]`}\n          >\n            <i className={`fas ${isMenuOpen ? 'fa-times' : 'fa-bars'} text-2xl`}></i>\n          </button>\n        </div>\n      </div>\n      \n      {/* Mobile Menu */}\n      {isMenuOpen && (\n        <div className=\"md:hidden bg-white shadow-lg absolute top-full left-0 right-0 py-4\">\n          <div className=\"container mx-auto px-6 flex flex-col space-y-4\">\n            <Link to=\"/\" className=\"text-base font-medium py-2 rounded-button whitespace-nowrap cursor-pointer text-[#475467]\">\n              Home\n            </Link>\n            <Link to=\"/departments\" className=\"text-base font-medium py-2 rounded-button whitespace-nowrap cursor-pointer text-[#475467]\">\n              Departments\n            </Link>\n            <Link to=\"/library\" className=\"text-base font-medium py-2 rounded-button whitespace-nowrap cursor-pointer text-[#475467]\">\n              Library\n            </Link>\n            <a href=\"/learning\" className=\"text-base font-medium py-2 rounded-button whitespace-nowrap cursor-pointer text-[#475467]\">\n              Learning\n            </a>\n            <Link to=\"/classroom\" className=\"text-base font-medium py-2 rounded-button whitespace-nowrap cursor-pointer text-[#475467]\">\n              Classroom\n            </Link>\n          </div>\n        </div>\n      )}\n    </nav>\n  );\n};\n\nexport default Navigation;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OAASC,IAAI,CAAEC,WAAW,KAAQ,kBAAkB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAErD,KAAM,CAAAC,UAAU,CAAGC,IAAA,EAAc,IAAb,CAAEC,IAAK,CAAC,CAAAD,IAAA,CAC1B,KAAM,CAAAE,QAAQ,CAAGR,WAAW,CAAC,CAAC,CAC9B,KAAM,CAACS,UAAU,CAAEC,aAAa,CAAC,CAAGb,QAAQ,CAAC,KAAK,CAAC,CACnD,KAAM,CAACc,UAAU,CAAEC,aAAa,CAAC,CAAGf,QAAQ,CAAC,KAAK,CAAC,CAEnD,KAAM,CAAAgB,QAAQ,CAAIC,IAAI,EAAKN,QAAQ,CAACO,QAAQ,CAACC,UAAU,CAACF,IAAI,CAAC,CAE7DhB,SAAS,CAAC,IAAM,CACd,KAAM,CAAAmB,YAAY,CAAGA,CAAA,GAAM,CACzB,GAAIC,MAAM,CAACC,OAAO,CAAG,EAAE,CAAE,CACvBT,aAAa,CAAC,IAAI,CAAC,CACrB,CAAC,IAAM,CACLA,aAAa,CAAC,KAAK,CAAC,CACtB,CACF,CAAC,CAEDQ,MAAM,CAACE,gBAAgB,CAAC,QAAQ,CAAEH,YAAY,CAAC,CAC/C,MAAO,IAAMC,MAAM,CAACG,mBAAmB,CAAC,QAAQ,CAAEJ,YAAY,CAAC,CACjE,CAAC,CAAE,EAAE,CAAC,CAEN,mBACEb,KAAA,QAAKkB,SAAS,gEAAAC,MAAA,CAAiEd,UAAU,CAAG,yBAAyB,CAAG,qBAAqB,CAAG,CAAAe,QAAA,eAC9IpB,KAAA,QAAKkB,SAAS,CAAC,0DAA0D,CAAAE,QAAA,eACvEpB,KAAA,CAACL,IAAI,EAAC0B,EAAE,CAAC,GAAG,CAACH,SAAS,CAAC,mBAAmB,CAAAE,QAAA,eACxCpB,KAAA,QAAKkB,SAAS,CAAC,oCAAoC,CAAAE,QAAA,eACjDtB,IAAA,QACEwB,GAAG,CAAC,sEAAsE,CAC1EC,GAAG,CAAC,eAAe,CACnBL,SAAS,CAAC,6CAA6C,CACxD,CAAC,cACFpB,IAAA,QAAKoB,SAAS,CAAC,qEAAqE,CAAM,CAAC,EACxF,CAAC,cACNlB,KAAA,OAAIkB,SAAS,8GAAAC,MAAA,CAA+Gd,UAAU,CAAG,gBAAgB,CAAG,YAAY,CAAG,CAAAe,QAAA,eACzKtB,IAAA,SAAMoB,SAAS,mCAAAC,MAAA,CAAoCd,UAAU,CAAG,gBAAgB,CAAG,YAAY,CAAG,CAAAe,QAAA,CAAC,SAAO,CAAM,CAAC,oBACnH,EAAI,CAAC,EACD,CAAC,cAEPpB,KAAA,QAAKkB,SAAS,CAAC,0BAA0B,CAAAE,QAAA,eACvCtB,IAAA,CAACH,IAAI,EACH0B,EAAE,CAAC,GAAG,CACNH,SAAS,yJAAAC,MAAA,CACPd,UAAU,CACPI,QAAQ,CAAC,GAAG,CAAC,EAAIL,QAAQ,CAACO,QAAQ,GAAK,GAAG,CAAG,4CAA4C,CAAG,qCAAqC,CACjIF,QAAQ,CAAC,GAAG,CAAC,EAAIL,QAAQ,CAACO,QAAQ,GAAK,GAAG,CAAG,oCAAoC,CAAG,gCAAiC,CACvH,CAAAS,QAAA,CACJ,MAED,CAAM,CAAC,cACPtB,IAAA,CAACH,IAAI,EACH0B,EAAE,CAAC,cAAc,CACjBH,SAAS,yJAAAC,MAAA,CACPd,UAAU,CACPI,QAAQ,CAAC,cAAc,CAAC,CAAG,4CAA4C,CAAG,qCAAqC,CAC/GA,QAAQ,CAAC,cAAc,CAAC,CAAG,oCAAoC,CAAG,gCAAiC,CACrG,CAAAW,QAAA,CACJ,aAED,CAAM,CAAC,cACPtB,IAAA,CAACH,IAAI,EACH0B,EAAE,CAAC,UAAU,CACbH,SAAS,yJAAAC,MAAA,CACPd,UAAU,CACPI,QAAQ,CAAC,UAAU,CAAC,CAAG,4CAA4C,CAAG,qCAAqC,CAC3GA,QAAQ,CAAC,UAAU,CAAC,CAAG,oCAAoC,CAAG,gCAAiC,CACjG,CAAAW,QAAA,CACJ,SAED,CAAM,CAAC,cACPtB,IAAA,MACE0B,IAAI,CAAC,WAAW,CAChBN,SAAS,yJAAAC,MAAA,CACPd,UAAU,CAAG,qCAAqC,CAAG,gCAAgC,CAAG,CAAAe,QAAA,CAC3F,UAED,CAAG,CAAC,cACJtB,IAAA,CAACH,IAAI,EACH0B,EAAE,CAAC,YAAY,CACfH,SAAS,yJAAAC,MAAA,CACPd,UAAU,CACPI,QAAQ,CAAC,YAAY,CAAC,CAAG,4CAA4C,CAAG,qCAAqC,CAC7GA,QAAQ,CAAC,YAAY,CAAC,CAAG,oCAAoC,CAAG,gCAAiC,CACnG,CAAAW,QAAA,CACJ,WAED,CAAM,CAAC,CACNjB,IAAI,eACHL,IAAA,CAACH,IAAI,EACH0B,EAAE,CAAC,YAAY,CACfH,SAAS,2JAAAC,MAAA,CACPd,UAAU,CACPI,QAAQ,CAAC,YAAY,CAAC,CAAG,4CAA4C,CAAG,qCAAqC,CAC7GA,QAAQ,CAAC,YAAY,CAAC,CAAG,oCAAoC,CAAG,gCAAiC,CACnG,CAAAW,QAAA,CACJ,WAED,CAAM,CACP,EACE,CAAC,cAENtB,IAAA,QAAAsB,QAAA,CACGjB,IAAI,cACHH,KAAA,SAAMkB,SAAS,mCAAAC,MAAA,CAAoCd,UAAU,CAAG,eAAe,CAAG,YAAY,CAAG,CAAAe,QAAA,EAAC,WACvF,CAACjB,IAAI,CAACsB,SAAS,EACpB,CAAC,cAEP3B,IAAA,MAAG0B,IAAI,CAAC,WAAW,CAACN,SAAS,mCAAAC,MAAA,CAAoCd,UAAU,CAAG,mCAAmC,CAAG,gCAAgC,CAAG,CAAAe,QAAA,CAAC,OAExJ,CAAG,CACJ,CACE,CAAC,cAGNtB,IAAA,QAAKoB,SAAS,CAAC,WAAW,CAAAE,QAAA,cACxBtB,IAAA,WACE4B,OAAO,CAAEA,CAAA,GAAMlB,aAAa,CAAC,CAACD,UAAU,CAAE,CAC1CW,SAAS,sGAAAC,MAAA,CAAuGd,UAAU,CAAG,gBAAgB,CAAG,YAAY,4CAA2C,CAAAe,QAAA,cAEvMtB,IAAA,MAAGoB,SAAS,QAAAC,MAAA,CAASZ,UAAU,CAAG,UAAU,CAAG,SAAS,aAAY,CAAI,CAAC,CACnE,CAAC,CACN,CAAC,EACH,CAAC,CAGLA,UAAU,eACTT,IAAA,QAAKoB,SAAS,CAAC,oEAAoE,CAAAE,QAAA,cACjFpB,KAAA,QAAKkB,SAAS,CAAC,gDAAgD,CAAAE,QAAA,eAC7DtB,IAAA,CAACH,IAAI,EAAC0B,EAAE,CAAC,GAAG,CAACH,SAAS,CAAC,2FAA2F,CAAAE,QAAA,CAAC,MAEnH,CAAM,CAAC,cACPtB,IAAA,CAACH,IAAI,EAAC0B,EAAE,CAAC,cAAc,CAACH,SAAS,CAAC,2FAA2F,CAAAE,QAAA,CAAC,aAE9H,CAAM,CAAC,cACPtB,IAAA,CAACH,IAAI,EAAC0B,EAAE,CAAC,UAAU,CAACH,SAAS,CAAC,2FAA2F,CAAAE,QAAA,CAAC,SAE1H,CAAM,CAAC,cACPtB,IAAA,MAAG0B,IAAI,CAAC,WAAW,CAACN,SAAS,CAAC,2FAA2F,CAAAE,QAAA,CAAC,UAE1H,CAAG,CAAC,cACJtB,IAAA,CAACH,IAAI,EAAC0B,EAAE,CAAC,YAAY,CAACH,SAAS,CAAC,2FAA2F,CAAAE,QAAA,CAAC,WAE5H,CAAM,CAAC,EACJ,CAAC,CACH,CACN,EACE,CAAC,CAEV,CAAC,CAED,cAAe,CAAAnB,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}