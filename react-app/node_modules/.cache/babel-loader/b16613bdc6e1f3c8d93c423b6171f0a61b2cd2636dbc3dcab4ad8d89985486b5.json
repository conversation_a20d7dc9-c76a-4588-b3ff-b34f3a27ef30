{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/FatbeamU/react-app/src/pages/departments/NetworkOperationsDeptPage.js\";\nimport React from 'react';\nimport { Link } from 'react-router-dom';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst NetworkOperationsDeptPage = () => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-white text-gray-800 font-sans pt-20\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container mx-auto px-6 py-12\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center mb-12\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-4xl md:text-5xl font-serif font-bold mb-6\",\n          children: [\"Network Operations \", /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-indigo-600\",\n            children: \"(<PERSON> Weavers)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 10,\n            columnNumber: 32\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 9,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-xl text-gray-600 max-w-3xl mx-auto\",\n          children: \"Craft the invisible pathways of data, ensuring seamless communication across the vast distances of our kingdom.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 12,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 8,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-indigo-50 rounded-lg p-8 text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"i\", {\n          className: \"fas fa-diagram-project text-6xl text-indigo-600 mb-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 18,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-2xl font-bold text-gray-800 mb-4\",\n          children: \"Department Page Coming Soon\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 19,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600 mb-6\",\n          children: \"Our data weavers are currently weaving the digital threads to manifest this page.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 20,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/departments\",\n          className: \"inline-flex items-center px-6 py-3 bg-indigo-600 text-white rounded-full hover:bg-indigo-700 transition-colors duration-300\",\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-arrow-left mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 24,\n            columnNumber: 13\n          }, this), \"Back to Departments\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 23,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 17,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 7,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 6,\n    columnNumber: 5\n  }, this);\n};\n_c = NetworkOperationsDeptPage;\nexport default NetworkOperationsDeptPage;\nvar _c;\n$RefreshReg$(_c, \"NetworkOperationsDeptPage\");", "map": {"version": 3, "names": ["React", "Link", "jsxDEV", "_jsxDEV", "NetworkOperationsDeptPage", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/FatbeamU/react-app/src/pages/departments/NetworkOperationsDeptPage.js"], "sourcesContent": ["import React from 'react';\nimport { Link } from 'react-router-dom';\n\nconst NetworkOperationsDeptPage = () => {\n  return (\n    <div className=\"min-h-screen bg-white text-gray-800 font-sans pt-20\">\n      <div className=\"container mx-auto px-6 py-12\">\n        <div className=\"text-center mb-12\">\n          <h1 className=\"text-4xl md:text-5xl font-serif font-bold mb-6\">\n            Network Operations <span className=\"text-indigo-600\">(Data Weavers)</span>\n          </h1>\n          <p className=\"text-xl text-gray-600 max-w-3xl mx-auto\">\n            Craft the invisible pathways of data, ensuring seamless communication across the vast distances of our kingdom.\n          </p>\n        </div>\n        \n        <div className=\"bg-indigo-50 rounded-lg p-8 text-center\">\n          <i className=\"fas fa-diagram-project text-6xl text-indigo-600 mb-4\"></i>\n          <h2 className=\"text-2xl font-bold text-gray-800 mb-4\">Department Page Coming Soon</h2>\n          <p className=\"text-gray-600 mb-6\">\n            Our data weavers are currently weaving the digital threads to manifest this page.\n          </p>\n          <Link to=\"/departments\" className=\"inline-flex items-center px-6 py-3 bg-indigo-600 text-white rounded-full hover:bg-indigo-700 transition-colors duration-300\">\n            <i className=\"fas fa-arrow-left mr-2\"></i>\n            Back to Departments\n          </Link>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default NetworkOperationsDeptPage;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExC,MAAMC,yBAAyB,GAAGA,CAAA,KAAM;EACtC,oBACED,OAAA;IAAKE,SAAS,EAAC,qDAAqD;IAAAC,QAAA,eAClEH,OAAA;MAAKE,SAAS,EAAC,8BAA8B;MAAAC,QAAA,gBAC3CH,OAAA;QAAKE,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChCH,OAAA;UAAIE,SAAS,EAAC,gDAAgD;UAAAC,QAAA,GAAC,qBAC1C,eAAAH,OAAA;YAAME,SAAS,EAAC,iBAAiB;YAAAC,QAAA,EAAC;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxE,CAAC,eACLP,OAAA;UAAGE,SAAS,EAAC,yCAAyC;UAAAC,QAAA,EAAC;QAEvD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAENP,OAAA;QAAKE,SAAS,EAAC,yCAAyC;QAAAC,QAAA,gBACtDH,OAAA;UAAGE,SAAS,EAAC;QAAsD;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACxEP,OAAA;UAAIE,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EAAC;QAA2B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACtFP,OAAA;UAAGE,SAAS,EAAC,oBAAoB;UAAAC,QAAA,EAAC;QAElC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACJP,OAAA,CAACF,IAAI;UAACU,EAAE,EAAC,cAAc;UAACN,SAAS,EAAC,6HAA6H;UAAAC,QAAA,gBAC7JH,OAAA;YAAGE,SAAS,EAAC;UAAwB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,uBAE5C;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACE,EAAA,GA3BIR,yBAAyB;AA6B/B,eAAeA,yBAAyB;AAAC,IAAAQ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}