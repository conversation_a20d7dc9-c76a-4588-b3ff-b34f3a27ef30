{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/FatbeamU/react-app/src/pages/DepartmentPages.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useParams, Link } from 'react-router-dom';\n\n// Import department page components\nimport FinancialDeptPage from './departments/FinancialDeptPage';\nimport ConstructionDeptPage from './departments/ConstructionDeptPage';\nimport TowerTechniciansDeptPage from './departments/TowerTechniciansDeptPage';\nimport HumanRelationsDeptPage from './departments/HumanRelationsDeptPage';\nimport SalesDeptPage from './departments/SalesDeptPage';\nimport NetworkOperationsDeptPage from './departments/NetworkOperationsDeptPage';\nimport LeadershipTeamPage from './departments/LeadershipTeamPage';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst DepartmentPages = ({\n  deptType\n}) => {\n  _s();\n  const {\n    deptName\n  } = useParams();\n  const [activeTab, setActiveTab] = useState('overview');\n  const [isScrolled, setIsScrolled] = useState(false);\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n\n  // Determine which department to show\n  const currentDept = deptType || deptName;\n  useEffect(() => {\n    const handleScroll = () => {\n      if (window.scrollY > 50) {\n        setIsScrolled(true);\n      } else {\n        setIsScrolled(false);\n      }\n    };\n    window.addEventListener('scroll', handleScroll);\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, []);\n\n  // If no specific department, show department overview\n  if (!currentDept) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-white text-gray-800 font-sans pt-20\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container mx-auto px-6 py-12\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center mb-16\",\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"text-4xl md:text-5xl font-serif font-bold mb-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-[#15a7dd]\",\n              children: \"Mystical\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 42,\n              columnNumber: 15\n            }, this), \" Departments\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 41,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-[#475467] max-w-3xl mx-auto text-lg\",\n            children: \"Explore our specialized schools of technomancy, each focusing on a different aspect of the magical fiber arts. Choose a department below to learn more about their programs, faculty, and opportunities.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 44,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-20 h-1 bg-[#15a7dd] mx-auto mt-6\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 48,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 40,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\",\n          children: [{\n            title: \"Finance Alchemy\",\n            icon: \"fa-coins\",\n            route: \"/FinanceDept\",\n            color: \"yellow\",\n            image: \"https://readdy.ai/api/search-image?query=A%20mystical%20treasury%20room%20with%20floating%20gold%20coins%20and%20glowing%20blue%20financial%20charts&width=400&height=300&seq=dept1&orientation=landscape\",\n            description: \"Transform raw resources into financial prosperity through the ancient arts of budget management and revenue enchantment.\"\n          }, {\n            title: \"Tower Levitation\",\n            icon: \"fa-tower-broadcast\",\n            route: \"/TowerTechnicians\",\n            color: \"blue\",\n            image: \"https://readdy.ai/api/search-image?query=Magical%20technicians%20working%20on%20a%20tall%20communications%20tower%20with%20glowing%20blue%20fiber%20optic%20connections&width=400&height=300&seq=dept2&orientation=landscape\",\n            description: \"Master the heights of our magical infrastructure, ensuring the uninterrupted flow of arcane data through our network.\"\n          }, {\n            title: \"Sales Sorcery\",\n            icon: \"fa-handshake\",\n            route: \"/SalesDept\",\n            color: \"green\",\n            image: \"https://readdy.ai/api/search-image?query=Wizards%20in%20business%20attire%20demonstrating%20magical%20fiber%20optic%20products%20to%20clients&width=400&height=300&seq=dept3&orientation=landscape\",\n            description: \"Learn the mystical arts of persuasion and relationship building to spread our magical connectivity throughout the realm.\"\n          }, {\n            title: \"Construction Earth Magic\",\n            icon: \"fa-shovel\",\n            route: \"/ConstructionDept\",\n            color: \"orange\",\n            image: \"https://readdy.ai/api/search-image?query=Wizards%20using%20magical%20tools%20to%20install%20underground%20fiber%20optic%20cables&width=400&height=300&seq=dept4&orientation=landscape\",\n            description: \"Harness the elements to lay the physical foundations of our network, connecting the material world to the digital realm.\"\n          }, {\n            title: \"Human Relations\",\n            icon: \"fa-brain\",\n            route: \"/HumanRelationsDept\",\n            color: \"purple\",\n            image: \"https://readdy.ai/api/search-image?query=A%20serene%20office%20space%20with%20floating%20crystal%20orbs%20showing%20employee%20auras&width=400&height=300&seq=dept5&orientation=landscape\",\n            description: \"Harness psychic energies to maintain harmony among our magical workforce, managing personnel records with telepathic precision.\"\n          }, {\n            title: \"Network Weavers\",\n            icon: \"fa-diagram-project\",\n            route: \"/NetworkOperations\",\n            color: \"indigo\",\n            image: \"https://readdy.ai/api/search-image?query=Technical%20wizards%20working%20with%20complex%20floating%20network%20diagrams%20made%20of%20blue%20light&width=400&height=300&seq=dept6&orientation=landscape\",\n            description: \"Craft the invisible pathways of data, ensuring seamless communication across the vast distances of our kingdom.\"\n          }, {\n            title: \"Leadership Council\",\n            icon: \"fa-crown\",\n            route: \"/LeadershipTeam\",\n            color: \"red\",\n            image: \"https://readdy.ai/api/search-image?query=Wise%20magical%20leaders%20in%20an%20elegant%20council%20chamber%20with%20floating%20strategic%20displays&width=400&height=300&seq=dept7&orientation=landscape\",\n            description: \"The guiding council of master technomancers who oversee the strategic direction of our mystical institution.\"\n          }].map((dept, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-lg overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300 group\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative h-48 overflow-hidden\",\n              children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                src: dept.image,\n                alt: dept.title,\n                className: \"w-full h-full object-cover transition-transform duration-500 group-hover:scale-110\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 112,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute inset-0 bg-gradient-to-t from-black/70 to-transparent\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 117,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute bottom-4 left-4 text-white\",\n                children: /*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"text-xl font-serif font-bold\",\n                  children: dept.title\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 119,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 118,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute top-4 right-4 w-12 h-12 rounded-full bg-[#15a7dd]/90 flex items-center justify-center shadow-lg\",\n                children: /*#__PURE__*/_jsxDEV(\"i\", {\n                  className: `fas ${dept.icon} text-white text-lg`\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 122,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 121,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 111,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-[#475467] mb-6\",\n                children: dept.description\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 126,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Link, {\n                to: dept.route,\n                className: \"inline-flex items-center px-6 py-3 bg-[#15a7dd] text-white rounded-full hover:bg-[#1397c7] transition-all duration-300\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"Explore Department\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 131,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"fas fa-arrow-right ml-2 transition-transform duration-300 group-hover:translate-x-1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 132,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 127,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 125,\n              columnNumber: 17\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 110,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 51,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-16 text-center\",\n          children: /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/\",\n            className: \"inline-flex items-center px-8 py-3 border-2 border-[#15a7dd] text-[#15a7dd] rounded-full hover:bg-[#15a7dd] hover:text-white transition-all duration-300\",\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fas fa-home mr-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 141,\n              columnNumber: 15\n            }, this), \"Return to Main Campus\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 140,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 139,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 39,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 38,\n      columnNumber: 7\n    }, this);\n  }\n\n  // Render specific department page\n  const renderDepartmentPage = () => {\n    switch (currentDept) {\n      case 'financial':\n        return /*#__PURE__*/_jsxDEV(FinancialDeptPage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 154,\n          columnNumber: 16\n        }, this);\n      case 'construction':\n        return /*#__PURE__*/_jsxDEV(ConstructionDeptPage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 156,\n          columnNumber: 16\n        }, this);\n      case 'tower':\n        return /*#__PURE__*/_jsxDEV(TowerTechniciansDeptPage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 158,\n          columnNumber: 16\n        }, this);\n      case 'hr':\n        return /*#__PURE__*/_jsxDEV(HumanRelationsDeptPage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 160,\n          columnNumber: 16\n        }, this);\n      case 'sales':\n        return /*#__PURE__*/_jsxDEV(SalesDeptPage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 162,\n          columnNumber: 16\n        }, this);\n      case 'network':\n        return /*#__PURE__*/_jsxDEV(NetworkOperationsDeptPage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 164,\n          columnNumber: 16\n        }, this);\n      case 'leadership':\n        return /*#__PURE__*/_jsxDEV(LeadershipTeamPage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 166,\n          columnNumber: 16\n        }, this);\n      default:\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"min-h-screen bg-white text-gray-800 font-sans pt-20\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"container mx-auto px-6 py-12 text-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"text-4xl font-serif font-bold mb-6 text-[#15a7dd]\",\n              children: \"Department Not Found\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 171,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-[#475467] mb-8\",\n              children: \"The requested department could not be found.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 172,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/departments\",\n              className: \"inline-flex items-center px-8 py-3 bg-[#15a7dd] text-white rounded-full hover:bg-[#1397c7] transition-all duration-300\",\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-arrow-left mr-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 174,\n                columnNumber: 17\n              }, this), \"Back to Departments\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 173,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 170,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 169,\n          columnNumber: 11\n        }, this);\n    }\n  };\n  return renderDepartmentPage();\n};\n_s(DepartmentPages, \"ea9ZIVSHD8oA+4ZQCZD+dC5iD/8=\", false, function () {\n  return [useParams];\n});\n_c = DepartmentPages;\nexport default DepartmentPages;\nvar _c;\n$RefreshReg$(_c, \"DepartmentPages\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useParams", "Link", "FinancialDeptPage", "ConstructionDeptPage", "TowerTechniciansDeptPage", "HumanRelationsDeptPage", "SalesDeptPage", "NetworkOperationsDeptPage", "LeadershipTeamPage", "jsxDEV", "_jsxDEV", "DepartmentPages", "deptType", "_s", "deptName", "activeTab", "setActiveTab", "isScrolled", "setIsScrolled", "isMenuOpen", "setIsMenuOpen", "currentDept", "handleScroll", "window", "scrollY", "addEventListener", "removeEventListener", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "title", "icon", "route", "color", "image", "description", "map", "dept", "index", "src", "alt", "to", "renderDepartmentPage", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/FatbeamU/react-app/src/pages/DepartmentPages.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useParams, Link } from 'react-router-dom';\n\n// Import department page components\nimport FinancialDeptPage from './departments/FinancialDeptPage';\nimport ConstructionDeptPage from './departments/ConstructionDeptPage';\nimport TowerTechniciansDeptPage from './departments/TowerTechniciansDeptPage';\nimport HumanRelationsDeptPage from './departments/HumanRelationsDeptPage';\nimport SalesDeptPage from './departments/SalesDeptPage';\nimport NetworkOperationsDeptPage from './departments/NetworkOperationsDeptPage';\nimport LeadershipTeamPage from './departments/LeadershipTeamPage';\n\nconst DepartmentPages = ({ deptType }) => {\n  const { deptName } = useParams();\n  const [activeTab, setActiveTab] = useState('overview');\n  const [isScrolled, setIsScrolled] = useState(false);\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n\n  // Determine which department to show\n  const currentDept = deptType || deptName;\n\n  useEffect(() => {\n    const handleScroll = () => {\n      if (window.scrollY > 50) {\n        setIsScrolled(true);\n      } else {\n        setIsScrolled(false);\n      }\n    };\n    \n    window.addEventListener('scroll', handleScroll);\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, []);\n\n  // If no specific department, show department overview\n  if (!currentDept) {\n    return (\n      <div className=\"min-h-screen bg-white text-gray-800 font-sans pt-20\">\n        <div className=\"container mx-auto px-6 py-12\">\n          <div className=\"text-center mb-16\">\n            <h1 className=\"text-4xl md:text-5xl font-serif font-bold mb-6\">\n              <span className=\"text-[#15a7dd]\">Mystical</span> Departments\n            </h1>\n            <p className=\"text-[#475467] max-w-3xl mx-auto text-lg\">\n              Explore our specialized schools of technomancy, each focusing on a different aspect of the magical fiber arts. \n              Choose a department below to learn more about their programs, faculty, and opportunities.\n            </p>\n            <div className=\"w-20 h-1 bg-[#15a7dd] mx-auto mt-6\"></div>\n          </div>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\">\n            {[\n              {\n                title: \"Finance Alchemy\",\n                icon: \"fa-coins\",\n                route: \"/FinanceDept\",\n                color: \"yellow\",\n                image: \"https://readdy.ai/api/search-image?query=A%20mystical%20treasury%20room%20with%20floating%20gold%20coins%20and%20glowing%20blue%20financial%20charts&width=400&height=300&seq=dept1&orientation=landscape\",\n                description: \"Transform raw resources into financial prosperity through the ancient arts of budget management and revenue enchantment.\"\n              },\n              {\n                title: \"Tower Levitation\",\n                icon: \"fa-tower-broadcast\",\n                route: \"/TowerTechnicians\",\n                color: \"blue\",\n                image: \"https://readdy.ai/api/search-image?query=Magical%20technicians%20working%20on%20a%20tall%20communications%20tower%20with%20glowing%20blue%20fiber%20optic%20connections&width=400&height=300&seq=dept2&orientation=landscape\",\n                description: \"Master the heights of our magical infrastructure, ensuring the uninterrupted flow of arcane data through our network.\"\n              },\n              {\n                title: \"Sales Sorcery\",\n                icon: \"fa-handshake\",\n                route: \"/SalesDept\",\n                color: \"green\",\n                image: \"https://readdy.ai/api/search-image?query=Wizards%20in%20business%20attire%20demonstrating%20magical%20fiber%20optic%20products%20to%20clients&width=400&height=300&seq=dept3&orientation=landscape\",\n                description: \"Learn the mystical arts of persuasion and relationship building to spread our magical connectivity throughout the realm.\"\n              },\n              {\n                title: \"Construction Earth Magic\",\n                icon: \"fa-shovel\",\n                route: \"/ConstructionDept\",\n                color: \"orange\",\n                image: \"https://readdy.ai/api/search-image?query=Wizards%20using%20magical%20tools%20to%20install%20underground%20fiber%20optic%20cables&width=400&height=300&seq=dept4&orientation=landscape\",\n                description: \"Harness the elements to lay the physical foundations of our network, connecting the material world to the digital realm.\"\n              },\n              {\n                title: \"Human Relations\",\n                icon: \"fa-brain\",\n                route: \"/HumanRelationsDept\",\n                color: \"purple\",\n                image: \"https://readdy.ai/api/search-image?query=A%20serene%20office%20space%20with%20floating%20crystal%20orbs%20showing%20employee%20auras&width=400&height=300&seq=dept5&orientation=landscape\",\n                description: \"Harness psychic energies to maintain harmony among our magical workforce, managing personnel records with telepathic precision.\"\n              },\n              {\n                title: \"Network Weavers\",\n                icon: \"fa-diagram-project\",\n                route: \"/NetworkOperations\",\n                color: \"indigo\",\n                image: \"https://readdy.ai/api/search-image?query=Technical%20wizards%20working%20with%20complex%20floating%20network%20diagrams%20made%20of%20blue%20light&width=400&height=300&seq=dept6&orientation=landscape\",\n                description: \"Craft the invisible pathways of data, ensuring seamless communication across the vast distances of our kingdom.\"\n              },\n              {\n                title: \"Leadership Council\",\n                icon: \"fa-crown\",\n                route: \"/LeadershipTeam\",\n                color: \"red\",\n                image: \"https://readdy.ai/api/search-image?query=Wise%20magical%20leaders%20in%20an%20elegant%20council%20chamber%20with%20floating%20strategic%20displays&width=400&height=300&seq=dept7&orientation=landscape\",\n                description: \"The guiding council of master technomancers who oversee the strategic direction of our mystical institution.\"\n              }\n            ].map((dept, index) => (\n              <div key={index} className=\"bg-white rounded-lg overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300 group\">\n                <div className=\"relative h-48 overflow-hidden\">\n                  <img\n                    src={dept.image}\n                    alt={dept.title}\n                    className=\"w-full h-full object-cover transition-transform duration-500 group-hover:scale-110\"\n                  />\n                  <div className=\"absolute inset-0 bg-gradient-to-t from-black/70 to-transparent\"></div>\n                  <div className=\"absolute bottom-4 left-4 text-white\">\n                    <h3 className=\"text-xl font-serif font-bold\">{dept.title}</h3>\n                  </div>\n                  <div className=\"absolute top-4 right-4 w-12 h-12 rounded-full bg-[#15a7dd]/90 flex items-center justify-center shadow-lg\">\n                    <i className={`fas ${dept.icon} text-white text-lg`}></i>\n                  </div>\n                </div>\n                <div className=\"p-6\">\n                  <p className=\"text-[#475467] mb-6\">{dept.description}</p>\n                  <Link\n                    to={dept.route}\n                    className=\"inline-flex items-center px-6 py-3 bg-[#15a7dd] text-white rounded-full hover:bg-[#1397c7] transition-all duration-300\"\n                  >\n                    <span>Explore Department</span>\n                    <i className=\"fas fa-arrow-right ml-2 transition-transform duration-300 group-hover:translate-x-1\"></i>\n                  </Link>\n                </div>\n              </div>\n            ))}\n          </div>\n\n          <div className=\"mt-16 text-center\">\n            <Link to=\"/\" className=\"inline-flex items-center px-8 py-3 border-2 border-[#15a7dd] text-[#15a7dd] rounded-full hover:bg-[#15a7dd] hover:text-white transition-all duration-300\">\n              <i className=\"fas fa-home mr-2\"></i>\n              Return to Main Campus\n            </Link>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  // Render specific department page\n  const renderDepartmentPage = () => {\n    switch (currentDept) {\n      case 'financial':\n        return <FinancialDeptPage />;\n      case 'construction':\n        return <ConstructionDeptPage />;\n      case 'tower':\n        return <TowerTechniciansDeptPage />;\n      case 'hr':\n        return <HumanRelationsDeptPage />;\n      case 'sales':\n        return <SalesDeptPage />;\n      case 'network':\n        return <NetworkOperationsDeptPage />;\n      case 'leadership':\n        return <LeadershipTeamPage />;\n      default:\n        return (\n          <div className=\"min-h-screen bg-white text-gray-800 font-sans pt-20\">\n            <div className=\"container mx-auto px-6 py-12 text-center\">\n              <h1 className=\"text-4xl font-serif font-bold mb-6 text-[#15a7dd]\">Department Not Found</h1>\n              <p className=\"text-[#475467] mb-8\">The requested department could not be found.</p>\n              <Link to=\"/departments\" className=\"inline-flex items-center px-8 py-3 bg-[#15a7dd] text-white rounded-full hover:bg-[#1397c7] transition-all duration-300\">\n                <i className=\"fas fa-arrow-left mr-2\"></i>\n                Back to Departments\n              </Link>\n            </div>\n          </div>\n        );\n    }\n  };\n\n  return renderDepartmentPage();\n};\n\nexport default DepartmentPages;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,EAAEC,IAAI,QAAQ,kBAAkB;;AAElD;AACA,OAAOC,iBAAiB,MAAM,iCAAiC;AAC/D,OAAOC,oBAAoB,MAAM,oCAAoC;AACrE,OAAOC,wBAAwB,MAAM,wCAAwC;AAC7E,OAAOC,sBAAsB,MAAM,sCAAsC;AACzE,OAAOC,aAAa,MAAM,6BAA6B;AACvD,OAAOC,yBAAyB,MAAM,yCAAyC;AAC/E,OAAOC,kBAAkB,MAAM,kCAAkC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElE,MAAMC,eAAe,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EACxC,MAAM;IAAEC;EAAS,CAAC,GAAGd,SAAS,CAAC,CAAC;EAChC,MAAM,CAACe,SAAS,EAAEC,YAAY,CAAC,GAAGlB,QAAQ,CAAC,UAAU,CAAC;EACtD,MAAM,CAACmB,UAAU,EAAEC,aAAa,CAAC,GAAGpB,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACqB,UAAU,EAAEC,aAAa,CAAC,GAAGtB,QAAQ,CAAC,KAAK,CAAC;;EAEnD;EACA,MAAMuB,WAAW,GAAGT,QAAQ,IAAIE,QAAQ;EAExCf,SAAS,CAAC,MAAM;IACd,MAAMuB,YAAY,GAAGA,CAAA,KAAM;MACzB,IAAIC,MAAM,CAACC,OAAO,GAAG,EAAE,EAAE;QACvBN,aAAa,CAAC,IAAI,CAAC;MACrB,CAAC,MAAM;QACLA,aAAa,CAAC,KAAK,CAAC;MACtB;IACF,CAAC;IAEDK,MAAM,CAACE,gBAAgB,CAAC,QAAQ,EAAEH,YAAY,CAAC;IAC/C,OAAO,MAAMC,MAAM,CAACG,mBAAmB,CAAC,QAAQ,EAAEJ,YAAY,CAAC;EACjE,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,IAAI,CAACD,WAAW,EAAE;IAChB,oBACEX,OAAA;MAAKiB,SAAS,EAAC,qDAAqD;MAAAC,QAAA,eAClElB,OAAA;QAAKiB,SAAS,EAAC,8BAA8B;QAAAC,QAAA,gBAC3ClB,OAAA;UAAKiB,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChClB,OAAA;YAAIiB,SAAS,EAAC,gDAAgD;YAAAC,QAAA,gBAC5DlB,OAAA;cAAMiB,SAAS,EAAC,gBAAgB;cAAAC,QAAA,EAAC;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,gBAClD;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLtB,OAAA;YAAGiB,SAAS,EAAC,0CAA0C;YAAAC,QAAA,EAAC;UAGxD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJtB,OAAA;YAAKiB,SAAS,EAAC;UAAoC;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvD,CAAC,eAENtB,OAAA;UAAKiB,SAAS,EAAC,sDAAsD;UAAAC,QAAA,EAClE,CACC;YACEK,KAAK,EAAE,iBAAiB;YACxBC,IAAI,EAAE,UAAU;YAChBC,KAAK,EAAE,cAAc;YACrBC,KAAK,EAAE,QAAQ;YACfC,KAAK,EAAE,2MAA2M;YAClNC,WAAW,EAAE;UACf,CAAC,EACD;YACEL,KAAK,EAAE,kBAAkB;YACzBC,IAAI,EAAE,oBAAoB;YAC1BC,KAAK,EAAE,mBAAmB;YAC1BC,KAAK,EAAE,MAAM;YACbC,KAAK,EAAE,8NAA8N;YACrOC,WAAW,EAAE;UACf,CAAC,EACD;YACEL,KAAK,EAAE,eAAe;YACtBC,IAAI,EAAE,cAAc;YACpBC,KAAK,EAAE,YAAY;YACnBC,KAAK,EAAE,OAAO;YACdC,KAAK,EAAE,oMAAoM;YAC3MC,WAAW,EAAE;UACf,CAAC,EACD;YACEL,KAAK,EAAE,0BAA0B;YACjCC,IAAI,EAAE,WAAW;YACjBC,KAAK,EAAE,mBAAmB;YAC1BC,KAAK,EAAE,QAAQ;YACfC,KAAK,EAAE,uLAAuL;YAC9LC,WAAW,EAAE;UACf,CAAC,EACD;YACEL,KAAK,EAAE,iBAAiB;YACxBC,IAAI,EAAE,UAAU;YAChBC,KAAK,EAAE,qBAAqB;YAC5BC,KAAK,EAAE,QAAQ;YACfC,KAAK,EAAE,2LAA2L;YAClMC,WAAW,EAAE;UACf,CAAC,EACD;YACEL,KAAK,EAAE,iBAAiB;YACxBC,IAAI,EAAE,oBAAoB;YAC1BC,KAAK,EAAE,oBAAoB;YAC3BC,KAAK,EAAE,QAAQ;YACfC,KAAK,EAAE,yMAAyM;YAChNC,WAAW,EAAE;UACf,CAAC,EACD;YACEL,KAAK,EAAE,oBAAoB;YAC3BC,IAAI,EAAE,UAAU;YAChBC,KAAK,EAAE,iBAAiB;YACxBC,KAAK,EAAE,KAAK;YACZC,KAAK,EAAE,yMAAyM;YAChNC,WAAW,EAAE;UACf,CAAC,CACF,CAACC,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBAChB/B,OAAA;YAAiBiB,SAAS,EAAC,iGAAiG;YAAAC,QAAA,gBAC1HlB,OAAA;cAAKiB,SAAS,EAAC,+BAA+B;cAAAC,QAAA,gBAC5ClB,OAAA;gBACEgC,GAAG,EAAEF,IAAI,CAACH,KAAM;gBAChBM,GAAG,EAAEH,IAAI,CAACP,KAAM;gBAChBN,SAAS,EAAC;cAAoF;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/F,CAAC,eACFtB,OAAA;gBAAKiB,SAAS,EAAC;cAAgE;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACtFtB,OAAA;gBAAKiB,SAAS,EAAC,qCAAqC;gBAAAC,QAAA,eAClDlB,OAAA;kBAAIiB,SAAS,EAAC,8BAA8B;kBAAAC,QAAA,EAAEY,IAAI,CAACP;gBAAK;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3D,CAAC,eACNtB,OAAA;gBAAKiB,SAAS,EAAC,0GAA0G;gBAAAC,QAAA,eACvHlB,OAAA;kBAAGiB,SAAS,EAAE,OAAOa,IAAI,CAACN,IAAI;gBAAsB;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNtB,OAAA;cAAKiB,SAAS,EAAC,KAAK;cAAAC,QAAA,gBAClBlB,OAAA;gBAAGiB,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,EAAEY,IAAI,CAACF;cAAW;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACzDtB,OAAA,CAACT,IAAI;gBACH2C,EAAE,EAAEJ,IAAI,CAACL,KAAM;gBACfR,SAAS,EAAC,wHAAwH;gBAAAC,QAAA,gBAElIlB,OAAA;kBAAAkB,QAAA,EAAM;gBAAkB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC/BtB,OAAA;kBAAGiB,SAAS,EAAC;gBAAqF;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA,GAxBES,KAAK;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAyBV,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAENtB,OAAA;UAAKiB,SAAS,EAAC,mBAAmB;UAAAC,QAAA,eAChClB,OAAA,CAACT,IAAI;YAAC2C,EAAE,EAAC,GAAG;YAACjB,SAAS,EAAC,0JAA0J;YAAAC,QAAA,gBAC/KlB,OAAA;cAAGiB,SAAS,EAAC;YAAkB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,yBAEtC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;;EAEA;EACA,MAAMa,oBAAoB,GAAGA,CAAA,KAAM;IACjC,QAAQxB,WAAW;MACjB,KAAK,WAAW;QACd,oBAAOX,OAAA,CAACR,iBAAiB;UAAA2B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC9B,KAAK,cAAc;QACjB,oBAAOtB,OAAA,CAACP,oBAAoB;UAAA0B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACjC,KAAK,OAAO;QACV,oBAAOtB,OAAA,CAACN,wBAAwB;UAAAyB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACrC,KAAK,IAAI;QACP,oBAAOtB,OAAA,CAACL,sBAAsB;UAAAwB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACnC,KAAK,OAAO;QACV,oBAAOtB,OAAA,CAACJ,aAAa;UAAAuB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC1B,KAAK,SAAS;QACZ,oBAAOtB,OAAA,CAACH,yBAAyB;UAAAsB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACtC,KAAK,YAAY;QACf,oBAAOtB,OAAA,CAACF,kBAAkB;UAAAqB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC/B;QACE,oBACEtB,OAAA;UAAKiB,SAAS,EAAC,qDAAqD;UAAAC,QAAA,eAClElB,OAAA;YAAKiB,SAAS,EAAC,0CAA0C;YAAAC,QAAA,gBACvDlB,OAAA;cAAIiB,SAAS,EAAC,mDAAmD;cAAAC,QAAA,EAAC;YAAoB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC3FtB,OAAA;cAAGiB,SAAS,EAAC,qBAAqB;cAAAC,QAAA,EAAC;YAA4C;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACnFtB,OAAA,CAACT,IAAI;cAAC2C,EAAE,EAAC,cAAc;cAACjB,SAAS,EAAC,wHAAwH;cAAAC,QAAA,gBACxJlB,OAAA;gBAAGiB,SAAS,EAAC;cAAwB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,uBAE5C;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;IAEZ;EACF,CAAC;EAED,OAAOa,oBAAoB,CAAC,CAAC;AAC/B,CAAC;AAAChC,EAAA,CA3KIF,eAAe;EAAA,QACEX,SAAS;AAAA;AAAA8C,EAAA,GAD1BnC,eAAe;AA6KrB,eAAeA,eAAe;AAAC,IAAAmC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}