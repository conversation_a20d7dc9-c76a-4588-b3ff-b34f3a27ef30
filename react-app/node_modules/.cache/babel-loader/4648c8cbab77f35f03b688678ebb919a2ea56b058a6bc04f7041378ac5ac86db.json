{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/FatbeamU/react-app/src/App.js\";\nimport React from 'react';\nimport { BrowserRouter as Router, Routes, Route } from 'react-router-dom';\nimport Navigation from './components/Navigation';\nimport MainPage from './pages/MainPage';\nimport DepartmentPages from './pages/DepartmentPages';\nimport LibraryPages from './pages/LibraryPages';\nimport ClassroomPage from './pages/ClassroomPage';\nimport UnifiedDashboard from './components/UnifiedDashboard';\nimport './App.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(Router, {\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"App\",\n      children: [/*#__PURE__*/_jsxDEV(Navigation, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 15,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Routes, {\n        children: [/*#__PURE__*/_jsxDEV(Route, {\n          path: \"/\",\n          element: /*#__PURE__*/_jsxDEV(MainPage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 17,\n            columnNumber: 36\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 17,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/departments\",\n          element: /*#__PURE__*/_jsxDEV(DepartmentPages, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 18,\n            columnNumber: 47\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 18,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/departments/:deptName\",\n          element: /*#__PURE__*/_jsxDEV(DepartmentPages, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 19,\n            columnNumber: 57\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 19,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/library\",\n          element: /*#__PURE__*/_jsxDEV(LibraryPages, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 20,\n            columnNumber: 43\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 20,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/classroom\",\n          element: /*#__PURE__*/_jsxDEV(ClassroomPage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 21,\n            columnNumber: 45\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 21,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/dashboard\",\n          element: /*#__PURE__*/_jsxDEV(UnifiedDashboard, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 22,\n            columnNumber: 45\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 22,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/FinanceDept\",\n          element: /*#__PURE__*/_jsxDEV(DepartmentPages, {\n            deptType: \"financial\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 24,\n            columnNumber: 47\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 24,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/ConstructionDept\",\n          element: /*#__PURE__*/_jsxDEV(DepartmentPages, {\n            deptType: \"construction\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 25,\n            columnNumber: 52\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 25,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/TowerTechnicians\",\n          element: /*#__PURE__*/_jsxDEV(DepartmentPages, {\n            deptType: \"tower\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 26,\n            columnNumber: 52\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 26,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/HumanRelationsDept\",\n          element: /*#__PURE__*/_jsxDEV(DepartmentPages, {\n            deptType: \"hr\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 27,\n            columnNumber: 54\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 27,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/SalesDept\",\n          element: /*#__PURE__*/_jsxDEV(DepartmentPages, {\n            deptType: \"sales\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 28,\n            columnNumber: 45\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 28,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/NetworkOperations\",\n          element: /*#__PURE__*/_jsxDEV(DepartmentPages, {\n            deptType: \"network\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 29,\n            columnNumber: 53\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 29,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/LeadershipTeam\",\n          element: /*#__PURE__*/_jsxDEV(DepartmentPages, {\n            deptType: \"leadership\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 30,\n            columnNumber: 50\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 30,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 16,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 14,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 13,\n    columnNumber: 5\n  }, this);\n}\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "Navigation", "MainPage", "DepartmentPages", "LibraryPages", "ClassroomPage", "UnifiedDashboard", "jsxDEV", "_jsxDEV", "App", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "path", "element", "deptType", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/FatbeamU/react-app/src/App.js"], "sourcesContent": ["import React from 'react';\nimport { BrowserRouter as Router, Routes, Route } from 'react-router-dom';\nimport Navigation from './components/Navigation';\nimport MainPage from './pages/MainPage';\nimport DepartmentPages from './pages/DepartmentPages';\nimport LibraryPages from './pages/LibraryPages';\nimport ClassroomPage from './pages/ClassroomPage';\nimport UnifiedDashboard from './components/UnifiedDashboard';\nimport './App.css';\n\nfunction App() {\n  return (\n    <Router>\n      <div className=\"App\">\n        <Navigation />\n        <Routes>\n          <Route path=\"/\" element={<MainPage />} />\n          <Route path=\"/departments\" element={<DepartmentPages />} />\n          <Route path=\"/departments/:deptName\" element={<DepartmentPages />} />\n          <Route path=\"/library\" element={<LibraryPages />} />\n          <Route path=\"/classroom\" element={<ClassroomPage />} />\n          <Route path=\"/dashboard\" element={<UnifiedDashboard />} />\n          {/* Department-specific routes */}\n          <Route path=\"/FinanceDept\" element={<DepartmentPages deptType=\"financial\" />} />\n          <Route path=\"/ConstructionDept\" element={<DepartmentPages deptType=\"construction\" />} />\n          <Route path=\"/TowerTechnicians\" element={<DepartmentPages deptType=\"tower\" />} />\n          <Route path=\"/HumanRelationsDept\" element={<DepartmentPages deptType=\"hr\" />} />\n          <Route path=\"/SalesDept\" element={<DepartmentPages deptType=\"sales\" />} />\n          <Route path=\"/NetworkOperations\" element={<DepartmentPages deptType=\"network\" />} />\n          <Route path=\"/LeadershipTeam\" element={<DepartmentPages deptType=\"leadership\" />} />\n        </Routes>\n      </div>\n    </Router>\n  );\n}\n\nexport default App;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,aAAa,IAAIC,MAAM,EAAEC,MAAM,EAAEC,KAAK,QAAQ,kBAAkB;AACzE,OAAOC,UAAU,MAAM,yBAAyB;AAChD,OAAOC,QAAQ,MAAM,kBAAkB;AACvC,OAAOC,eAAe,MAAM,yBAAyB;AACrD,OAAOC,YAAY,MAAM,sBAAsB;AAC/C,OAAOC,aAAa,MAAM,uBAAuB;AACjD,OAAOC,gBAAgB,MAAM,+BAA+B;AAC5D,OAAO,WAAW;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnB,SAASC,GAAGA,CAAA,EAAG;EACb,oBACED,OAAA,CAACV,MAAM;IAAAY,QAAA,eACLF,OAAA;MAAKG,SAAS,EAAC,KAAK;MAAAD,QAAA,gBAClBF,OAAA,CAACP,UAAU;QAAAW,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACdP,OAAA,CAACT,MAAM;QAAAW,QAAA,gBACLF,OAAA,CAACR,KAAK;UAACgB,IAAI,EAAC,GAAG;UAACC,OAAO,eAAET,OAAA,CAACN,QAAQ;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACzCP,OAAA,CAACR,KAAK;UAACgB,IAAI,EAAC,cAAc;UAACC,OAAO,eAAET,OAAA,CAACL,eAAe;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC3DP,OAAA,CAACR,KAAK;UAACgB,IAAI,EAAC,wBAAwB;UAACC,OAAO,eAAET,OAAA,CAACL,eAAe;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACrEP,OAAA,CAACR,KAAK;UAACgB,IAAI,EAAC,UAAU;UAACC,OAAO,eAAET,OAAA,CAACJ,YAAY;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACpDP,OAAA,CAACR,KAAK;UAACgB,IAAI,EAAC,YAAY;UAACC,OAAO,eAAET,OAAA,CAACH,aAAa;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACvDP,OAAA,CAACR,KAAK;UAACgB,IAAI,EAAC,YAAY;UAACC,OAAO,eAAET,OAAA,CAACF,gBAAgB;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAE1DP,OAAA,CAACR,KAAK;UAACgB,IAAI,EAAC,cAAc;UAACC,OAAO,eAAET,OAAA,CAACL,eAAe;YAACe,QAAQ,EAAC;UAAW;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAChFP,OAAA,CAACR,KAAK;UAACgB,IAAI,EAAC,mBAAmB;UAACC,OAAO,eAAET,OAAA,CAACL,eAAe;YAACe,QAAQ,EAAC;UAAc;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACxFP,OAAA,CAACR,KAAK;UAACgB,IAAI,EAAC,mBAAmB;UAACC,OAAO,eAAET,OAAA,CAACL,eAAe;YAACe,QAAQ,EAAC;UAAO;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACjFP,OAAA,CAACR,KAAK;UAACgB,IAAI,EAAC,qBAAqB;UAACC,OAAO,eAAET,OAAA,CAACL,eAAe;YAACe,QAAQ,EAAC;UAAI;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAChFP,OAAA,CAACR,KAAK;UAACgB,IAAI,EAAC,YAAY;UAACC,OAAO,eAAET,OAAA,CAACL,eAAe;YAACe,QAAQ,EAAC;UAAO;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC1EP,OAAA,CAACR,KAAK;UAACgB,IAAI,EAAC,oBAAoB;UAACC,OAAO,eAAET,OAAA,CAACL,eAAe;YAACe,QAAQ,EAAC;UAAS;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACpFP,OAAA,CAACR,KAAK;UAACgB,IAAI,EAAC,iBAAiB;UAACC,OAAO,eAAET,OAAA,CAACL,eAAe;YAACe,QAAQ,EAAC;UAAY;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9E,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEb;AAACI,EAAA,GAxBQV,GAAG;AA0BZ,eAAeA,GAAG;AAAC,IAAAU,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}