{"ast": null, "code": "import React,{useState,useEffect}from'react';import moodle<PERSON><PERSON> from'../services/moodleAPI';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const MoodleIntegration=()=>{const[connectionStatus,setConnectionStatus]=useState('checking');const[apiData,setApiData]=useState(null);const[error,setError]=useState(null);useEffect(()=>{checkMoodleConnection();},[]);const checkMoodleConnection=async()=>{try{setConnectionStatus('checking');const siteInfo=await moodleAPI.getSiteInfo();setApiData(siteInfo);setConnectionStatus('connected');setError(null);}catch(err){setConnectionStatus('error');setError(err.message);}};const testApiCall=async endpoint=>{try{let result;switch(endpoint){case'courses':result=await moodleAPI.getCourses();break;case'users':result=await moodleAPI.getCurrentUser();break;case'site':result=await moodleAPI.getSiteInfo();break;default:result={error:'Unknown endpoint'};}alert(\"API Test Result:\\n\".concat(JSON.stringify(result,null,2)));}catch(err){alert(\"API Test Error:\\n\".concat(err.message));}};return/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-6\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"bg-[#1a0f00] rounded-lg p-6 border border-gray-700\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"text-xl font-semibold mb-4\",children:\"Moodle Connection Status\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-4 h-4 rounded-full mr-3 \".concat(connectionStatus==='connected'?'bg-green-500':connectionStatus==='error'?'bg-red-500':'bg-yellow-500')}),/*#__PURE__*/_jsx(\"span\",{className:\"font-medium\",children:connectionStatus==='connected'?'Connected':connectionStatus==='error'?'Connection Error':'Checking...'})]}),/*#__PURE__*/_jsxs(\"button\",{onClick:checkMoodleConnection,className:\"px-4 py-2 bg-[#15a7dd] rounded hover:bg-[#1397c7] transition-colors duration-300\",children:[/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-sync-alt mr-2\"}),\"Refresh\"]})]}),error&&/*#__PURE__*/_jsx(\"div\",{className:\"mt-4 p-3 bg-red-900/20 border border-red-500/30 rounded\",children:/*#__PURE__*/_jsx(\"p\",{className:\"text-red-400 text-sm\",children:error})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"bg-[#1a0f00] rounded-lg p-6 border border-gray-700\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"text-xl font-semibold mb-4\",children:\"API Configuration\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-1 md:grid-cols-2 gap-4\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium mb-2\",children:\"Moodle URL\"}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",value:process.env.REACT_APP_MOODLE_URL||'',readOnly:true,className:\"w-full px-3 py-2 bg-[#2a1a05] border border-gray-600 rounded text-gray-300\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium mb-2\",children:\"API Endpoint\"}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",value:process.env.REACT_APP_MOODLE_API_URL||'',readOnly:true,className:\"w-full px-3 py-2 bg-[#2a1a05] border border-gray-600 rounded text-gray-300\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium mb-2\",children:\"Token Status\"}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",value:process.env.REACT_APP_MOODLE_TOKEN?'Configured':'Not Set',readOnly:true,className:\"w-full px-3 py-2 bg-[#2a1a05] border border-gray-600 rounded text-gray-300\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium mb-2\",children:\"Connection Type\"}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",value:\"REST API\",readOnly:true,className:\"w-full px-3 py-2 bg-[#2a1a05] border border-gray-600 rounded text-gray-300\"})]})]})]}),apiData&&/*#__PURE__*/_jsxs(\"div\",{className:\"bg-[#1a0f00] rounded-lg p-6 border border-gray-700\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"text-xl font-semibold mb-4\",children:\"Moodle Site Information\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-1 md:grid-cols-2 gap-4\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium mb-2\",children:\"Site Name\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-300\",children:apiData.sitename||'N/A'})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium mb-2\",children:\"Version\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-300\",children:apiData.release||'N/A'})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium mb-2\",children:\"Language\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-300\",children:apiData.lang||'N/A'})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium mb-2\",children:\"User ID\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-300\",children:apiData.userid||'N/A'})]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"bg-[#1a0f00] rounded-lg p-6 border border-gray-700\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"text-xl font-semibold mb-4\",children:\"API Testing\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-400 mb-4\",children:\"Test various Moodle API endpoints to ensure proper integration.\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-1 md:grid-cols-3 gap-4\",children:[/*#__PURE__*/_jsxs(\"button\",{onClick:()=>testApiCall('site'),className:\"p-3 bg-blue-600 rounded hover:bg-blue-700 transition-colors duration-300\",children:[/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-info-circle mr-2\"}),\"Test Site Info\"]}),/*#__PURE__*/_jsxs(\"button\",{onClick:()=>testApiCall('courses'),className:\"p-3 bg-green-600 rounded hover:bg-green-700 transition-colors duration-300\",children:[/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-book mr-2\"}),\"Test Courses\"]}),/*#__PURE__*/_jsxs(\"button\",{onClick:()=>testApiCall('users'),className:\"p-3 bg-purple-600 rounded hover:bg-purple-700 transition-colors duration-300\",children:[/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-user mr-2\"}),\"Test User Info\"]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"bg-[#1a0f00] rounded-lg p-6 border border-gray-700\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"text-xl font-semibold mb-4\",children:\"Integration Features\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-1 md:grid-cols-2 gap-6\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h4\",{className:\"font-semibold mb-2 text-[#15a7dd]\",children:\"Implemented Features\"}),/*#__PURE__*/_jsxs(\"ul\",{className:\"space-y-2 text-sm\",children:[/*#__PURE__*/_jsxs(\"li\",{className:\"flex items-center\",children:[/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-check text-green-500 mr-2\"}),\"Single Sign-On (SSO) Integration\"]}),/*#__PURE__*/_jsxs(\"li\",{className:\"flex items-center\",children:[/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-check text-green-500 mr-2\"}),\"Course Data Synchronization\"]}),/*#__PURE__*/_jsxs(\"li\",{className:\"flex items-center\",children:[/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-check text-green-500 mr-2\"}),\"User Progress Tracking\"]}),/*#__PURE__*/_jsxs(\"li\",{className:\"flex items-center\",children:[/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-check text-green-500 mr-2\"}),\"Unified Navigation\"]})]})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h4\",{className:\"font-semibold mb-2 text-yellow-500\",children:\"Planned Features\"}),/*#__PURE__*/_jsxs(\"ul\",{className:\"space-y-2 text-sm\",children:[/*#__PURE__*/_jsxs(\"li\",{className:\"flex items-center\",children:[/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-clock text-yellow-500 mr-2\"}),\"Real-time Notifications\"]}),/*#__PURE__*/_jsxs(\"li\",{className:\"flex items-center\",children:[/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-clock text-yellow-500 mr-2\"}),\"Grade Synchronization\"]}),/*#__PURE__*/_jsxs(\"li\",{className:\"flex items-center\",children:[/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-clock text-yellow-500 mr-2\"}),\"Assignment Submission\"]}),/*#__PURE__*/_jsxs(\"li\",{className:\"flex items-center\",children:[/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-clock text-yellow-500 mr-2\"}),\"Discussion Forum Integration\"]})]})]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"bg-[#1a0f00] rounded-lg p-6 border border-gray-700\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"text-xl font-semibold mb-4\",children:\"Documentation & Resources\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-1 md:grid-cols-2 gap-4\",children:[/*#__PURE__*/_jsxs(\"a\",{href:\"https://docs.moodle.org/dev/Web_service_API_functions\",target:\"_blank\",rel:\"noopener noreferrer\",className:\"flex items-center p-3 bg-[#2a1a05] rounded hover:bg-[#3a2a15] transition-colors duration-300\",children:[/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-external-link-alt mr-3 text-[#15a7dd]\"}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h4\",{className:\"font-semibold\",children:\"Moodle Web Services API\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-sm text-gray-400\",children:\"Official API documentation\"})]})]}),/*#__PURE__*/_jsxs(\"a\",{href:\"https://docs.moodle.org/dev/Creating_a_web_service_client\",target:\"_blank\",rel:\"noopener noreferrer\",className:\"flex items-center p-3 bg-[#2a1a05] rounded hover:bg-[#3a2a15] transition-colors duration-300\",children:[/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-external-link-alt mr-3 text-[#15a7dd]\"}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h4\",{className:\"font-semibold\",children:\"Web Service Client Guide\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-sm text-gray-400\",children:\"Integration best practices\"})]})]})]})]})]});};export default MoodleIntegration;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "moodleAPI", "jsx", "_jsx", "jsxs", "_jsxs", "MoodleIntegration", "connectionStatus", "setConnectionStatus", "apiData", "setApiData", "error", "setError", "checkMoodleConnection", "siteInfo", "getSiteInfo", "err", "message", "testApiCall", "endpoint", "result", "getCourses", "getCurrentUser", "alert", "concat", "JSON", "stringify", "className", "children", "onClick", "type", "value", "process", "env", "REACT_APP_MOODLE_URL", "readOnly", "REACT_APP_MOODLE_API_URL", "REACT_APP_MOODLE_TOKEN", "sitename", "release", "lang", "userid", "href", "target", "rel"], "sources": ["/Users/<USER>/Documents/FatbeamU/react-app/src/components/MoodleIntegration.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport moodle<PERSON><PERSON> from '../services/moodleAPI';\n\nconst MoodleIntegration = () => {\n  const [connectionStatus, setConnectionStatus] = useState('checking');\n  const [apiData, setApiData] = useState(null);\n  const [error, setError] = useState(null);\n\n  useEffect(() => {\n    checkMoodleConnection();\n  }, []);\n\n  const checkMoodleConnection = async () => {\n    try {\n      setConnectionStatus('checking');\n      const siteInfo = await moodleAPI.getSiteInfo();\n      setApiData(siteInfo);\n      setConnectionStatus('connected');\n      setError(null);\n    } catch (err) {\n      setConnectionStatus('error');\n      setError(err.message);\n    }\n  };\n\n  const testApiCall = async (endpoint) => {\n    try {\n      let result;\n      switch (endpoint) {\n        case 'courses':\n          result = await moodleAPI.getCourses();\n          break;\n        case 'users':\n          result = await moodleAPI.getCurrentUser();\n          break;\n        case 'site':\n          result = await moodleAPI.getSiteInfo();\n          break;\n        default:\n          result = { error: 'Unknown endpoint' };\n      }\n      alert(`API Test Result:\\n${JSON.stringify(result, null, 2)}`);\n    } catch (err) {\n      alert(`API Test Error:\\n${err.message}`);\n    }\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Connection Status */}\n      <div className=\"bg-[#1a0f00] rounded-lg p-6 border border-gray-700\">\n        <h3 className=\"text-xl font-semibold mb-4\">Moodle Connection Status</h3>\n        <div className=\"flex items-center justify-between\">\n          <div className=\"flex items-center\">\n            <div className={`w-4 h-4 rounded-full mr-3 ${\n              connectionStatus === 'connected' ? 'bg-green-500' :\n              connectionStatus === 'error' ? 'bg-red-500' : 'bg-yellow-500'\n            }`}></div>\n            <span className=\"font-medium\">\n              {connectionStatus === 'connected' ? 'Connected' :\n               connectionStatus === 'error' ? 'Connection Error' : 'Checking...'}\n            </span>\n          </div>\n          <button\n            onClick={checkMoodleConnection}\n            className=\"px-4 py-2 bg-[#15a7dd] rounded hover:bg-[#1397c7] transition-colors duration-300\"\n          >\n            <i className=\"fas fa-sync-alt mr-2\"></i>\n            Refresh\n          </button>\n        </div>\n        \n        {error && (\n          <div className=\"mt-4 p-3 bg-red-900/20 border border-red-500/30 rounded\">\n            <p className=\"text-red-400 text-sm\">{error}</p>\n          </div>\n        )}\n      </div>\n\n      {/* API Configuration */}\n      <div className=\"bg-[#1a0f00] rounded-lg p-6 border border-gray-700\">\n        <h3 className=\"text-xl font-semibold mb-4\">API Configuration</h3>\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n          <div>\n            <label className=\"block text-sm font-medium mb-2\">Moodle URL</label>\n            <input\n              type=\"text\"\n              value={process.env.REACT_APP_MOODLE_URL || ''}\n              readOnly\n              className=\"w-full px-3 py-2 bg-[#2a1a05] border border-gray-600 rounded text-gray-300\"\n            />\n          </div>\n          <div>\n            <label className=\"block text-sm font-medium mb-2\">API Endpoint</label>\n            <input\n              type=\"text\"\n              value={process.env.REACT_APP_MOODLE_API_URL || ''}\n              readOnly\n              className=\"w-full px-3 py-2 bg-[#2a1a05] border border-gray-600 rounded text-gray-300\"\n            />\n          </div>\n          <div>\n            <label className=\"block text-sm font-medium mb-2\">Token Status</label>\n            <input\n              type=\"text\"\n              value={process.env.REACT_APP_MOODLE_TOKEN ? 'Configured' : 'Not Set'}\n              readOnly\n              className=\"w-full px-3 py-2 bg-[#2a1a05] border border-gray-600 rounded text-gray-300\"\n            />\n          </div>\n          <div>\n            <label className=\"block text-sm font-medium mb-2\">Connection Type</label>\n            <input\n              type=\"text\"\n              value=\"REST API\"\n              readOnly\n              className=\"w-full px-3 py-2 bg-[#2a1a05] border border-gray-600 rounded text-gray-300\"\n            />\n          </div>\n        </div>\n      </div>\n\n      {/* Site Information */}\n      {apiData && (\n        <div className=\"bg-[#1a0f00] rounded-lg p-6 border border-gray-700\">\n          <h3 className=\"text-xl font-semibold mb-4\">Moodle Site Information</h3>\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n            <div>\n              <label className=\"block text-sm font-medium mb-2\">Site Name</label>\n              <p className=\"text-gray-300\">{apiData.sitename || 'N/A'}</p>\n            </div>\n            <div>\n              <label className=\"block text-sm font-medium mb-2\">Version</label>\n              <p className=\"text-gray-300\">{apiData.release || 'N/A'}</p>\n            </div>\n            <div>\n              <label className=\"block text-sm font-medium mb-2\">Language</label>\n              <p className=\"text-gray-300\">{apiData.lang || 'N/A'}</p>\n            </div>\n            <div>\n              <label className=\"block text-sm font-medium mb-2\">User ID</label>\n              <p className=\"text-gray-300\">{apiData.userid || 'N/A'}</p>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* API Testing */}\n      <div className=\"bg-[#1a0f00] rounded-lg p-6 border border-gray-700\">\n        <h3 className=\"text-xl font-semibold mb-4\">API Testing</h3>\n        <p className=\"text-gray-400 mb-4\">Test various Moodle API endpoints to ensure proper integration.</p>\n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n          <button\n            onClick={() => testApiCall('site')}\n            className=\"p-3 bg-blue-600 rounded hover:bg-blue-700 transition-colors duration-300\"\n          >\n            <i className=\"fas fa-info-circle mr-2\"></i>\n            Test Site Info\n          </button>\n          <button\n            onClick={() => testApiCall('courses')}\n            className=\"p-3 bg-green-600 rounded hover:bg-green-700 transition-colors duration-300\"\n          >\n            <i className=\"fas fa-book mr-2\"></i>\n            Test Courses\n          </button>\n          <button\n            onClick={() => testApiCall('users')}\n            className=\"p-3 bg-purple-600 rounded hover:bg-purple-700 transition-colors duration-300\"\n          >\n            <i className=\"fas fa-user mr-2\"></i>\n            Test User Info\n          </button>\n        </div>\n      </div>\n\n      {/* Integration Features */}\n      <div className=\"bg-[#1a0f00] rounded-lg p-6 border border-gray-700\">\n        <h3 className=\"text-xl font-semibold mb-4\">Integration Features</h3>\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n          <div>\n            <h4 className=\"font-semibold mb-2 text-[#15a7dd]\">Implemented Features</h4>\n            <ul className=\"space-y-2 text-sm\">\n              <li className=\"flex items-center\">\n                <i className=\"fas fa-check text-green-500 mr-2\"></i>\n                Single Sign-On (SSO) Integration\n              </li>\n              <li className=\"flex items-center\">\n                <i className=\"fas fa-check text-green-500 mr-2\"></i>\n                Course Data Synchronization\n              </li>\n              <li className=\"flex items-center\">\n                <i className=\"fas fa-check text-green-500 mr-2\"></i>\n                User Progress Tracking\n              </li>\n              <li className=\"flex items-center\">\n                <i className=\"fas fa-check text-green-500 mr-2\"></i>\n                Unified Navigation\n              </li>\n            </ul>\n          </div>\n          <div>\n            <h4 className=\"font-semibold mb-2 text-yellow-500\">Planned Features</h4>\n            <ul className=\"space-y-2 text-sm\">\n              <li className=\"flex items-center\">\n                <i className=\"fas fa-clock text-yellow-500 mr-2\"></i>\n                Real-time Notifications\n              </li>\n              <li className=\"flex items-center\">\n                <i className=\"fas fa-clock text-yellow-500 mr-2\"></i>\n                Grade Synchronization\n              </li>\n              <li className=\"flex items-center\">\n                <i className=\"fas fa-clock text-yellow-500 mr-2\"></i>\n                Assignment Submission\n              </li>\n              <li className=\"flex items-center\">\n                <i className=\"fas fa-clock text-yellow-500 mr-2\"></i>\n                Discussion Forum Integration\n              </li>\n            </ul>\n          </div>\n        </div>\n      </div>\n\n      {/* Documentation Links */}\n      <div className=\"bg-[#1a0f00] rounded-lg p-6 border border-gray-700\">\n        <h3 className=\"text-xl font-semibold mb-4\">Documentation & Resources</h3>\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n          <a\n            href=\"https://docs.moodle.org/dev/Web_service_API_functions\"\n            target=\"_blank\"\n            rel=\"noopener noreferrer\"\n            className=\"flex items-center p-3 bg-[#2a1a05] rounded hover:bg-[#3a2a15] transition-colors duration-300\"\n          >\n            <i className=\"fas fa-external-link-alt mr-3 text-[#15a7dd]\"></i>\n            <div>\n              <h4 className=\"font-semibold\">Moodle Web Services API</h4>\n              <p className=\"text-sm text-gray-400\">Official API documentation</p>\n            </div>\n          </a>\n          <a\n            href=\"https://docs.moodle.org/dev/Creating_a_web_service_client\"\n            target=\"_blank\"\n            rel=\"noopener noreferrer\"\n            className=\"flex items-center p-3 bg-[#2a1a05] rounded hover:bg-[#3a2a15] transition-colors duration-300\"\n          >\n            <i className=\"fas fa-external-link-alt mr-3 text-[#15a7dd]\"></i>\n            <div>\n              <h4 className=\"font-semibold\">Web Service Client Guide</h4>\n              <p className=\"text-sm text-gray-400\">Integration best practices</p>\n            </div>\n          </a>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default MoodleIntegration;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,MAAO,CAAAC,SAAS,KAAM,uBAAuB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE9C,KAAM,CAAAC,iBAAiB,CAAGA,CAAA,GAAM,CAC9B,KAAM,CAACC,gBAAgB,CAAEC,mBAAmB,CAAC,CAAGT,QAAQ,CAAC,UAAU,CAAC,CACpE,KAAM,CAACU,OAAO,CAAEC,UAAU,CAAC,CAAGX,QAAQ,CAAC,IAAI,CAAC,CAC5C,KAAM,CAACY,KAAK,CAAEC,QAAQ,CAAC,CAAGb,QAAQ,CAAC,IAAI,CAAC,CAExCC,SAAS,CAAC,IAAM,CACda,qBAAqB,CAAC,CAAC,CACzB,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAA,qBAAqB,CAAG,KAAAA,CAAA,GAAY,CACxC,GAAI,CACFL,mBAAmB,CAAC,UAAU,CAAC,CAC/B,KAAM,CAAAM,QAAQ,CAAG,KAAM,CAAAb,SAAS,CAACc,WAAW,CAAC,CAAC,CAC9CL,UAAU,CAACI,QAAQ,CAAC,CACpBN,mBAAmB,CAAC,WAAW,CAAC,CAChCI,QAAQ,CAAC,IAAI,CAAC,CAChB,CAAE,MAAOI,GAAG,CAAE,CACZR,mBAAmB,CAAC,OAAO,CAAC,CAC5BI,QAAQ,CAACI,GAAG,CAACC,OAAO,CAAC,CACvB,CACF,CAAC,CAED,KAAM,CAAAC,WAAW,CAAG,KAAO,CAAAC,QAAQ,EAAK,CACtC,GAAI,CACF,GAAI,CAAAC,MAAM,CACV,OAAQD,QAAQ,EACd,IAAK,SAAS,CACZC,MAAM,CAAG,KAAM,CAAAnB,SAAS,CAACoB,UAAU,CAAC,CAAC,CACrC,MACF,IAAK,OAAO,CACVD,MAAM,CAAG,KAAM,CAAAnB,SAAS,CAACqB,cAAc,CAAC,CAAC,CACzC,MACF,IAAK,MAAM,CACTF,MAAM,CAAG,KAAM,CAAAnB,SAAS,CAACc,WAAW,CAAC,CAAC,CACtC,MACF,QACEK,MAAM,CAAG,CAAET,KAAK,CAAE,kBAAmB,CAAC,CAC1C,CACAY,KAAK,sBAAAC,MAAA,CAAsBC,IAAI,CAACC,SAAS,CAACN,MAAM,CAAE,IAAI,CAAE,CAAC,CAAC,CAAE,CAAC,CAC/D,CAAE,MAAOJ,GAAG,CAAE,CACZO,KAAK,qBAAAC,MAAA,CAAqBR,GAAG,CAACC,OAAO,CAAE,CAAC,CAC1C,CACF,CAAC,CAED,mBACEZ,KAAA,QAAKsB,SAAS,CAAC,WAAW,CAAAC,QAAA,eAExBvB,KAAA,QAAKsB,SAAS,CAAC,oDAAoD,CAAAC,QAAA,eACjEzB,IAAA,OAAIwB,SAAS,CAAC,4BAA4B,CAAAC,QAAA,CAAC,0BAAwB,CAAI,CAAC,cACxEvB,KAAA,QAAKsB,SAAS,CAAC,mCAAmC,CAAAC,QAAA,eAChDvB,KAAA,QAAKsB,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChCzB,IAAA,QAAKwB,SAAS,8BAAAH,MAAA,CACZjB,gBAAgB,GAAK,WAAW,CAAG,cAAc,CACjDA,gBAAgB,GAAK,OAAO,CAAG,YAAY,CAAG,eAAe,CAC5D,CAAM,CAAC,cACVJ,IAAA,SAAMwB,SAAS,CAAC,aAAa,CAAAC,QAAA,CAC1BrB,gBAAgB,GAAK,WAAW,CAAG,WAAW,CAC9CA,gBAAgB,GAAK,OAAO,CAAG,kBAAkB,CAAG,aAAa,CAC9D,CAAC,EACJ,CAAC,cACNF,KAAA,WACEwB,OAAO,CAAEhB,qBAAsB,CAC/Bc,SAAS,CAAC,kFAAkF,CAAAC,QAAA,eAE5FzB,IAAA,MAAGwB,SAAS,CAAC,sBAAsB,CAAI,CAAC,UAE1C,EAAQ,CAAC,EACN,CAAC,CAELhB,KAAK,eACJR,IAAA,QAAKwB,SAAS,CAAC,yDAAyD,CAAAC,QAAA,cACtEzB,IAAA,MAAGwB,SAAS,CAAC,sBAAsB,CAAAC,QAAA,CAAEjB,KAAK,CAAI,CAAC,CAC5C,CACN,EACE,CAAC,cAGNN,KAAA,QAAKsB,SAAS,CAAC,oDAAoD,CAAAC,QAAA,eACjEzB,IAAA,OAAIwB,SAAS,CAAC,4BAA4B,CAAAC,QAAA,CAAC,mBAAiB,CAAI,CAAC,cACjEvB,KAAA,QAAKsB,SAAS,CAAC,uCAAuC,CAAAC,QAAA,eACpDvB,KAAA,QAAAuB,QAAA,eACEzB,IAAA,UAAOwB,SAAS,CAAC,gCAAgC,CAAAC,QAAA,CAAC,YAAU,CAAO,CAAC,cACpEzB,IAAA,UACE2B,IAAI,CAAC,MAAM,CACXC,KAAK,CAAEC,OAAO,CAACC,GAAG,CAACC,oBAAoB,EAAI,EAAG,CAC9CC,QAAQ,MACRR,SAAS,CAAC,4EAA4E,CACvF,CAAC,EACC,CAAC,cACNtB,KAAA,QAAAuB,QAAA,eACEzB,IAAA,UAAOwB,SAAS,CAAC,gCAAgC,CAAAC,QAAA,CAAC,cAAY,CAAO,CAAC,cACtEzB,IAAA,UACE2B,IAAI,CAAC,MAAM,CACXC,KAAK,CAAEC,OAAO,CAACC,GAAG,CAACG,wBAAwB,EAAI,EAAG,CAClDD,QAAQ,MACRR,SAAS,CAAC,4EAA4E,CACvF,CAAC,EACC,CAAC,cACNtB,KAAA,QAAAuB,QAAA,eACEzB,IAAA,UAAOwB,SAAS,CAAC,gCAAgC,CAAAC,QAAA,CAAC,cAAY,CAAO,CAAC,cACtEzB,IAAA,UACE2B,IAAI,CAAC,MAAM,CACXC,KAAK,CAAEC,OAAO,CAACC,GAAG,CAACI,sBAAsB,CAAG,YAAY,CAAG,SAAU,CACrEF,QAAQ,MACRR,SAAS,CAAC,4EAA4E,CACvF,CAAC,EACC,CAAC,cACNtB,KAAA,QAAAuB,QAAA,eACEzB,IAAA,UAAOwB,SAAS,CAAC,gCAAgC,CAAAC,QAAA,CAAC,iBAAe,CAAO,CAAC,cACzEzB,IAAA,UACE2B,IAAI,CAAC,MAAM,CACXC,KAAK,CAAC,UAAU,CAChBI,QAAQ,MACRR,SAAS,CAAC,4EAA4E,CACvF,CAAC,EACC,CAAC,EACH,CAAC,EACH,CAAC,CAGLlB,OAAO,eACNJ,KAAA,QAAKsB,SAAS,CAAC,oDAAoD,CAAAC,QAAA,eACjEzB,IAAA,OAAIwB,SAAS,CAAC,4BAA4B,CAAAC,QAAA,CAAC,yBAAuB,CAAI,CAAC,cACvEvB,KAAA,QAAKsB,SAAS,CAAC,uCAAuC,CAAAC,QAAA,eACpDvB,KAAA,QAAAuB,QAAA,eACEzB,IAAA,UAAOwB,SAAS,CAAC,gCAAgC,CAAAC,QAAA,CAAC,WAAS,CAAO,CAAC,cACnEzB,IAAA,MAAGwB,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAEnB,OAAO,CAAC6B,QAAQ,EAAI,KAAK,CAAI,CAAC,EACzD,CAAC,cACNjC,KAAA,QAAAuB,QAAA,eACEzB,IAAA,UAAOwB,SAAS,CAAC,gCAAgC,CAAAC,QAAA,CAAC,SAAO,CAAO,CAAC,cACjEzB,IAAA,MAAGwB,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAEnB,OAAO,CAAC8B,OAAO,EAAI,KAAK,CAAI,CAAC,EACxD,CAAC,cACNlC,KAAA,QAAAuB,QAAA,eACEzB,IAAA,UAAOwB,SAAS,CAAC,gCAAgC,CAAAC,QAAA,CAAC,UAAQ,CAAO,CAAC,cAClEzB,IAAA,MAAGwB,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAEnB,OAAO,CAAC+B,IAAI,EAAI,KAAK,CAAI,CAAC,EACrD,CAAC,cACNnC,KAAA,QAAAuB,QAAA,eACEzB,IAAA,UAAOwB,SAAS,CAAC,gCAAgC,CAAAC,QAAA,CAAC,SAAO,CAAO,CAAC,cACjEzB,IAAA,MAAGwB,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAEnB,OAAO,CAACgC,MAAM,EAAI,KAAK,CAAI,CAAC,EACvD,CAAC,EACH,CAAC,EACH,CACN,cAGDpC,KAAA,QAAKsB,SAAS,CAAC,oDAAoD,CAAAC,QAAA,eACjEzB,IAAA,OAAIwB,SAAS,CAAC,4BAA4B,CAAAC,QAAA,CAAC,aAAW,CAAI,CAAC,cAC3DzB,IAAA,MAAGwB,SAAS,CAAC,oBAAoB,CAAAC,QAAA,CAAC,iEAA+D,CAAG,CAAC,cACrGvB,KAAA,QAAKsB,SAAS,CAAC,uCAAuC,CAAAC,QAAA,eACpDvB,KAAA,WACEwB,OAAO,CAAEA,CAAA,GAAMX,WAAW,CAAC,MAAM,CAAE,CACnCS,SAAS,CAAC,0EAA0E,CAAAC,QAAA,eAEpFzB,IAAA,MAAGwB,SAAS,CAAC,yBAAyB,CAAI,CAAC,iBAE7C,EAAQ,CAAC,cACTtB,KAAA,WACEwB,OAAO,CAAEA,CAAA,GAAMX,WAAW,CAAC,SAAS,CAAE,CACtCS,SAAS,CAAC,4EAA4E,CAAAC,QAAA,eAEtFzB,IAAA,MAAGwB,SAAS,CAAC,kBAAkB,CAAI,CAAC,eAEtC,EAAQ,CAAC,cACTtB,KAAA,WACEwB,OAAO,CAAEA,CAAA,GAAMX,WAAW,CAAC,OAAO,CAAE,CACpCS,SAAS,CAAC,8EAA8E,CAAAC,QAAA,eAExFzB,IAAA,MAAGwB,SAAS,CAAC,kBAAkB,CAAI,CAAC,iBAEtC,EAAQ,CAAC,EACN,CAAC,EACH,CAAC,cAGNtB,KAAA,QAAKsB,SAAS,CAAC,oDAAoD,CAAAC,QAAA,eACjEzB,IAAA,OAAIwB,SAAS,CAAC,4BAA4B,CAAAC,QAAA,CAAC,sBAAoB,CAAI,CAAC,cACpEvB,KAAA,QAAKsB,SAAS,CAAC,uCAAuC,CAAAC,QAAA,eACpDvB,KAAA,QAAAuB,QAAA,eACEzB,IAAA,OAAIwB,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CAAC,sBAAoB,CAAI,CAAC,cAC3EvB,KAAA,OAAIsB,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAC/BvB,KAAA,OAAIsB,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAC/BzB,IAAA,MAAGwB,SAAS,CAAC,kCAAkC,CAAI,CAAC,mCAEtD,EAAI,CAAC,cACLtB,KAAA,OAAIsB,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAC/BzB,IAAA,MAAGwB,SAAS,CAAC,kCAAkC,CAAI,CAAC,8BAEtD,EAAI,CAAC,cACLtB,KAAA,OAAIsB,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAC/BzB,IAAA,MAAGwB,SAAS,CAAC,kCAAkC,CAAI,CAAC,yBAEtD,EAAI,CAAC,cACLtB,KAAA,OAAIsB,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAC/BzB,IAAA,MAAGwB,SAAS,CAAC,kCAAkC,CAAI,CAAC,qBAEtD,EAAI,CAAC,EACH,CAAC,EACF,CAAC,cACNtB,KAAA,QAAAuB,QAAA,eACEzB,IAAA,OAAIwB,SAAS,CAAC,oCAAoC,CAAAC,QAAA,CAAC,kBAAgB,CAAI,CAAC,cACxEvB,KAAA,OAAIsB,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAC/BvB,KAAA,OAAIsB,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAC/BzB,IAAA,MAAGwB,SAAS,CAAC,mCAAmC,CAAI,CAAC,0BAEvD,EAAI,CAAC,cACLtB,KAAA,OAAIsB,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAC/BzB,IAAA,MAAGwB,SAAS,CAAC,mCAAmC,CAAI,CAAC,wBAEvD,EAAI,CAAC,cACLtB,KAAA,OAAIsB,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAC/BzB,IAAA,MAAGwB,SAAS,CAAC,mCAAmC,CAAI,CAAC,wBAEvD,EAAI,CAAC,cACLtB,KAAA,OAAIsB,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAC/BzB,IAAA,MAAGwB,SAAS,CAAC,mCAAmC,CAAI,CAAC,+BAEvD,EAAI,CAAC,EACH,CAAC,EACF,CAAC,EACH,CAAC,EACH,CAAC,cAGNtB,KAAA,QAAKsB,SAAS,CAAC,oDAAoD,CAAAC,QAAA,eACjEzB,IAAA,OAAIwB,SAAS,CAAC,4BAA4B,CAAAC,QAAA,CAAC,2BAAyB,CAAI,CAAC,cACzEvB,KAAA,QAAKsB,SAAS,CAAC,uCAAuC,CAAAC,QAAA,eACpDvB,KAAA,MACEqC,IAAI,CAAC,uDAAuD,CAC5DC,MAAM,CAAC,QAAQ,CACfC,GAAG,CAAC,qBAAqB,CACzBjB,SAAS,CAAC,8FAA8F,CAAAC,QAAA,eAExGzB,IAAA,MAAGwB,SAAS,CAAC,8CAA8C,CAAI,CAAC,cAChEtB,KAAA,QAAAuB,QAAA,eACEzB,IAAA,OAAIwB,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,yBAAuB,CAAI,CAAC,cAC1DzB,IAAA,MAAGwB,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,4BAA0B,CAAG,CAAC,EAChE,CAAC,EACL,CAAC,cACJvB,KAAA,MACEqC,IAAI,CAAC,2DAA2D,CAChEC,MAAM,CAAC,QAAQ,CACfC,GAAG,CAAC,qBAAqB,CACzBjB,SAAS,CAAC,8FAA8F,CAAAC,QAAA,eAExGzB,IAAA,MAAGwB,SAAS,CAAC,8CAA8C,CAAI,CAAC,cAChEtB,KAAA,QAAAuB,QAAA,eACEzB,IAAA,OAAIwB,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,0BAAwB,CAAI,CAAC,cAC3DzB,IAAA,MAAGwB,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,4BAA0B,CAAG,CAAC,EAChE,CAAC,EACL,CAAC,EACD,CAAC,EACH,CAAC,EACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAAtB,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}