{"ast": null, "code": "// Converted from ClassroomSplash-Page.tsx - The exported code uses Tailwind CSS\nimport React,{useState,useEffect,useRef}from'react';import{Link}from'react-router-dom';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const ClassroomPage=_ref=>{let{user,moodleData}=_ref;const[sideMenuOpen,setSideMenuOpen]=useState(false);const[activeTab,setActiveTab]=useState('dashboard');const[hoverCourse,setHoverCourse]=useState(null);const previewPlayerRef=useRef(null);const progressChartRef=useRef(null);useEffect(()=>{// Simplified chart implementation for now\nif(progressChartRef.current){// Chart initialization would go here\n// For now, we'll use a simple implementation\n}},[]);const toggleSideMenu=()=>{setSideMenuOpen(!sideMenuOpen);};const handleCourseHover=index=>{setHoverCourse(index);if(previewPlayerRef.current&&index!==null){previewPlayerRef.current.play().catch(e=>console.log(\"Autoplay prevented:\",e));}};const featuredCourses=[{id:1,title:\"Advanced Network Enchantments\",instructor:\"Professor Elara Waveweaver\",progress:65,image:\"https://readdy.ai/api/search-image?query=Magical%2520network%2520operations%2520center%2520with%2520glowing%2520blue%2520and%2520purple%2520holographic%2520displays%2520showing%2520network%2520traffic%2520visualizations%252C%2520staffed%2520by%2520professionals%2520in%2520tech-inspired%2520robes%252C%2520with%2520floating%2520data%2520streams%2520and%2520ethereal%2520connections%2520between%2520server%2520racks%252C%2520high%2520quality%2520digital%2520art&width=400&height=225&seq=course1&orientation=landscape\",isSeminar:false,description:\"Master the art of network enchantment with advanced techniques for optimizing magical data flows.\"},{id:2,title:\"Packet Manipulation Arts\",instructor:\"Dr. Thorne Cablemancer\",progress:30,image:\"https://readdy.ai/api/search-image?query=Ethereal%2520hands%2520manipulating%2520glowing%2520network%2520packets%2520floating%2520in%2520air%252C%2520with%2520streams%2520of%2520data%2520flowing%2520between%2520crystalline%2520nodes%252C%2520against%2520a%2520backdrop%2520of%2520magical%2520server%2520infrastructure%2520with%2520blue%2520and%2520purple%2520lighting%252C%2520high%2520quality%2520digital%2520art&width=400&height=225&seq=course2&orientation=landscape\",isSeminar:false,description:\"Learn to shape and direct network packets with precision and efficiency.\"},{id:3,title:\"Network Defense Against Dark Arts\",instructor:\"Master Orion Signalkeeper\",progress:0,image:\"https://readdy.ai/api/search-image?query=Magical%2520shield%2520protecting%2520network%2520infrastructure%2520from%2520shadowy%2520attacks%252C%2520with%2520glowing%2520blue%2520defensive%2520runes%2520and%2520sigils%252C%2520ethereal%2520guardians%2520monitoring%2520for%2520intrusions%252C%2520dark%2520background%2520with%2520contrasting%2520magical%2520light%2520effects%252C%2520high%2520quality%2520digital%2520art&width=400&height=225&seq=course3&orientation=landscape\",isSeminar:false,description:\"Protect your networks from malicious attacks with advanced defensive techniques.\"},{id:4,title:\"Live Seminar: Future of Magical Networks\",instructor:\"Professor Iris Dataweave\",date:\"May 10, 2025 • 2:00 PM\",image:\"https://readdy.ai/api/search-image?query=Futuristic%2520magical%2520auditorium%2520with%2520a%2520presenter%2520on%2520stage%2520demonstrating%2520advanced%2520network%2520concepts%2520with%2520floating%2520holographic%2520displays%252C%2520audience%2520of%2520diverse%2520students%2520in%2520tech-magical%2520attire%252C%2520glowing%2520blue%2520and%2520purple%2520lighting%252C%2520high%2520quality%2520digital%2520art&width=400&height=225&seq=seminar1&orientation=landscape\",isSeminar:true,seats:\"43/100 seats available\",description:\"Join us for a live discussion on emerging technologies and future trends in magical networking.\"}];return/*#__PURE__*/_jsxs(\"div\",{className:\"min-h-screen bg-gray-900 text-white font-sans\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"fixed top-0 left-0 h-full w-80 bg-indigo-900 z-50 transform transition-transform duration-300 ease-in-out shadow-2xl \".concat(sideMenuOpen?'translate-x-0':'-translate-x-full'),children:/*#__PURE__*/_jsxs(\"div\",{className:\"p-6\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex justify-between items-center mb-8\",children:[/*#__PURE__*/_jsx(\"h2\",{className:\"text-2xl font-bold text-white\",children:\"Student Dashboard\"}),/*#__PURE__*/_jsx(\"button\",{onClick:toggleSideMenu,className:\"text-white hover:text-blue-300 cursor-pointer\",children:/*#__PURE__*/_jsx(\"i\",{className:\"fa-solid fa-times text-xl\"})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center mb-8 border-b border-indigo-700 pb-6\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"h-16 w-16 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center text-2xl font-bold\",children:\"SW\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"ml-4\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg font-semibold\",children:\"Student Wizard\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-blue-300\",children:\"Network Operations\"})]})]}),/*#__PURE__*/_jsx(\"nav\",{className:\"mb-8\",children:/*#__PURE__*/_jsxs(\"ul\",{className:\"space-y-2\",children:[/*#__PURE__*/_jsx(\"li\",{children:/*#__PURE__*/_jsxs(\"button\",{onClick:()=>setActiveTab('dashboard'),className:\"w-full text-left px-4 py-3 rounded-lg flex items-center \".concat(activeTab==='dashboard'?'bg-indigo-800 text-white':'text-blue-200 hover:bg-indigo-800'),children:[/*#__PURE__*/_jsx(\"i\",{className:\"fa-solid fa-gauge-high mr-3\"}),\"Dashboard\"]})}),/*#__PURE__*/_jsx(\"li\",{children:/*#__PURE__*/_jsxs(\"button\",{onClick:()=>setActiveTab('courses'),className:\"w-full text-left px-4 py-3 rounded-lg flex items-center \".concat(activeTab==='courses'?'bg-indigo-800 text-white':'text-blue-200 hover:bg-indigo-800'),children:[/*#__PURE__*/_jsx(\"i\",{className:\"fa-solid fa-graduation-cap mr-3\"}),\"My Courses\"]})}),/*#__PURE__*/_jsx(\"li\",{children:/*#__PURE__*/_jsxs(\"button\",{onClick:()=>setActiveTab('schedule'),className:\"w-full text-left px-4 py-3 rounded-lg flex items-center \".concat(activeTab==='schedule'?'bg-indigo-800 text-white':'text-blue-200 hover:bg-indigo-800'),children:[/*#__PURE__*/_jsx(\"i\",{className:\"fa-solid fa-calendar-alt mr-3\"}),\"Schedule\"]})})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"border-t border-indigo-700 pt-6\",children:[/*#__PURE__*/_jsx(\"h4\",{className:\"text-lg font-semibold mb-4\",children:\"Course Progress\"}),/*#__PURE__*/_jsx(\"div\",{ref:progressChartRef,className:\"h-48 w-full bg-gray-800 rounded-lg flex items-center justify-center\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"text-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-4xl font-bold text-blue-400 mb-2\",children:\"65%\"}),/*#__PURE__*/_jsx(\"div\",{className:\"text-sm text-gray-400\",children:\"Overall Progress\"})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"mt-4 grid grid-cols-3 gap-2 text-center text-sm\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"div\",{className:\"font-semibold\",children:\"Completed\"}),/*#__PURE__*/_jsx(\"div\",{className:\"text-blue-300\",children:\"35%\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"div\",{className:\"font-semibold\",children:\"In Progress\"}),/*#__PURE__*/_jsx(\"div\",{className:\"text-blue-300\",children:\"20%\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"div\",{className:\"font-semibold\",children:\"Not Started\"}),/*#__PURE__*/_jsx(\"div\",{className:\"text-blue-300\",children:\"45%\"})]})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"mt-8\",children:/*#__PURE__*/_jsxs(Link,{to:\"/departments\",className:\"block w-full px-4 py-3 bg-gradient-to-r from-purple-600 to-indigo-600 text-white rounded-lg text-center hover:from-purple-700 hover:to-indigo-700 transition-all duration-300 font-medium shadow-md\",children:[/*#__PURE__*/_jsx(\"i\",{className:\"fa-solid fa-arrow-left mr-2\"}),\"Back to Department\"]})})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"min-h-screen relative\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"absolute inset-0 z-0\",children:/*#__PURE__*/_jsx(\"img\",{src:\"https://readdy.ai/api/search-image?query=Abstract%20digital%20network%20visualization%20with%20glowing%20blue%20and%20purple%20nodes%20connected%20by%20light%20streams%2C%20creating%20an%20intricate%20weaving%20pattern%20across%20a%20dark%20background%2C%20modern%20tech%20aesthetic%20with%20depth%20and%20dimension%2C%20professional%20enterprise%20grade%20network%20visualization&width=1920&height=1080&seq=bg1&orientation=landscape\",alt:\"Network Background\",className:\"w-full h-full object-cover opacity-10\"})}),/*#__PURE__*/_jsxs(\"div\",{className:\"relative z-10\",children:[/*#__PURE__*/_jsx(\"header\",{className:\"bg-indigo-900/90 shadow-lg backdrop-blur-sm\",children:/*#__PURE__*/_jsx(\"div\",{className:\"container mx-auto px-6 py-4\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex justify-between items-center\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center\",children:[/*#__PURE__*/_jsx(\"button\",{onClick:toggleSideMenu,className:\"mr-4 text-white hover:text-blue-300 cursor-pointer\",children:/*#__PURE__*/_jsx(\"i\",{className:\"fa-solid fa-bars text-xl\"})}),/*#__PURE__*/_jsx(\"h1\",{className:\"text-2xl font-bold text-white\",children:\"Network Operations Classroom\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-4\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"relative\",children:[/*#__PURE__*/_jsx(\"input\",{type:\"text\",placeholder:\"Search courses...\",className:\"px-4 py-2 bg-indigo-800 bg-opacity-50 rounded-lg text-white placeholder-blue-300 border-none focus:outline-none focus:ring-2 focus:ring-blue-500 w-64 text-sm\"}),/*#__PURE__*/_jsx(\"i\",{className:\"fa-solid fa-search absolute right-3 top-1/2 transform -translate-y-1/2 text-blue-300\"})]}),/*#__PURE__*/_jsxs(\"button\",{className:\"relative text-white hover:text-blue-300\",children:[/*#__PURE__*/_jsx(\"i\",{className:\"fa-solid fa-bell text-xl\"}),/*#__PURE__*/_jsx(\"span\",{className:\"absolute -top-1 -right-1 h-4 w-4 bg-red-500 rounded-full text-xs flex items-center justify-center\",children:\"2\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\"h-10 w-10 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center text-lg font-bold cursor-pointer\",children:\"SW\"})]})]})})}),/*#__PURE__*/_jsxs(\"div\",{className:\"relative h-[70vh] overflow-hidden\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"absolute inset-0 bg-gradient-to-b from-indigo-900/90 to-gray-900/90 backdrop-blur-sm\"}),hoverCourse!==null&&/*#__PURE__*/_jsxs(\"div\",{className:\"absolute inset-0 z-0\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"absolute inset-0 bg-gradient-to-r from-indigo-900 via-indigo-900/80 to-transparent z-10\"}),/*#__PURE__*/_jsx(\"video\",{ref:previewPlayerRef,className:\"w-full h-full object-cover opacity-50\",loop:true,muted:true,playsInline:true,children:/*#__PURE__*/_jsx(\"source\",{src:\"https://assets.mixkit.co/videos/preview/mixkit-digital-network-connection-over-blue-background-97.mp4\",type:\"video/mp4\"})})]}),/*#__PURE__*/_jsx(\"div\",{className:\"absolute inset-0 flex items-center z-10\",children:/*#__PURE__*/_jsx(\"div\",{className:\"container mx-auto px-6\",children:/*#__PURE__*/_jsx(\"div\",{className:\"grid grid-cols-1 md:grid-cols-2 gap-12 items-center\",children:/*#__PURE__*/_jsx(\"div\",{children:hoverCourse!==null?/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(\"div\",{className:\"mb-2 text-blue-400 font-medium\",children:featuredCourses[hoverCourse].isSeminar?'LIVE SEMINAR':'FEATURED COURSE'}),/*#__PURE__*/_jsx(\"h2\",{className:\"text-4xl font-bold mb-4\",children:featuredCourses[hoverCourse].title}),/*#__PURE__*/_jsxs(\"p\",{className:\"text-blue-200 mb-2\",children:[/*#__PURE__*/_jsx(\"i\",{className:\"fa-solid fa-user-tie mr-2\"}),featuredCourses[hoverCourse].instructor]}),featuredCourses[hoverCourse].isSeminar?/*#__PURE__*/_jsxs(\"p\",{className:\"text-blue-200 mb-4\",children:[/*#__PURE__*/_jsx(\"i\",{className:\"fa-solid fa-calendar-day mr-2\"}),featuredCourses[hoverCourse].date]}):/*#__PURE__*/_jsxs(\"div\",{className:\"mb-4\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center mb-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-sm text-blue-200 mr-2\",children:\"Progress:\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-sm text-blue-200\",children:[featuredCourses[hoverCourse].progress,\"%\"]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"w-full bg-gray-700 rounded-full h-2\",children:/*#__PURE__*/_jsx(\"div\",{className:\"bg-gradient-to-r from-blue-500 to-purple-600 h-2 rounded-full\",style:{width:\"\".concat(featuredCourses[hoverCourse].progress,\"%\")}})})]}),/*#__PURE__*/_jsx(\"p\",{className:\"text-lg text-gray-300 mb-6\",children:featuredCourses[hoverCourse].description}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-col sm:flex-row gap-4\",children:[/*#__PURE__*/_jsx(Link,{to:\"/learning\",className:\"px-6 py-3 bg-gradient-to-r from-purple-600 to-blue-600 rounded-lg hover:from-purple-700 hover:to-blue-700 transition-all duration-300 font-medium shadow-lg text-center\",children:featuredCourses[hoverCourse].isSeminar?'Register for Seminar':featuredCourses[hoverCourse].progress>0?'Continue Learning':'Start Course'}),/*#__PURE__*/_jsx(\"button\",{className:\"px-6 py-3 bg-gray-800 rounded-lg hover:bg-gray-700 transition-all duration-300 font-medium\",children:\"View Details\"})]})]}):/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(\"h2\",{className:\"text-4xl font-bold mb-4\",children:\"Welcome to the Classroom\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-lg text-gray-300 mb-6\",children:\"Explore your courses, track your progress, and enhance your network operations skills. Hover over a course below to see more details.\"}),/*#__PURE__*/_jsx(\"button\",{className:\"px-6 py-3 bg-gradient-to-r from-purple-600 to-blue-600 rounded-lg hover:from-purple-700 hover:to-blue-700 transition-all duration-300 font-medium shadow-lg\",children:\"Browse All Courses\"})]})})})})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"container mx-auto px-6 py-12\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"mb-16\",children:[/*#__PURE__*/_jsx(\"h2\",{className:\"text-2xl font-bold mb-6\",children:\"Continue Learning\"}),/*#__PURE__*/_jsx(\"div\",{className:\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",children:featuredCourses.filter(course=>!course.isSeminar).map((course,index)=>/*#__PURE__*/_jsxs(\"div\",{className:\"bg-gray-800 rounded-xl overflow-hidden shadow-lg hover:shadow-blue-500/10 transition-all duration-300 cursor-pointer h-full\",onMouseEnter:()=>handleCourseHover(index),onMouseLeave:()=>handleCourseHover(null),children:[/*#__PURE__*/_jsx(\"div\",{className:\"h-48 overflow-hidden\",children:/*#__PURE__*/_jsx(\"img\",{src:course.image,alt:course.title,className:\"w-full h-full object-cover object-top transform hover:scale-105 transition-transform duration-300\"})}),/*#__PURE__*/_jsxs(\"div\",{className:\"p-6\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"text-xl font-bold mb-2\",children:course.title}),/*#__PURE__*/_jsxs(\"p\",{className:\"text-blue-300 text-sm mb-4\",children:[/*#__PURE__*/_jsx(\"i\",{className:\"fa-solid fa-user-tie mr-2\"}),course.instructor]}),/*#__PURE__*/_jsxs(\"div\",{className:\"mb-4\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex justify-between text-sm mb-1\",children:[/*#__PURE__*/_jsx(\"span\",{children:\"Progress\"}),/*#__PURE__*/_jsxs(\"span\",{children:[course.progress,\"%\"]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"w-full bg-gray-700 rounded-full h-2\",children:/*#__PURE__*/_jsx(\"div\",{className:\"bg-gradient-to-r from-blue-500 to-purple-600 h-2 rounded-full\",style:{width:\"\".concat(course.progress,\"%\")}})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex justify-between items-center\",children:[/*#__PURE__*/_jsx(Link,{to:\"/learning\",className:\"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-300 font-medium text-center\",children:course.progress>0?'Continue':'Start'}),/*#__PURE__*/_jsx(\"button\",{className:\"px-4 py-2 bg-gray-700 text-white rounded-lg hover:bg-gray-600 transition-colors duration-300 font-medium\",children:\"Details\"})]})]})]},course.id))})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"mb-16\",children:[/*#__PURE__*/_jsx(\"h2\",{className:\"text-2xl font-bold mb-6\",children:\"Live Seminars\"}),/*#__PURE__*/_jsx(\"div\",{className:\"grid grid-cols-1 md:grid-cols-2 gap-6\",children:featuredCourses.filter(course=>course.isSeminar).map((course,index)=>/*#__PURE__*/_jsxs(\"div\",{className:\"bg-gradient-to-r from-purple-900 to-indigo-900 rounded-xl overflow-hidden shadow-lg hover:shadow-purple-500/20 transition-all duration-300 cursor-pointer\",onMouseEnter:()=>handleCourseHover(featuredCourses.indexOf(course)),onMouseLeave:()=>handleCourseHover(null),children:[/*#__PURE__*/_jsx(\"div\",{className:\"h-48 overflow-hidden\",children:/*#__PURE__*/_jsx(\"img\",{src:course.image,alt:course.title,className:\"w-full h-full object-cover object-top transform hover:scale-105 transition-transform duration-300\"})}),/*#__PURE__*/_jsxs(\"div\",{className:\"p-6\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center mb-2\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"bg-red-500 text-white text-xs px-2 py-1 rounded-full mr-2\",children:\"LIVE\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-purple-300 text-sm\",children:course.date})]}),/*#__PURE__*/_jsx(\"h3\",{className:\"text-xl font-bold mb-2\",children:course.title}),/*#__PURE__*/_jsxs(\"p\",{className:\"text-blue-300 text-sm mb-4\",children:[/*#__PURE__*/_jsx(\"i\",{className:\"fa-solid fa-user-tie mr-2\"}),course.instructor]}),/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-300 text-sm mb-4\",children:course.description}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex justify-between items-center\",children:[/*#__PURE__*/_jsx(Link,{to:\"/learning\",className:\"px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors duration-300 font-medium text-center\",children:\"Register\"}),/*#__PURE__*/_jsxs(\"span\",{className:\"text-purple-300 text-sm\",children:[/*#__PURE__*/_jsx(\"i\",{className:\"fa-solid fa-users mr-1\"}),course.seats]})]})]})]},course.id))})]})]})]})]})]});};export default ClassroomPage;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "Link", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "ClassroomPage", "_ref", "user", "moodleData", "sideMenuOpen", "setSideMenuOpen", "activeTab", "setActiveTab", "hoverCourse", "setHoverCourse", "previewPlayerRef", "progressChartRef", "current", "toggleSideMenu", "handleCourseHover", "index", "play", "catch", "e", "console", "log", "featuredCourses", "id", "title", "instructor", "progress", "image", "isSeminar", "description", "date", "seats", "className", "children", "concat", "onClick", "ref", "to", "src", "alt", "type", "placeholder", "loop", "muted", "playsInline", "style", "width", "filter", "course", "map", "onMouseEnter", "onMouseLeave", "indexOf"], "sources": ["/Users/<USER>/Documents/FatbeamU/react-app/src/pages/ClassroomPage.js"], "sourcesContent": ["// Converted from ClassroomSplash-Page.tsx - The exported code uses Tailwind CSS\nimport React, { useState, useEffect, useRef } from 'react';\nimport { Link } from 'react-router-dom';\n\nconst ClassroomPage = ({ user, moodleData }) => {\n  const [sideMenuOpen, setSideMenuOpen] = useState(false);\n  const [activeTab, setActiveTab] = useState('dashboard');\n  const [hoverCourse, setHoverCourse] = useState(null);\n  const previewPlayerRef = useRef(null);\n  const progressChartRef = useRef(null);\n\n  useEffect(() => {\n    // Simplified chart implementation for now\n    if (progressChartRef.current) {\n      // Chart initialization would go here\n      // For now, we'll use a simple implementation\n    }\n  }, []);\n\n  const toggleSideMenu = () => {\n    setSideMenuOpen(!sideMenuOpen);\n  };\n\n  const handleCourseHover = (index) => {\n    setHoverCourse(index);\n    if (previewPlayerRef.current && index !== null) {\n      previewPlayerRef.current.play().catch(e => console.log(\"Autoplay prevented:\", e));\n    }\n  };\n\n  const featuredCourses = [\n    {\n      id: 1,\n      title: \"Advanced Network Enchantments\",\n      instructor: \"Professor Elara Waveweaver\",\n      progress: 65,\n      image: \"https://readdy.ai/api/search-image?query=Magical%2520network%2520operations%2520center%2520with%2520glowing%2520blue%2520and%2520purple%2520holographic%2520displays%2520showing%2520network%2520traffic%2520visualizations%252C%2520staffed%2520by%2520professionals%2520in%2520tech-inspired%2520robes%252C%2520with%2520floating%2520data%2520streams%2520and%2520ethereal%2520connections%2520between%2520server%2520racks%252C%2520high%2520quality%2520digital%2520art&width=400&height=225&seq=course1&orientation=landscape\",\n      isSeminar: false,\n      description: \"Master the art of network enchantment with advanced techniques for optimizing magical data flows.\"\n    },\n    {\n      id: 2,\n      title: \"Packet Manipulation Arts\",\n      instructor: \"Dr. Thorne Cablemancer\",\n      progress: 30,\n      image: \"https://readdy.ai/api/search-image?query=Ethereal%2520hands%2520manipulating%2520glowing%2520network%2520packets%2520floating%2520in%2520air%252C%2520with%2520streams%2520of%2520data%2520flowing%2520between%2520crystalline%2520nodes%252C%2520against%2520a%2520backdrop%2520of%2520magical%2520server%2520infrastructure%2520with%2520blue%2520and%2520purple%2520lighting%252C%2520high%2520quality%2520digital%2520art&width=400&height=225&seq=course2&orientation=landscape\",\n      isSeminar: false,\n      description: \"Learn to shape and direct network packets with precision and efficiency.\"\n    },\n    {\n      id: 3,\n      title: \"Network Defense Against Dark Arts\",\n      instructor: \"Master Orion Signalkeeper\",\n      progress: 0,\n      image: \"https://readdy.ai/api/search-image?query=Magical%2520shield%2520protecting%2520network%2520infrastructure%2520from%2520shadowy%2520attacks%252C%2520with%2520glowing%2520blue%2520defensive%2520runes%2520and%2520sigils%252C%2520ethereal%2520guardians%2520monitoring%2520for%2520intrusions%252C%2520dark%2520background%2520with%2520contrasting%2520magical%2520light%2520effects%252C%2520high%2520quality%2520digital%2520art&width=400&height=225&seq=course3&orientation=landscape\",\n      isSeminar: false,\n      description: \"Protect your networks from malicious attacks with advanced defensive techniques.\"\n    },\n    {\n      id: 4,\n      title: \"Live Seminar: Future of Magical Networks\",\n      instructor: \"Professor Iris Dataweave\",\n      date: \"May 10, 2025 • 2:00 PM\",\n      image: \"https://readdy.ai/api/search-image?query=Futuristic%2520magical%2520auditorium%2520with%2520a%2520presenter%2520on%2520stage%2520demonstrating%2520advanced%2520network%2520concepts%2520with%2520floating%2520holographic%2520displays%252C%2520audience%2520of%2520diverse%2520students%2520in%2520tech-magical%2520attire%252C%2520glowing%2520blue%2520and%2520purple%2520lighting%252C%2520high%2520quality%2520digital%2520art&width=400&height=225&seq=seminar1&orientation=landscape\",\n      isSeminar: true,\n      seats: \"43/100 seats available\",\n      description: \"Join us for a live discussion on emerging technologies and future trends in magical networking.\"\n    }\n  ];\n\n  return (\n    <div className=\"min-h-screen bg-gray-900 text-white font-sans\">\n      {/* Side Menu */}\n      <div className={`fixed top-0 left-0 h-full w-80 bg-indigo-900 z-50 transform transition-transform duration-300 ease-in-out shadow-2xl ${sideMenuOpen ? 'translate-x-0' : '-translate-x-full'}`}>\n        <div className=\"p-6\">\n          <div className=\"flex justify-between items-center mb-8\">\n            <h2 className=\"text-2xl font-bold text-white\">Student Dashboard</h2>\n            <button onClick={toggleSideMenu} className=\"text-white hover:text-blue-300 cursor-pointer\">\n              <i className=\"fa-solid fa-times text-xl\"></i>\n            </button>\n          </div>\n          <div className=\"flex items-center mb-8 border-b border-indigo-700 pb-6\">\n            <div className=\"h-16 w-16 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center text-2xl font-bold\">\n              SW\n            </div>\n            <div className=\"ml-4\">\n              <h3 className=\"text-lg font-semibold\">Student Wizard</h3>\n              <p className=\"text-blue-300\">Network Operations</p>\n            </div>\n          </div>\n          <nav className=\"mb-8\">\n            <ul className=\"space-y-2\">\n              <li>\n                <button\n                  onClick={() => setActiveTab('dashboard')}\n                  className={`w-full text-left px-4 py-3 rounded-lg flex items-center ${activeTab === 'dashboard' ? 'bg-indigo-800 text-white' : 'text-blue-200 hover:bg-indigo-800'}`}\n                >\n                  <i className=\"fa-solid fa-gauge-high mr-3\"></i>\n                  Dashboard\n                </button>\n              </li>\n              <li>\n                <button\n                  onClick={() => setActiveTab('courses')}\n                  className={`w-full text-left px-4 py-3 rounded-lg flex items-center ${activeTab === 'courses' ? 'bg-indigo-800 text-white' : 'text-blue-200 hover:bg-indigo-800'}`}\n                >\n                  <i className=\"fa-solid fa-graduation-cap mr-3\"></i>\n                  My Courses\n                </button>\n              </li>\n              <li>\n                <button\n                  onClick={() => setActiveTab('schedule')}\n                  className={`w-full text-left px-4 py-3 rounded-lg flex items-center ${activeTab === 'schedule' ? 'bg-indigo-800 text-white' : 'text-blue-200 hover:bg-indigo-800'}`}\n                >\n                  <i className=\"fa-solid fa-calendar-alt mr-3\"></i>\n                  Schedule\n                </button>\n              </li>\n            </ul>\n          </nav>\n          <div className=\"border-t border-indigo-700 pt-6\">\n            <h4 className=\"text-lg font-semibold mb-4\">Course Progress</h4>\n            <div ref={progressChartRef} className=\"h-48 w-full bg-gray-800 rounded-lg flex items-center justify-center\">\n              <div className=\"text-center\">\n                <div className=\"text-4xl font-bold text-blue-400 mb-2\">65%</div>\n                <div className=\"text-sm text-gray-400\">Overall Progress</div>\n              </div>\n            </div>\n            <div className=\"mt-4 grid grid-cols-3 gap-2 text-center text-sm\">\n              <div>\n                <div className=\"font-semibold\">Completed</div>\n                <div className=\"text-blue-300\">35%</div>\n              </div>\n              <div>\n                <div className=\"font-semibold\">In Progress</div>\n                <div className=\"text-blue-300\">20%</div>\n              </div>\n              <div>\n                <div className=\"font-semibold\">Not Started</div>\n                <div className=\"text-blue-300\">45%</div>\n              </div>\n            </div>\n          </div>\n          <div className=\"mt-8\">\n            <Link\n              to=\"/departments\"\n              className=\"block w-full px-4 py-3 bg-gradient-to-r from-purple-600 to-indigo-600 text-white rounded-lg text-center hover:from-purple-700 hover:to-indigo-700 transition-all duration-300 font-medium shadow-md\"\n            >\n              <i className=\"fa-solid fa-arrow-left mr-2\"></i>\n              Back to Department\n            </Link>\n          </div>\n        </div>\n      </div>\n\n      {/* Main Content */}\n      <div className=\"min-h-screen relative\">\n        <div className=\"absolute inset-0 z-0\">\n          <img\n            src=\"https://readdy.ai/api/search-image?query=Abstract%20digital%20network%20visualization%20with%20glowing%20blue%20and%20purple%20nodes%20connected%20by%20light%20streams%2C%20creating%20an%20intricate%20weaving%20pattern%20across%20a%20dark%20background%2C%20modern%20tech%20aesthetic%20with%20depth%20and%20dimension%2C%20professional%20enterprise%20grade%20network%20visualization&width=1920&height=1080&seq=bg1&orientation=landscape\"\n            alt=\"Network Background\"\n            className=\"w-full h-full object-cover opacity-10\"\n          />\n        </div>\n        <div className=\"relative z-10\">\n          {/* Header */}\n          <header className=\"bg-indigo-900/90 shadow-lg backdrop-blur-sm\">\n            <div className=\"container mx-auto px-6 py-4\">\n              <div className=\"flex justify-between items-center\">\n                <div className=\"flex items-center\">\n                  <button onClick={toggleSideMenu} className=\"mr-4 text-white hover:text-blue-300 cursor-pointer\">\n                    <i className=\"fa-solid fa-bars text-xl\"></i>\n                  </button>\n                  <h1 className=\"text-2xl font-bold text-white\">Network Operations Classroom</h1>\n                </div>\n                <div className=\"flex items-center space-x-4\">\n                  <div className=\"relative\">\n                    <input\n                      type=\"text\"\n                      placeholder=\"Search courses...\"\n                      className=\"px-4 py-2 bg-indigo-800 bg-opacity-50 rounded-lg text-white placeholder-blue-300 border-none focus:outline-none focus:ring-2 focus:ring-blue-500 w-64 text-sm\"\n                    />\n                    <i className=\"fa-solid fa-search absolute right-3 top-1/2 transform -translate-y-1/2 text-blue-300\"></i>\n                  </div>\n                  <button className=\"relative text-white hover:text-blue-300\">\n                    <i className=\"fa-solid fa-bell text-xl\"></i>\n                    <span className=\"absolute -top-1 -right-1 h-4 w-4 bg-red-500 rounded-full text-xs flex items-center justify-center\">2</span>\n                  </button>\n                  <div className=\"h-10 w-10 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center text-lg font-bold cursor-pointer\">\n                    SW\n                  </div>\n                </div>\n              </div>\n            </div>\n          </header>\n\n          {/* Featured Course Preview with Video */}\n          <div className=\"relative h-[70vh] overflow-hidden\">\n            <div className=\"absolute inset-0 bg-gradient-to-b from-indigo-900/90 to-gray-900/90 backdrop-blur-sm\"></div>\n            {hoverCourse !== null && (\n              <div className=\"absolute inset-0 z-0\">\n                <div className=\"absolute inset-0 bg-gradient-to-r from-indigo-900 via-indigo-900/80 to-transparent z-10\"></div>\n                <video\n                  ref={previewPlayerRef}\n                  className=\"w-full h-full object-cover opacity-50\"\n                  loop\n                  muted\n                  playsInline\n                >\n                  <source src=\"https://assets.mixkit.co/videos/preview/mixkit-digital-network-connection-over-blue-background-97.mp4\" type=\"video/mp4\" />\n                </video>\n              </div>\n            )}\n            <div className=\"absolute inset-0 flex items-center z-10\">\n              <div className=\"container mx-auto px-6\">\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-12 items-center\">\n                  <div>\n                    {hoverCourse !== null ? (\n                      <>\n                        <div className=\"mb-2 text-blue-400 font-medium\">\n                          {featuredCourses[hoverCourse].isSeminar ? 'LIVE SEMINAR' : 'FEATURED COURSE'}\n                        </div>\n                        <h2 className=\"text-4xl font-bold mb-4\">{featuredCourses[hoverCourse].title}</h2>\n                        <p className=\"text-blue-200 mb-2\">\n                          <i className=\"fa-solid fa-user-tie mr-2\"></i>\n                          {featuredCourses[hoverCourse].instructor}\n                        </p>\n                        {featuredCourses[hoverCourse].isSeminar ? (\n                          <p className=\"text-blue-200 mb-4\">\n                            <i className=\"fa-solid fa-calendar-day mr-2\"></i>\n                            {featuredCourses[hoverCourse].date}\n                          </p>\n                        ) : (\n                          <div className=\"mb-4\">\n                            <div className=\"flex items-center mb-1\">\n                              <div className=\"text-sm text-blue-200 mr-2\">Progress:</div>\n                              <div className=\"text-sm text-blue-200\">{featuredCourses[hoverCourse].progress}%</div>\n                            </div>\n                            <div className=\"w-full bg-gray-700 rounded-full h-2\">\n                              <div\n                                className=\"bg-gradient-to-r from-blue-500 to-purple-600 h-2 rounded-full\"\n                                style={{ width: `${featuredCourses[hoverCourse].progress}%` }}\n                              ></div>\n                            </div>\n                          </div>\n                        )}\n                        <p className=\"text-lg text-gray-300 mb-6\">\n                          {featuredCourses[hoverCourse].description}\n                        </p>\n                        <div className=\"flex flex-col sm:flex-row gap-4\">\n                          <Link\n                            to=\"/learning\"\n                            className=\"px-6 py-3 bg-gradient-to-r from-purple-600 to-blue-600 rounded-lg hover:from-purple-700 hover:to-blue-700 transition-all duration-300 font-medium shadow-lg text-center\"\n                          >\n                            {featuredCourses[hoverCourse].isSeminar ? 'Register for Seminar' :\n                             featuredCourses[hoverCourse].progress > 0 ? 'Continue Learning' : 'Start Course'}\n                          </Link>\n                          <button className=\"px-6 py-3 bg-gray-800 rounded-lg hover:bg-gray-700 transition-all duration-300 font-medium\">\n                            View Details\n                          </button>\n                        </div>\n                      </>\n                    ) : (\n                      <>\n                        <h2 className=\"text-4xl font-bold mb-4\">Welcome to the Classroom</h2>\n                        <p className=\"text-lg text-gray-300 mb-6\">\n                          Explore your courses, track your progress, and enhance your network operations skills. Hover over a course below to see more details.\n                        </p>\n                        <button className=\"px-6 py-3 bg-gradient-to-r from-purple-600 to-blue-600 rounded-lg hover:from-purple-700 hover:to-blue-700 transition-all duration-300 font-medium shadow-lg\">\n                          Browse All Courses\n                        </button>\n                      </>\n                    )}\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* Dynamic Course Categories */}\n          <div className=\"container mx-auto px-6 py-12\">\n            {/* Continue Learning Section */}\n            <div className=\"mb-16\">\n              <h2 className=\"text-2xl font-bold mb-6\">Continue Learning</h2>\n              <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n                {featuredCourses.filter(course => !course.isSeminar).map((course, index) => (\n                  <div\n                    key={course.id}\n                    className=\"bg-gray-800 rounded-xl overflow-hidden shadow-lg hover:shadow-blue-500/10 transition-all duration-300 cursor-pointer h-full\"\n                    onMouseEnter={() => handleCourseHover(index)}\n                    onMouseLeave={() => handleCourseHover(null)}\n                  >\n                    <div className=\"h-48 overflow-hidden\">\n                      <img\n                        src={course.image}\n                        alt={course.title}\n                        className=\"w-full h-full object-cover object-top transform hover:scale-105 transition-transform duration-300\"\n                      />\n                    </div>\n                    <div className=\"p-6\">\n                      <h3 className=\"text-xl font-bold mb-2\">{course.title}</h3>\n                      <p className=\"text-blue-300 text-sm mb-4\">\n                        <i className=\"fa-solid fa-user-tie mr-2\"></i>\n                        {course.instructor}\n                      </p>\n                      <div className=\"mb-4\">\n                        <div className=\"flex justify-between text-sm mb-1\">\n                          <span>Progress</span>\n                          <span>{course.progress}%</span>\n                        </div>\n                        <div className=\"w-full bg-gray-700 rounded-full h-2\">\n                          <div\n                            className=\"bg-gradient-to-r from-blue-500 to-purple-600 h-2 rounded-full\"\n                            style={{ width: `${course.progress}%` }}\n                          ></div>\n                        </div>\n                      </div>\n                      <div className=\"flex justify-between items-center\">\n                        <Link\n                          to=\"/learning\"\n                          className=\"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-300 font-medium text-center\"\n                        >\n                          {course.progress > 0 ? 'Continue' : 'Start'}\n                        </Link>\n                        <button className=\"px-4 py-2 bg-gray-700 text-white rounded-lg hover:bg-gray-600 transition-colors duration-300 font-medium\">\n                          Details\n                        </button>\n                      </div>\n                    </div>\n                  </div>\n                ))}\n              </div>\n            </div>\n\n            {/* Live Seminars Section */}\n            <div className=\"mb-16\">\n              <h2 className=\"text-2xl font-bold mb-6\">Live Seminars</h2>\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                {featuredCourses.filter(course => course.isSeminar).map((course, index) => (\n                  <div\n                    key={course.id}\n                    className=\"bg-gradient-to-r from-purple-900 to-indigo-900 rounded-xl overflow-hidden shadow-lg hover:shadow-purple-500/20 transition-all duration-300 cursor-pointer\"\n                    onMouseEnter={() => handleCourseHover(featuredCourses.indexOf(course))}\n                    onMouseLeave={() => handleCourseHover(null)}\n                  >\n                    <div className=\"h-48 overflow-hidden\">\n                      <img\n                        src={course.image}\n                        alt={course.title}\n                        className=\"w-full h-full object-cover object-top transform hover:scale-105 transition-transform duration-300\"\n                      />\n                    </div>\n                    <div className=\"p-6\">\n                      <div className=\"flex items-center mb-2\">\n                        <span className=\"bg-red-500 text-white text-xs px-2 py-1 rounded-full mr-2\">LIVE</span>\n                        <span className=\"text-purple-300 text-sm\">{course.date}</span>\n                      </div>\n                      <h3 className=\"text-xl font-bold mb-2\">{course.title}</h3>\n                      <p className=\"text-blue-300 text-sm mb-4\">\n                        <i className=\"fa-solid fa-user-tie mr-2\"></i>\n                        {course.instructor}\n                      </p>\n                      <p className=\"text-gray-300 text-sm mb-4\">{course.description}</p>\n                      <div className=\"flex justify-between items-center\">\n                        <Link\n                          to=\"/learning\"\n                          className=\"px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors duration-300 font-medium text-center\"\n                        >\n                          Register\n                        </Link>\n                        <span className=\"text-purple-300 text-sm\">\n                          <i className=\"fa-solid fa-users mr-1\"></i>\n                          {course.seats}\n                        </span>\n                      </div>\n                    </div>\n                  </div>\n                ))}\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default ClassroomPage;\n"], "mappings": "AAAA;AACA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,CAAEC,MAAM,KAAQ,OAAO,CAC1D,OAASC,IAAI,KAAQ,kBAAkB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAExC,KAAM,CAAAC,aAAa,CAAGC,IAAA,EAA0B,IAAzB,CAAEC,IAAI,CAAEC,UAAW,CAAC,CAAAF,IAAA,CACzC,KAAM,CAACG,YAAY,CAAEC,eAAe,CAAC,CAAGf,QAAQ,CAAC,KAAK,CAAC,CACvD,KAAM,CAACgB,SAAS,CAAEC,YAAY,CAAC,CAAGjB,QAAQ,CAAC,WAAW,CAAC,CACvD,KAAM,CAACkB,WAAW,CAAEC,cAAc,CAAC,CAAGnB,QAAQ,CAAC,IAAI,CAAC,CACpD,KAAM,CAAAoB,gBAAgB,CAAGlB,MAAM,CAAC,IAAI,CAAC,CACrC,KAAM,CAAAmB,gBAAgB,CAAGnB,MAAM,CAAC,IAAI,CAAC,CAErCD,SAAS,CAAC,IAAM,CACd;AACA,GAAIoB,gBAAgB,CAACC,OAAO,CAAE,CAC5B;AACA;AAAA,CAEJ,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAC,cAAc,CAAGA,CAAA,GAAM,CAC3BR,eAAe,CAAC,CAACD,YAAY,CAAC,CAChC,CAAC,CAED,KAAM,CAAAU,iBAAiB,CAAIC,KAAK,EAAK,CACnCN,cAAc,CAACM,KAAK,CAAC,CACrB,GAAIL,gBAAgB,CAACE,OAAO,EAAIG,KAAK,GAAK,IAAI,CAAE,CAC9CL,gBAAgB,CAACE,OAAO,CAACI,IAAI,CAAC,CAAC,CAACC,KAAK,CAACC,CAAC,EAAIC,OAAO,CAACC,GAAG,CAAC,qBAAqB,CAAEF,CAAC,CAAC,CAAC,CACnF,CACF,CAAC,CAED,KAAM,CAAAG,eAAe,CAAG,CACtB,CACEC,EAAE,CAAE,CAAC,CACLC,KAAK,CAAE,+BAA+B,CACtCC,UAAU,CAAE,4BAA4B,CACxCC,QAAQ,CAAE,EAAE,CACZC,KAAK,CAAE,qgBAAqgB,CAC5gBC,SAAS,CAAE,KAAK,CAChBC,WAAW,CAAE,mGACf,CAAC,CACD,CACEN,EAAE,CAAE,CAAC,CACLC,KAAK,CAAE,0BAA0B,CACjCC,UAAU,CAAE,wBAAwB,CACpCC,QAAQ,CAAE,EAAE,CACZC,KAAK,CAAE,sdAAsd,CAC7dC,SAAS,CAAE,KAAK,CAChBC,WAAW,CAAE,0EACf,CAAC,CACD,CACEN,EAAE,CAAE,CAAC,CACLC,KAAK,CAAE,mCAAmC,CAC1CC,UAAU,CAAE,2BAA2B,CACvCC,QAAQ,CAAE,CAAC,CACXC,KAAK,CAAE,6dAA6d,CACpeC,SAAS,CAAE,KAAK,CAChBC,WAAW,CAAE,kFACf,CAAC,CACD,CACEN,EAAE,CAAE,CAAC,CACLC,KAAK,CAAE,0CAA0C,CACjDC,UAAU,CAAE,0BAA0B,CACtCK,IAAI,CAAE,wBAAwB,CAC9BH,KAAK,CAAE,8dAA8d,CACreC,SAAS,CAAE,IAAI,CACfG,KAAK,CAAE,wBAAwB,CAC/BF,WAAW,CAAE,iGACf,CAAC,CACF,CAED,mBACE/B,KAAA,QAAKkC,SAAS,CAAC,+CAA+C,CAAAC,QAAA,eAE5DrC,IAAA,QAAKoC,SAAS,yHAAAE,MAAA,CAA0H7B,YAAY,CAAG,eAAe,CAAG,mBAAmB,CAAG,CAAA4B,QAAA,cAC7LnC,KAAA,QAAKkC,SAAS,CAAC,KAAK,CAAAC,QAAA,eAClBnC,KAAA,QAAKkC,SAAS,CAAC,wCAAwC,CAAAC,QAAA,eACrDrC,IAAA,OAAIoC,SAAS,CAAC,+BAA+B,CAAAC,QAAA,CAAC,mBAAiB,CAAI,CAAC,cACpErC,IAAA,WAAQuC,OAAO,CAAErB,cAAe,CAACkB,SAAS,CAAC,+CAA+C,CAAAC,QAAA,cACxFrC,IAAA,MAAGoC,SAAS,CAAC,2BAA2B,CAAI,CAAC,CACvC,CAAC,EACN,CAAC,cACNlC,KAAA,QAAKkC,SAAS,CAAC,wDAAwD,CAAAC,QAAA,eACrErC,IAAA,QAAKoC,SAAS,CAAC,yHAAyH,CAAAC,QAAA,CAAC,IAEzI,CAAK,CAAC,cACNnC,KAAA,QAAKkC,SAAS,CAAC,MAAM,CAAAC,QAAA,eACnBrC,IAAA,OAAIoC,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,gBAAc,CAAI,CAAC,cACzDrC,IAAA,MAAGoC,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,oBAAkB,CAAG,CAAC,EAChD,CAAC,EACH,CAAC,cACNrC,IAAA,QAAKoC,SAAS,CAAC,MAAM,CAAAC,QAAA,cACnBnC,KAAA,OAAIkC,SAAS,CAAC,WAAW,CAAAC,QAAA,eACvBrC,IAAA,OAAAqC,QAAA,cACEnC,KAAA,WACEqC,OAAO,CAAEA,CAAA,GAAM3B,YAAY,CAAC,WAAW,CAAE,CACzCwB,SAAS,4DAAAE,MAAA,CAA6D3B,SAAS,GAAK,WAAW,CAAG,0BAA0B,CAAG,mCAAmC,CAAG,CAAA0B,QAAA,eAErKrC,IAAA,MAAGoC,SAAS,CAAC,6BAA6B,CAAI,CAAC,YAEjD,EAAQ,CAAC,CACP,CAAC,cACLpC,IAAA,OAAAqC,QAAA,cACEnC,KAAA,WACEqC,OAAO,CAAEA,CAAA,GAAM3B,YAAY,CAAC,SAAS,CAAE,CACvCwB,SAAS,4DAAAE,MAAA,CAA6D3B,SAAS,GAAK,SAAS,CAAG,0BAA0B,CAAG,mCAAmC,CAAG,CAAA0B,QAAA,eAEnKrC,IAAA,MAAGoC,SAAS,CAAC,iCAAiC,CAAI,CAAC,aAErD,EAAQ,CAAC,CACP,CAAC,cACLpC,IAAA,OAAAqC,QAAA,cACEnC,KAAA,WACEqC,OAAO,CAAEA,CAAA,GAAM3B,YAAY,CAAC,UAAU,CAAE,CACxCwB,SAAS,4DAAAE,MAAA,CAA6D3B,SAAS,GAAK,UAAU,CAAG,0BAA0B,CAAG,mCAAmC,CAAG,CAAA0B,QAAA,eAEpKrC,IAAA,MAAGoC,SAAS,CAAC,+BAA+B,CAAI,CAAC,WAEnD,EAAQ,CAAC,CACP,CAAC,EACH,CAAC,CACF,CAAC,cACNlC,KAAA,QAAKkC,SAAS,CAAC,iCAAiC,CAAAC,QAAA,eAC9CrC,IAAA,OAAIoC,SAAS,CAAC,4BAA4B,CAAAC,QAAA,CAAC,iBAAe,CAAI,CAAC,cAC/DrC,IAAA,QAAKwC,GAAG,CAAExB,gBAAiB,CAACoB,SAAS,CAAC,qEAAqE,CAAAC,QAAA,cACzGnC,KAAA,QAAKkC,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1BrC,IAAA,QAAKoC,SAAS,CAAC,uCAAuC,CAAAC,QAAA,CAAC,KAAG,CAAK,CAAC,cAChErC,IAAA,QAAKoC,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,kBAAgB,CAAK,CAAC,EAC1D,CAAC,CACH,CAAC,cACNnC,KAAA,QAAKkC,SAAS,CAAC,iDAAiD,CAAAC,QAAA,eAC9DnC,KAAA,QAAAmC,QAAA,eACErC,IAAA,QAAKoC,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,WAAS,CAAK,CAAC,cAC9CrC,IAAA,QAAKoC,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,KAAG,CAAK,CAAC,EACrC,CAAC,cACNnC,KAAA,QAAAmC,QAAA,eACErC,IAAA,QAAKoC,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,aAAW,CAAK,CAAC,cAChDrC,IAAA,QAAKoC,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,KAAG,CAAK,CAAC,EACrC,CAAC,cACNnC,KAAA,QAAAmC,QAAA,eACErC,IAAA,QAAKoC,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,aAAW,CAAK,CAAC,cAChDrC,IAAA,QAAKoC,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,KAAG,CAAK,CAAC,EACrC,CAAC,EACH,CAAC,EACH,CAAC,cACNrC,IAAA,QAAKoC,SAAS,CAAC,MAAM,CAAAC,QAAA,cACnBnC,KAAA,CAACJ,IAAI,EACH2C,EAAE,CAAC,cAAc,CACjBL,SAAS,CAAC,qMAAqM,CAAAC,QAAA,eAE/MrC,IAAA,MAAGoC,SAAS,CAAC,6BAA6B,CAAI,CAAC,qBAEjD,EAAM,CAAC,CACJ,CAAC,EACH,CAAC,CACH,CAAC,cAGNlC,KAAA,QAAKkC,SAAS,CAAC,uBAAuB,CAAAC,QAAA,eACpCrC,IAAA,QAAKoC,SAAS,CAAC,sBAAsB,CAAAC,QAAA,cACnCrC,IAAA,QACE0C,GAAG,CAAC,mbAAmb,CACvbC,GAAG,CAAC,oBAAoB,CACxBP,SAAS,CAAC,uCAAuC,CAClD,CAAC,CACC,CAAC,cACNlC,KAAA,QAAKkC,SAAS,CAAC,eAAe,CAAAC,QAAA,eAE5BrC,IAAA,WAAQoC,SAAS,CAAC,6CAA6C,CAAAC,QAAA,cAC7DrC,IAAA,QAAKoC,SAAS,CAAC,6BAA6B,CAAAC,QAAA,cAC1CnC,KAAA,QAAKkC,SAAS,CAAC,mCAAmC,CAAAC,QAAA,eAChDnC,KAAA,QAAKkC,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChCrC,IAAA,WAAQuC,OAAO,CAAErB,cAAe,CAACkB,SAAS,CAAC,oDAAoD,CAAAC,QAAA,cAC7FrC,IAAA,MAAGoC,SAAS,CAAC,0BAA0B,CAAI,CAAC,CACtC,CAAC,cACTpC,IAAA,OAAIoC,SAAS,CAAC,+BAA+B,CAAAC,QAAA,CAAC,8BAA4B,CAAI,CAAC,EAC5E,CAAC,cACNnC,KAAA,QAAKkC,SAAS,CAAC,6BAA6B,CAAAC,QAAA,eAC1CnC,KAAA,QAAKkC,SAAS,CAAC,UAAU,CAAAC,QAAA,eACvBrC,IAAA,UACE4C,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,mBAAmB,CAC/BT,SAAS,CAAC,+JAA+J,CAC1K,CAAC,cACFpC,IAAA,MAAGoC,SAAS,CAAC,sFAAsF,CAAI,CAAC,EACrG,CAAC,cACNlC,KAAA,WAAQkC,SAAS,CAAC,yCAAyC,CAAAC,QAAA,eACzDrC,IAAA,MAAGoC,SAAS,CAAC,0BAA0B,CAAI,CAAC,cAC5CpC,IAAA,SAAMoC,SAAS,CAAC,mGAAmG,CAAAC,QAAA,CAAC,GAAC,CAAM,CAAC,EACtH,CAAC,cACTrC,IAAA,QAAKoC,SAAS,CAAC,uIAAuI,CAAAC,QAAA,CAAC,IAEvJ,CAAK,CAAC,EACH,CAAC,EACH,CAAC,CACH,CAAC,CACA,CAAC,cAGTnC,KAAA,QAAKkC,SAAS,CAAC,mCAAmC,CAAAC,QAAA,eAChDrC,IAAA,QAAKoC,SAAS,CAAC,sFAAsF,CAAM,CAAC,CAC3GvB,WAAW,GAAK,IAAI,eACnBX,KAAA,QAAKkC,SAAS,CAAC,sBAAsB,CAAAC,QAAA,eACnCrC,IAAA,QAAKoC,SAAS,CAAC,yFAAyF,CAAM,CAAC,cAC/GpC,IAAA,UACEwC,GAAG,CAAEzB,gBAAiB,CACtBqB,SAAS,CAAC,uCAAuC,CACjDU,IAAI,MACJC,KAAK,MACLC,WAAW,MAAAX,QAAA,cAEXrC,IAAA,WAAQ0C,GAAG,CAAC,uGAAuG,CAACE,IAAI,CAAC,WAAW,CAAE,CAAC,CAClI,CAAC,EACL,CACN,cACD5C,IAAA,QAAKoC,SAAS,CAAC,yCAAyC,CAAAC,QAAA,cACtDrC,IAAA,QAAKoC,SAAS,CAAC,wBAAwB,CAAAC,QAAA,cACrCrC,IAAA,QAAKoC,SAAS,CAAC,qDAAqD,CAAAC,QAAA,cAClErC,IAAA,QAAAqC,QAAA,CACGxB,WAAW,GAAK,IAAI,cACnBX,KAAA,CAAAE,SAAA,EAAAiC,QAAA,eACErC,IAAA,QAAKoC,SAAS,CAAC,gCAAgC,CAAAC,QAAA,CAC5CX,eAAe,CAACb,WAAW,CAAC,CAACmB,SAAS,CAAG,cAAc,CAAG,iBAAiB,CACzE,CAAC,cACNhC,IAAA,OAAIoC,SAAS,CAAC,yBAAyB,CAAAC,QAAA,CAAEX,eAAe,CAACb,WAAW,CAAC,CAACe,KAAK,CAAK,CAAC,cACjF1B,KAAA,MAAGkC,SAAS,CAAC,oBAAoB,CAAAC,QAAA,eAC/BrC,IAAA,MAAGoC,SAAS,CAAC,2BAA2B,CAAI,CAAC,CAC5CV,eAAe,CAACb,WAAW,CAAC,CAACgB,UAAU,EACvC,CAAC,CACHH,eAAe,CAACb,WAAW,CAAC,CAACmB,SAAS,cACrC9B,KAAA,MAAGkC,SAAS,CAAC,oBAAoB,CAAAC,QAAA,eAC/BrC,IAAA,MAAGoC,SAAS,CAAC,+BAA+B,CAAI,CAAC,CAChDV,eAAe,CAACb,WAAW,CAAC,CAACqB,IAAI,EACjC,CAAC,cAEJhC,KAAA,QAAKkC,SAAS,CAAC,MAAM,CAAAC,QAAA,eACnBnC,KAAA,QAAKkC,SAAS,CAAC,wBAAwB,CAAAC,QAAA,eACrCrC,IAAA,QAAKoC,SAAS,CAAC,4BAA4B,CAAAC,QAAA,CAAC,WAAS,CAAK,CAAC,cAC3DnC,KAAA,QAAKkC,SAAS,CAAC,uBAAuB,CAAAC,QAAA,EAAEX,eAAe,CAACb,WAAW,CAAC,CAACiB,QAAQ,CAAC,GAAC,EAAK,CAAC,EAClF,CAAC,cACN9B,IAAA,QAAKoC,SAAS,CAAC,qCAAqC,CAAAC,QAAA,cAClDrC,IAAA,QACEoC,SAAS,CAAC,+DAA+D,CACzEa,KAAK,CAAE,CAAEC,KAAK,IAAAZ,MAAA,CAAKZ,eAAe,CAACb,WAAW,CAAC,CAACiB,QAAQ,KAAI,CAAE,CAC1D,CAAC,CACJ,CAAC,EACH,CACN,cACD9B,IAAA,MAAGoC,SAAS,CAAC,4BAA4B,CAAAC,QAAA,CACtCX,eAAe,CAACb,WAAW,CAAC,CAACoB,WAAW,CACxC,CAAC,cACJ/B,KAAA,QAAKkC,SAAS,CAAC,iCAAiC,CAAAC,QAAA,eAC9CrC,IAAA,CAACF,IAAI,EACH2C,EAAE,CAAC,WAAW,CACdL,SAAS,CAAC,yKAAyK,CAAAC,QAAA,CAElLX,eAAe,CAACb,WAAW,CAAC,CAACmB,SAAS,CAAG,sBAAsB,CAC/DN,eAAe,CAACb,WAAW,CAAC,CAACiB,QAAQ,CAAG,CAAC,CAAG,mBAAmB,CAAG,cAAc,CAC7E,CAAC,cACP9B,IAAA,WAAQoC,SAAS,CAAC,4FAA4F,CAAAC,QAAA,CAAC,cAE/G,CAAQ,CAAC,EACN,CAAC,EACN,CAAC,cAEHnC,KAAA,CAAAE,SAAA,EAAAiC,QAAA,eACErC,IAAA,OAAIoC,SAAS,CAAC,yBAAyB,CAAAC,QAAA,CAAC,0BAAwB,CAAI,CAAC,cACrErC,IAAA,MAAGoC,SAAS,CAAC,4BAA4B,CAAAC,QAAA,CAAC,uIAE1C,CAAG,CAAC,cACJrC,IAAA,WAAQoC,SAAS,CAAC,6JAA6J,CAAAC,QAAA,CAAC,oBAEhL,CAAQ,CAAC,EACT,CACH,CACE,CAAC,CACH,CAAC,CACH,CAAC,CACH,CAAC,EACH,CAAC,cAGNnC,KAAA,QAAKkC,SAAS,CAAC,8BAA8B,CAAAC,QAAA,eAE3CnC,KAAA,QAAKkC,SAAS,CAAC,OAAO,CAAAC,QAAA,eACpBrC,IAAA,OAAIoC,SAAS,CAAC,yBAAyB,CAAAC,QAAA,CAAC,mBAAiB,CAAI,CAAC,cAC9DrC,IAAA,QAAKoC,SAAS,CAAC,sDAAsD,CAAAC,QAAA,CAClEX,eAAe,CAACyB,MAAM,CAACC,MAAM,EAAI,CAACA,MAAM,CAACpB,SAAS,CAAC,CAACqB,GAAG,CAAC,CAACD,MAAM,CAAEhC,KAAK,gBACrElB,KAAA,QAEEkC,SAAS,CAAC,6HAA6H,CACvIkB,YAAY,CAAEA,CAAA,GAAMnC,iBAAiB,CAACC,KAAK,CAAE,CAC7CmC,YAAY,CAAEA,CAAA,GAAMpC,iBAAiB,CAAC,IAAI,CAAE,CAAAkB,QAAA,eAE5CrC,IAAA,QAAKoC,SAAS,CAAC,sBAAsB,CAAAC,QAAA,cACnCrC,IAAA,QACE0C,GAAG,CAAEU,MAAM,CAACrB,KAAM,CAClBY,GAAG,CAAES,MAAM,CAACxB,KAAM,CAClBQ,SAAS,CAAC,mGAAmG,CAC9G,CAAC,CACC,CAAC,cACNlC,KAAA,QAAKkC,SAAS,CAAC,KAAK,CAAAC,QAAA,eAClBrC,IAAA,OAAIoC,SAAS,CAAC,wBAAwB,CAAAC,QAAA,CAAEe,MAAM,CAACxB,KAAK,CAAK,CAAC,cAC1D1B,KAAA,MAAGkC,SAAS,CAAC,4BAA4B,CAAAC,QAAA,eACvCrC,IAAA,MAAGoC,SAAS,CAAC,2BAA2B,CAAI,CAAC,CAC5CgB,MAAM,CAACvB,UAAU,EACjB,CAAC,cACJ3B,KAAA,QAAKkC,SAAS,CAAC,MAAM,CAAAC,QAAA,eACnBnC,KAAA,QAAKkC,SAAS,CAAC,mCAAmC,CAAAC,QAAA,eAChDrC,IAAA,SAAAqC,QAAA,CAAM,UAAQ,CAAM,CAAC,cACrBnC,KAAA,SAAAmC,QAAA,EAAOe,MAAM,CAACtB,QAAQ,CAAC,GAAC,EAAM,CAAC,EAC5B,CAAC,cACN9B,IAAA,QAAKoC,SAAS,CAAC,qCAAqC,CAAAC,QAAA,cAClDrC,IAAA,QACEoC,SAAS,CAAC,+DAA+D,CACzEa,KAAK,CAAE,CAAEC,KAAK,IAAAZ,MAAA,CAAKc,MAAM,CAACtB,QAAQ,KAAI,CAAE,CACpC,CAAC,CACJ,CAAC,EACH,CAAC,cACN5B,KAAA,QAAKkC,SAAS,CAAC,mCAAmC,CAAAC,QAAA,eAChDrC,IAAA,CAACF,IAAI,EACH2C,EAAE,CAAC,WAAW,CACdL,SAAS,CAAC,sHAAsH,CAAAC,QAAA,CAE/He,MAAM,CAACtB,QAAQ,CAAG,CAAC,CAAG,UAAU,CAAG,OAAO,CACvC,CAAC,cACP9B,IAAA,WAAQoC,SAAS,CAAC,0GAA0G,CAAAC,QAAA,CAAC,SAE7H,CAAQ,CAAC,EACN,CAAC,EACH,CAAC,GAzCDe,MAAM,CAACzB,EA0CT,CACN,CAAC,CACC,CAAC,EACH,CAAC,cAGNzB,KAAA,QAAKkC,SAAS,CAAC,OAAO,CAAAC,QAAA,eACpBrC,IAAA,OAAIoC,SAAS,CAAC,yBAAyB,CAAAC,QAAA,CAAC,eAAa,CAAI,CAAC,cAC1DrC,IAAA,QAAKoC,SAAS,CAAC,uCAAuC,CAAAC,QAAA,CACnDX,eAAe,CAACyB,MAAM,CAACC,MAAM,EAAIA,MAAM,CAACpB,SAAS,CAAC,CAACqB,GAAG,CAAC,CAACD,MAAM,CAAEhC,KAAK,gBACpElB,KAAA,QAEEkC,SAAS,CAAC,2JAA2J,CACrKkB,YAAY,CAAEA,CAAA,GAAMnC,iBAAiB,CAACO,eAAe,CAAC8B,OAAO,CAACJ,MAAM,CAAC,CAAE,CACvEG,YAAY,CAAEA,CAAA,GAAMpC,iBAAiB,CAAC,IAAI,CAAE,CAAAkB,QAAA,eAE5CrC,IAAA,QAAKoC,SAAS,CAAC,sBAAsB,CAAAC,QAAA,cACnCrC,IAAA,QACE0C,GAAG,CAAEU,MAAM,CAACrB,KAAM,CAClBY,GAAG,CAAES,MAAM,CAACxB,KAAM,CAClBQ,SAAS,CAAC,mGAAmG,CAC9G,CAAC,CACC,CAAC,cACNlC,KAAA,QAAKkC,SAAS,CAAC,KAAK,CAAAC,QAAA,eAClBnC,KAAA,QAAKkC,SAAS,CAAC,wBAAwB,CAAAC,QAAA,eACrCrC,IAAA,SAAMoC,SAAS,CAAC,2DAA2D,CAAAC,QAAA,CAAC,MAAI,CAAM,CAAC,cACvFrC,IAAA,SAAMoC,SAAS,CAAC,yBAAyB,CAAAC,QAAA,CAAEe,MAAM,CAAClB,IAAI,CAAO,CAAC,EAC3D,CAAC,cACNlC,IAAA,OAAIoC,SAAS,CAAC,wBAAwB,CAAAC,QAAA,CAAEe,MAAM,CAACxB,KAAK,CAAK,CAAC,cAC1D1B,KAAA,MAAGkC,SAAS,CAAC,4BAA4B,CAAAC,QAAA,eACvCrC,IAAA,MAAGoC,SAAS,CAAC,2BAA2B,CAAI,CAAC,CAC5CgB,MAAM,CAACvB,UAAU,EACjB,CAAC,cACJ7B,IAAA,MAAGoC,SAAS,CAAC,4BAA4B,CAAAC,QAAA,CAAEe,MAAM,CAACnB,WAAW,CAAI,CAAC,cAClE/B,KAAA,QAAKkC,SAAS,CAAC,mCAAmC,CAAAC,QAAA,eAChDrC,IAAA,CAACF,IAAI,EACH2C,EAAE,CAAC,WAAW,CACdL,SAAS,CAAC,0HAA0H,CAAAC,QAAA,CACrI,UAED,CAAM,CAAC,cACPnC,KAAA,SAAMkC,SAAS,CAAC,yBAAyB,CAAAC,QAAA,eACvCrC,IAAA,MAAGoC,SAAS,CAAC,wBAAwB,CAAI,CAAC,CACzCgB,MAAM,CAACjB,KAAK,EACT,CAAC,EACJ,CAAC,EACH,CAAC,GAnCDiB,MAAM,CAACzB,EAoCT,CACN,CAAC,CACC,CAAC,EACH,CAAC,EACH,CAAC,EACH,CAAC,EACH,CAAC,EACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAAtB,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}