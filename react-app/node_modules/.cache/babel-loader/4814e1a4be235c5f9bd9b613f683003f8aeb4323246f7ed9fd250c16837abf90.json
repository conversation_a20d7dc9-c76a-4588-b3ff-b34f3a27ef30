{"ast": null, "code": "import platform from \"../platform/index.js\";\nimport utils from \"../utils.js\";\nimport isURLSameOrigin from \"./isURLSameOrigin.js\";\nimport cookies from \"./cookies.js\";\nimport buildFullPath from \"../core/buildFullPath.js\";\nimport mergeConfig from \"../core/mergeConfig.js\";\nimport AxiosHeaders from \"../core/AxiosHeaders.js\";\nimport buildURL from \"./buildURL.js\";\nexport default config => {\n  const newConfig = mergeConfig({}, config);\n  let {\n    data,\n    withXSRFToken,\n    xsrfHeaderName,\n    xsrfCookieName,\n    headers,\n    auth\n  } = newConfig;\n  newConfig.headers = headers = AxiosHeaders.from(headers);\n  newConfig.url = buildURL(buildFullPath(newConfig.baseURL, newConfig.url, newConfig.allowAbsoluteUrls), config.params, config.paramsSerializer);\n\n  // HTTP basic authentication\n  if (auth) {\n    headers.set('Authorization', 'Basic ' + btoa((auth.username || '') + ':' + (auth.password ? unescape(encodeURIComponent(auth.password)) : '')));\n  }\n  let contentType;\n  if (utils.isFormData(data)) {\n    if (platform.hasStandardBrowserEnv || platform.hasStandardBrowserWebWorkerEnv) {\n      headers.setContentType(undefined); // Let the browser set it\n    } else if ((contentType = headers.getContentType()) !== false) {\n      // fix semicolon duplication issue for ReactNative FormData implementation\n      const [type, ...tokens] = contentType ? contentType.split(';').map(token => token.trim()).filter(Boolean) : [];\n      headers.setContentType([type || 'multipart/form-data', ...tokens].join('; '));\n    }\n  }\n\n  // Add xsrf header\n  // This is only done if running in a standard browser environment.\n  // Specifically not if we're in a web worker, or react-native.\n\n  if (platform.hasStandardBrowserEnv) {\n    withXSRFToken && utils.isFunction(withXSRFToken) && (withXSRFToken = withXSRFToken(newConfig));\n    if (withXSRFToken || withXSRFToken !== false && isURLSameOrigin(newConfig.url)) {\n      // Add xsrf header\n      const xsrfValue = xsrfHeaderName && xsrfCookieName && cookies.read(xsrfCookieName);\n      if (xsrfValue) {\n        headers.set(xsrfHeaderName, xsrfValue);\n      }\n    }\n  }\n  return newConfig;\n};", "map": {"version": 3, "names": ["platform", "utils", "isURLSameOrigin", "cookies", "buildFullPath", "mergeConfig", "AxiosHeaders", "buildURL", "config", "newConfig", "data", "withXSRFToken", "xsrfHeaderName", "xsrfCookieName", "headers", "auth", "from", "url", "baseURL", "allowAbsoluteUrls", "params", "paramsSerializer", "set", "btoa", "username", "password", "unescape", "encodeURIComponent", "contentType", "isFormData", "hasStandardBrowserEnv", "hasStandardBrowserWebWorkerEnv", "setContentType", "undefined", "getContentType", "type", "tokens", "split", "map", "token", "trim", "filter", "Boolean", "join", "isFunction", "xsrfValue", "read"], "sources": ["/Users/<USER>/Documents/FatbeamU/react-app/node_modules/axios/lib/helpers/resolveConfig.js"], "sourcesContent": ["import platform from \"../platform/index.js\";\nimport utils from \"../utils.js\";\nimport isURLSameOrigin from \"./isURLSameOrigin.js\";\nimport cookies from \"./cookies.js\";\nimport buildFullPath from \"../core/buildFullPath.js\";\nimport mergeConfig from \"../core/mergeConfig.js\";\nimport AxiosHeaders from \"../core/AxiosHeaders.js\";\nimport buildURL from \"./buildURL.js\";\n\nexport default (config) => {\n  const newConfig = mergeConfig({}, config);\n\n  let {data, withXSRFToken, xsrfHeaderName, xsrfCookieName, headers, auth} = newConfig;\n\n  newConfig.headers = headers = AxiosHeaders.from(headers);\n\n  newConfig.url = buildURL(buildFullPath(newConfig.baseURL, newConfig.url, newConfig.allowAbsoluteUrls), config.params, config.paramsSerializer);\n\n  // HTTP basic authentication\n  if (auth) {\n    headers.set('Authorization', 'Basic ' +\n      btoa((auth.username || '') + ':' + (auth.password ? unescape(encodeURIComponent(auth.password)) : ''))\n    );\n  }\n\n  let contentType;\n\n  if (utils.isFormData(data)) {\n    if (platform.hasStandardBrowserEnv || platform.hasStandardBrowserWebWorkerEnv) {\n      headers.setContentType(undefined); // Let the browser set it\n    } else if ((contentType = headers.getContentType()) !== false) {\n      // fix semicolon duplication issue for ReactNative FormData implementation\n      const [type, ...tokens] = contentType ? contentType.split(';').map(token => token.trim()).filter(Boolean) : [];\n      headers.setContentType([type || 'multipart/form-data', ...tokens].join('; '));\n    }\n  }\n\n  // Add xsrf header\n  // This is only done if running in a standard browser environment.\n  // Specifically not if we're in a web worker, or react-native.\n\n  if (platform.hasStandardBrowserEnv) {\n    withXSRFToken && utils.isFunction(withXSRFToken) && (withXSRFToken = withXSRFToken(newConfig));\n\n    if (withXSRFToken || (withXSRFToken !== false && isURLSameOrigin(newConfig.url))) {\n      // Add xsrf header\n      const xsrfValue = xsrfHeaderName && xsrfCookieName && cookies.read(xsrfCookieName);\n\n      if (xsrfValue) {\n        headers.set(xsrfHeaderName, xsrfValue);\n      }\n    }\n  }\n\n  return newConfig;\n}\n\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,sBAAsB;AAC3C,OAAOC,KAAK,MAAM,aAAa;AAC/B,OAAOC,eAAe,MAAM,sBAAsB;AAClD,OAAOC,OAAO,MAAM,cAAc;AAClC,OAAOC,aAAa,MAAM,0BAA0B;AACpD,OAAOC,WAAW,MAAM,wBAAwB;AAChD,OAAOC,YAAY,MAAM,yBAAyB;AAClD,OAAOC,QAAQ,MAAM,eAAe;AAEpC,eAAgBC,MAAM,IAAK;EACzB,MAAMC,SAAS,GAAGJ,WAAW,CAAC,CAAC,CAAC,EAAEG,MAAM,CAAC;EAEzC,IAAI;IAACE,IAAI;IAAEC,aAAa;IAAEC,cAAc;IAAEC,cAAc;IAAEC,OAAO;IAAEC;EAAI,CAAC,GAAGN,SAAS;EAEpFA,SAAS,CAACK,OAAO,GAAGA,OAAO,GAAGR,YAAY,CAACU,IAAI,CAACF,OAAO,CAAC;EAExDL,SAAS,CAACQ,GAAG,GAAGV,QAAQ,CAACH,aAAa,CAACK,SAAS,CAACS,OAAO,EAAET,SAAS,CAACQ,GAAG,EAAER,SAAS,CAACU,iBAAiB,CAAC,EAAEX,MAAM,CAACY,MAAM,EAAEZ,MAAM,CAACa,gBAAgB,CAAC;;EAE9I;EACA,IAAIN,IAAI,EAAE;IACRD,OAAO,CAACQ,GAAG,CAAC,eAAe,EAAE,QAAQ,GACnCC,IAAI,CAAC,CAACR,IAAI,CAACS,QAAQ,IAAI,EAAE,IAAI,GAAG,IAAIT,IAAI,CAACU,QAAQ,GAAGC,QAAQ,CAACC,kBAAkB,CAACZ,IAAI,CAACU,QAAQ,CAAC,CAAC,GAAG,EAAE,CAAC,CACvG,CAAC;EACH;EAEA,IAAIG,WAAW;EAEf,IAAI3B,KAAK,CAAC4B,UAAU,CAACnB,IAAI,CAAC,EAAE;IAC1B,IAAIV,QAAQ,CAAC8B,qBAAqB,IAAI9B,QAAQ,CAAC+B,8BAA8B,EAAE;MAC7EjB,OAAO,CAACkB,cAAc,CAACC,SAAS,CAAC,CAAC,CAAC;IACrC,CAAC,MAAM,IAAI,CAACL,WAAW,GAAGd,OAAO,CAACoB,cAAc,CAAC,CAAC,MAAM,KAAK,EAAE;MAC7D;MACA,MAAM,CAACC,IAAI,EAAE,GAAGC,MAAM,CAAC,GAAGR,WAAW,GAAGA,WAAW,CAACS,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAACC,KAAK,IAAIA,KAAK,CAACC,IAAI,CAAC,CAAC,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC,GAAG,EAAE;MAC9G5B,OAAO,CAACkB,cAAc,CAAC,CAACG,IAAI,IAAI,qBAAqB,EAAE,GAAGC,MAAM,CAAC,CAACO,IAAI,CAAC,IAAI,CAAC,CAAC;IAC/E;EACF;;EAEA;EACA;EACA;;EAEA,IAAI3C,QAAQ,CAAC8B,qBAAqB,EAAE;IAClCnB,aAAa,IAAIV,KAAK,CAAC2C,UAAU,CAACjC,aAAa,CAAC,KAAKA,aAAa,GAAGA,aAAa,CAACF,SAAS,CAAC,CAAC;IAE9F,IAAIE,aAAa,IAAKA,aAAa,KAAK,KAAK,IAAIT,eAAe,CAACO,SAAS,CAACQ,GAAG,CAAE,EAAE;MAChF;MACA,MAAM4B,SAAS,GAAGjC,cAAc,IAAIC,cAAc,IAAIV,OAAO,CAAC2C,IAAI,CAACjC,cAAc,CAAC;MAElF,IAAIgC,SAAS,EAAE;QACb/B,OAAO,CAACQ,GAAG,CAACV,cAAc,EAAEiC,SAAS,CAAC;MACxC;IACF;EACF;EAEA,OAAOpC,SAAS;AAClB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}