{"ast": null, "code": "import React from'react';import{<PERSON>}from'react-router-dom';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const TowerTechniciansDeptPage=()=>{return/*#__PURE__*/_jsx(\"div\",{className:\"min-h-screen bg-white text-gray-800 font-sans pt-20\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"container mx-auto px-6 py-12\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-center mb-12\",children:[/*#__PURE__*/_jsxs(\"h1\",{className:\"text-4xl md:text-5xl font-serif font-bold mb-6\",children:[\"Tower Technicians \",/*#__PURE__*/_jsx(\"span\",{className:\"text-blue-600\",children:\"(<PERSON>)\"})]}),/*#__PURE__*/_jsx(\"p\",{className:\"text-xl text-gray-600 max-w-3xl mx-auto\",children:\"Master the heights of our magical infrastructure, ensuring the uninterrupted flow of arcane data through our network.\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"bg-blue-50 rounded-lg p-8 text-center\",children:[/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-tower-broadcast text-6xl text-blue-600 mb-4\"}),/*#__PURE__*/_jsx(\"h2\",{className:\"text-2xl font-bold text-gray-800 mb-4\",children:\"Department Page Coming Soon\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-600 mb-6\",children:\"Our sky weavers are currently ascending to new heights to bring you this content.\"}),/*#__PURE__*/_jsxs(Link,{to:\"/departments\",className:\"inline-flex items-center px-6 py-3 bg-blue-600 text-white rounded-full hover:bg-blue-700 transition-colors duration-300\",children:[/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-arrow-left mr-2\"}),\"Back to Departments\"]})]})]})});};export default TowerTechniciansDeptPage;", "map": {"version": 3, "names": ["React", "Link", "jsx", "_jsx", "jsxs", "_jsxs", "TowerTechniciansDeptPage", "className", "children", "to"], "sources": ["/Users/<USER>/Documents/FatbeamU/react-app/src/pages/departments/TowerTechniciansDeptPage.js"], "sourcesContent": ["import React from 'react';\nimport { Link } from 'react-router-dom';\n\nconst TowerTechniciansDeptPage = () => {\n  return (\n    <div className=\"min-h-screen bg-white text-gray-800 font-sans pt-20\">\n      <div className=\"container mx-auto px-6 py-12\">\n        <div className=\"text-center mb-12\">\n          <h1 className=\"text-4xl md:text-5xl font-serif font-bold mb-6\">\n            Tower Technicians <span className=\"text-blue-600\">(Sky Weavers)</span>\n          </h1>\n          <p className=\"text-xl text-gray-600 max-w-3xl mx-auto\">\n            Master the heights of our magical infrastructure, ensuring the uninterrupted flow of arcane data through our network.\n          </p>\n        </div>\n        \n        <div className=\"bg-blue-50 rounded-lg p-8 text-center\">\n          <i className=\"fas fa-tower-broadcast text-6xl text-blue-600 mb-4\"></i>\n          <h2 className=\"text-2xl font-bold text-gray-800 mb-4\">Department Page Coming Soon</h2>\n          <p className=\"text-gray-600 mb-6\">\n            Our sky weavers are currently ascending to new heights to bring you this content.\n          </p>\n          <Link to=\"/departments\" className=\"inline-flex items-center px-6 py-3 bg-blue-600 text-white rounded-full hover:bg-blue-700 transition-colors duration-300\">\n            <i className=\"fas fa-arrow-left mr-2\"></i>\n            Back to Departments\n          </Link>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default TowerTechniciansDeptPage;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OAASC,IAAI,KAAQ,kBAAkB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAExC,KAAM,CAAAC,wBAAwB,CAAGA,CAAA,GAAM,CACrC,mBACEH,IAAA,QAAKI,SAAS,CAAC,qDAAqD,CAAAC,QAAA,cAClEH,KAAA,QAAKE,SAAS,CAAC,8BAA8B,CAAAC,QAAA,eAC3CH,KAAA,QAAKE,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChCH,KAAA,OAAIE,SAAS,CAAC,gDAAgD,CAAAC,QAAA,EAAC,oBAC3C,cAAAL,IAAA,SAAMI,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,eAAa,CAAM,CAAC,EACpE,CAAC,cACLL,IAAA,MAAGI,SAAS,CAAC,yCAAyC,CAAAC,QAAA,CAAC,uHAEvD,CAAG,CAAC,EACD,CAAC,cAENH,KAAA,QAAKE,SAAS,CAAC,uCAAuC,CAAAC,QAAA,eACpDL,IAAA,MAAGI,SAAS,CAAC,oDAAoD,CAAI,CAAC,cACtEJ,IAAA,OAAII,SAAS,CAAC,uCAAuC,CAAAC,QAAA,CAAC,6BAA2B,CAAI,CAAC,cACtFL,IAAA,MAAGI,SAAS,CAAC,oBAAoB,CAAAC,QAAA,CAAC,mFAElC,CAAG,CAAC,cACJH,KAAA,CAACJ,IAAI,EAACQ,EAAE,CAAC,cAAc,CAACF,SAAS,CAAC,yHAAyH,CAAAC,QAAA,eACzJL,IAAA,MAAGI,SAAS,CAAC,wBAAwB,CAAI,CAAC,sBAE5C,EAAM,CAAC,EACJ,CAAC,EACH,CAAC,CACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAAD,wBAAwB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}