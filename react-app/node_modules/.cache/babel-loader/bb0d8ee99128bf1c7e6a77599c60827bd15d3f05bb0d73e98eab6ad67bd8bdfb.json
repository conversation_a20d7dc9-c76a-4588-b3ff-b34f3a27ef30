{"ast": null, "code": "import _objectSpread from\"/Users/<USER>/Documents/FatbeamU/react-app/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import axios from'axios';class MoodleAPI{constructor(){this.baseURL=process.env.REACT_APP_MOODLE_API_URL;this.token=process.env.REACT_APP_MOODLE_TOKEN;this.moodleURL=process.env.REACT_APP_MOODLE_URL;// Create axios instance with default config\nthis.api=axios.create({baseURL:this.baseURL,timeout:10000,headers:{'Content-Type':'application/x-www-form-urlencoded'}});}// Helper method to make API calls\nasync makeRequest(wsfunction){let params=arguments.length>1&&arguments[1]!==undefined?arguments[1]:{};try{const data=new URLSearchParams(_objectSpread({wstoken:this.token,wsfunction:wsfunction,moodlewsrestformat:'json'},params));const response=await this.api.post('',data);if(response.data.exception){throw new Error(response.data.message||'Moodle API Error');}return response.data;}catch(error){if(error.response){throw new Error(\"API Error: \".concat(error.response.status,\" - \").concat(error.response.statusText));}else if(error.request){throw new Error('Network Error: Unable to connect to Moodle');}else{throw new Error(error.message);}}}// Get site information\nasync getSiteInfo(){return await this.makeRequest('core_webservice_get_site_info');}// Get current user information\nasync getCurrentUser(){const siteInfo=await this.getSiteInfo();return{id:siteInfo.userid,username:siteInfo.username,firstname:siteInfo.firstname,lastname:siteInfo.lastname,email:siteInfo.useremail,lang:siteInfo.lang};}// Get user's courses\nasync getCourses(){let userid=arguments.length>0&&arguments[0]!==undefined?arguments[0]:null;const params=userid?{userid}:{};return await this.makeRequest('core_enrol_get_users_courses',params);}// Get course contents\nasync getCourseContents(courseid){return await this.makeRequest('core_course_get_contents',{courseid});}// Get user's grades for a course\nasync getCourseGrades(courseid){let userid=arguments.length>1&&arguments[1]!==undefined?arguments[1]:null;const params={courseid};if(userid)params.userid=userid;return await this.makeRequest('gradereport_user_get_grade_items',params);}// Get user's assignments\nasync getAssignments(){let courseids=arguments.length>0&&arguments[0]!==undefined?arguments[0]:[];const params=courseids.length>0?{courseids}:{};return await this.makeRequest('mod_assign_get_assignments',params);}// Get user's calendar events\nasync getCalendarEvents(){let options=arguments.length>0&&arguments[0]!==undefined?arguments[0]:{};const defaultOptions={events:{eventids:[],courseids:[],groupids:[],categoryids:[]},options:{userevents:true,siteevents:true,timestart:Math.floor(Date.now()/1000),timeend:Math.floor(Date.now()/1000)+30*24*60*60// 30 days\n}};const mergedOptions=_objectSpread(_objectSpread({},defaultOptions),options);return await this.makeRequest('core_calendar_get_calendar_events',mergedOptions);}// Get user's notifications\nasync getNotifications(){let useridto=arguments.length>0&&arguments[0]!==undefined?arguments[0]:null;let limit=arguments.length>1&&arguments[1]!==undefined?arguments[1]:20;const params={limit};if(useridto)params.useridto=useridto;return await this.makeRequest('message_popup_get_popup_notifications',params);}// Get course participants\nasync getCourseParticipants(courseid){return await this.makeRequest('core_enrol_get_enrolled_users',{courseid});}// Get user's recent activity\nasync getRecentActivity(courseid){let since=arguments.length>1&&arguments[1]!==undefined?arguments[1]:null;const params={courseid};if(since)params.since=since;return await this.makeRequest('core_course_get_recent_courses',params);}// Search courses\nasync searchCourses(criterianame,criteriavalue){let page=arguments.length>2&&arguments[2]!==undefined?arguments[2]:0;let perpage=arguments.length>3&&arguments[3]!==undefined?arguments[3]:20;return await this.makeRequest('core_course_search_courses',{criterianame,criteriavalue,page,perpage});}// Get user preferences\nasync getUserPreferences(){let name=arguments.length>0&&arguments[0]!==undefined?arguments[0]:null;const params=name?{name}:{};return await this.makeRequest('core_user_get_user_preferences',params);}// Set user preferences\nasync setUserPreferences(preferences){return await this.makeRequest('core_user_set_user_preferences',{preferences});}// Get course categories\nasync getCourseCategories(){let criteria=arguments.length>0&&arguments[0]!==undefined?arguments[0]:[];return await this.makeRequest('core_course_get_categories',{criteria});}// Get user's dashboard blocks\nasync getDashboardBlocks(){return await this.makeRequest('core_block_get_dashboard_blocks');}// Get user's completion status for courses\nasync getCourseCompletionStatus(courseid){let userid=arguments.length>1&&arguments[1]!==undefined?arguments[1]:null;const params={courseid};if(userid)params.userid=userid;return await this.makeRequest('core_completion_get_course_completion_status',params);}// Helper method to construct Moodle URLs\ngetMoodleURL(){let path=arguments.length>0&&arguments[0]!==undefined?arguments[0]:'';return\"\".concat(this.moodleURL).concat(path);}// Helper method to get course URL\ngetCourseURL(courseid){return this.getMoodleURL(\"/course/view.php?id=\".concat(courseid));}// Helper method to get user profile URL\ngetUserProfileURL(userid){return this.getMoodleURL(\"/user/profile.php?id=\".concat(userid));}// Helper method to check if API is configured\nisConfigured(){return!!(this.baseURL&&this.token&&this.moodleURL);}// Helper method to test connection\nasync testConnection(){try{await this.getSiteInfo();return{success:true,message:'Connection successful'};}catch(error){return{success:false,message:error.message};}}// Get user's progress data for dashboard\nasync getUserProgress(){let userid=arguments.length>0&&arguments[0]!==undefined?arguments[0]:null;try{const[courses,siteInfo]=await Promise.all([this.getCourses(userid),this.getSiteInfo()]);const progressData={totalCourses:courses.length,completedCourses:0,inProgressCourses:0,overallProgress:0,courses:[]};// Calculate progress for each course\nfor(const course of courses){try{const completion=await this.getCourseCompletionStatus(course.id,userid);const progress=completion.completionpercentage||0;progressData.courses.push(_objectSpread(_objectSpread({},course),{},{progress,isCompleted:progress===100}));if(progress===100){progressData.completedCourses++;}else if(progress>0){progressData.inProgressCourses++;}}catch(error){// If completion data is not available, add course with 0 progress\nprogressData.courses.push(_objectSpread(_objectSpread({},course),{},{progress:0,isCompleted:false}));}}// Calculate overall progress\nif(progressData.courses.length>0){const totalProgress=progressData.courses.reduce((sum,course)=>sum+course.progress,0);progressData.overallProgress=Math.round(totalProgress/progressData.courses.length);}return progressData;}catch(error){throw new Error(\"Failed to get user progress: \".concat(error.message));}}}// Create and export a singleton instance\nconst moodleAPI=new MoodleAPI();export default moodleAPI;", "map": {"version": 3, "names": ["axios", "MoodleAPI", "constructor", "baseURL", "process", "env", "REACT_APP_MOODLE_API_URL", "token", "REACT_APP_MOODLE_TOKEN", "moodleURL", "REACT_APP_MOODLE_URL", "api", "create", "timeout", "headers", "makeRequest", "wsfunction", "params", "arguments", "length", "undefined", "data", "URLSearchParams", "_objectSpread", "wstoken", "moodlewsrestformat", "response", "post", "exception", "Error", "message", "error", "concat", "status", "statusText", "request", "getSiteInfo", "getCurrentUser", "siteInfo", "id", "userid", "username", "firstname", "lastname", "email", "useremail", "lang", "getCourses", "getCourseContents", "courseid", "getCourseGrades", "getAssignments", "courseids", "getCalendarEvents", "options", "defaultOptions", "events", "eventids", "groupids", "categoryids", "userevents", "siteevents", "timestart", "Math", "floor", "Date", "now", "timeend", "mergedOptions", "getNotifications", "<PERSON><PERSON><PERSON>", "limit", "getCourseParticipants", "getRecentActivity", "since", "searchCourses", "criterianame", "criteriavalue", "page", "perpage", "getUserPreferences", "name", "setUserPreferences", "preferences", "getCourseCategories", "criteria", "getDashboardBlocks", "getCourseCompletionStatus", "getMoodleURL", "path", "getCourseURL", "getUserProfileURL", "isConfigured", "testConnection", "success", "getUserProgress", "courses", "Promise", "all", "progressData", "totalCourses", "completedCourses", "inProgressCourses", "overallProgress", "course", "completion", "progress", "completionpercentage", "push", "isCompleted", "totalProgress", "reduce", "sum", "round", "moodleAPI"], "sources": ["/Users/<USER>/Documents/FatbeamU/react-app/src/services/moodleAPI.js"], "sourcesContent": ["import axios from 'axios';\n\nclass MoodleAPI {\n  constructor() {\n    this.baseURL = process.env.REACT_APP_MOODLE_API_URL;\n    this.token = process.env.REACT_APP_MOODLE_TOKEN;\n    this.moodleURL = process.env.REACT_APP_MOODLE_URL;\n    \n    // Create axios instance with default config\n    this.api = axios.create({\n      baseURL: this.baseURL,\n      timeout: 10000,\n      headers: {\n        'Content-Type': 'application/x-www-form-urlencoded',\n      },\n    });\n  }\n\n  // Helper method to make API calls\n  async makeRequest(wsfunction, params = {}) {\n    try {\n      const data = new URLSearchParams({\n        wstoken: this.token,\n        wsfunction: wsfunction,\n        moodlewsrestformat: 'json',\n        ...params\n      });\n\n      const response = await this.api.post('', data);\n      \n      if (response.data.exception) {\n        throw new Error(response.data.message || 'Moodle API Error');\n      }\n      \n      return response.data;\n    } catch (error) {\n      if (error.response) {\n        throw new Error(`API Error: ${error.response.status} - ${error.response.statusText}`);\n      } else if (error.request) {\n        throw new Error('Network Error: Unable to connect to Moodle');\n      } else {\n        throw new Error(error.message);\n      }\n    }\n  }\n\n  // Get site information\n  async getSiteInfo() {\n    return await this.makeRequest('core_webservice_get_site_info');\n  }\n\n  // Get current user information\n  async getCurrentUser() {\n    const siteInfo = await this.getSiteInfo();\n    return {\n      id: siteInfo.userid,\n      username: siteInfo.username,\n      firstname: siteInfo.firstname,\n      lastname: siteInfo.lastname,\n      email: siteInfo.useremail,\n      lang: siteInfo.lang\n    };\n  }\n\n  // Get user's courses\n  async getCourses(userid = null) {\n    const params = userid ? { userid } : {};\n    return await this.makeRequest('core_enrol_get_users_courses', params);\n  }\n\n  // Get course contents\n  async getCourseContents(courseid) {\n    return await this.makeRequest('core_course_get_contents', { courseid });\n  }\n\n  // Get user's grades for a course\n  async getCourseGrades(courseid, userid = null) {\n    const params = { courseid };\n    if (userid) params.userid = userid;\n    return await this.makeRequest('gradereport_user_get_grade_items', params);\n  }\n\n  // Get user's assignments\n  async getAssignments(courseids = []) {\n    const params = courseids.length > 0 ? { courseids } : {};\n    return await this.makeRequest('mod_assign_get_assignments', params);\n  }\n\n  // Get user's calendar events\n  async getCalendarEvents(options = {}) {\n    const defaultOptions = {\n      events: {\n        eventids: [],\n        courseids: [],\n        groupids: [],\n        categoryids: []\n      },\n      options: {\n        userevents: true,\n        siteevents: true,\n        timestart: Math.floor(Date.now() / 1000),\n        timeend: Math.floor(Date.now() / 1000) + (30 * 24 * 60 * 60) // 30 days\n      }\n    };\n    \n    const mergedOptions = { ...defaultOptions, ...options };\n    return await this.makeRequest('core_calendar_get_calendar_events', mergedOptions);\n  }\n\n  // Get user's notifications\n  async getNotifications(useridto = null, limit = 20) {\n    const params = { limit };\n    if (useridto) params.useridto = useridto;\n    return await this.makeRequest('message_popup_get_popup_notifications', params);\n  }\n\n  // Get course participants\n  async getCourseParticipants(courseid) {\n    return await this.makeRequest('core_enrol_get_enrolled_users', { courseid });\n  }\n\n  // Get user's recent activity\n  async getRecentActivity(courseid, since = null) {\n    const params = { courseid };\n    if (since) params.since = since;\n    return await this.makeRequest('core_course_get_recent_courses', params);\n  }\n\n  // Search courses\n  async searchCourses(criterianame, criteriavalue, page = 0, perpage = 20) {\n    return await this.makeRequest('core_course_search_courses', {\n      criterianame,\n      criteriavalue,\n      page,\n      perpage\n    });\n  }\n\n  // Get user preferences\n  async getUserPreferences(name = null) {\n    const params = name ? { name } : {};\n    return await this.makeRequest('core_user_get_user_preferences', params);\n  }\n\n  // Set user preferences\n  async setUserPreferences(preferences) {\n    return await this.makeRequest('core_user_set_user_preferences', { preferences });\n  }\n\n  // Get course categories\n  async getCourseCategories(criteria = []) {\n    return await this.makeRequest('core_course_get_categories', { criteria });\n  }\n\n  // Get user's dashboard blocks\n  async getDashboardBlocks() {\n    return await this.makeRequest('core_block_get_dashboard_blocks');\n  }\n\n  // Get user's completion status for courses\n  async getCourseCompletionStatus(courseid, userid = null) {\n    const params = { courseid };\n    if (userid) params.userid = userid;\n    return await this.makeRequest('core_completion_get_course_completion_status', params);\n  }\n\n  // Helper method to construct Moodle URLs\n  getMoodleURL(path = '') {\n    return `${this.moodleURL}${path}`;\n  }\n\n  // Helper method to get course URL\n  getCourseURL(courseid) {\n    return this.getMoodleURL(`/course/view.php?id=${courseid}`);\n  }\n\n  // Helper method to get user profile URL\n  getUserProfileURL(userid) {\n    return this.getMoodleURL(`/user/profile.php?id=${userid}`);\n  }\n\n  // Helper method to check if API is configured\n  isConfigured() {\n    return !!(this.baseURL && this.token && this.moodleURL);\n  }\n\n  // Helper method to test connection\n  async testConnection() {\n    try {\n      await this.getSiteInfo();\n      return { success: true, message: 'Connection successful' };\n    } catch (error) {\n      return { success: false, message: error.message };\n    }\n  }\n\n  // Get user's progress data for dashboard\n  async getUserProgress(userid = null) {\n    try {\n      const [courses, siteInfo] = await Promise.all([\n        this.getCourses(userid),\n        this.getSiteInfo()\n      ]);\n\n      const progressData = {\n        totalCourses: courses.length,\n        completedCourses: 0,\n        inProgressCourses: 0,\n        overallProgress: 0,\n        courses: []\n      };\n\n      // Calculate progress for each course\n      for (const course of courses) {\n        try {\n          const completion = await this.getCourseCompletionStatus(course.id, userid);\n          const progress = completion.completionpercentage || 0;\n          \n          progressData.courses.push({\n            ...course,\n            progress,\n            isCompleted: progress === 100\n          });\n\n          if (progress === 100) {\n            progressData.completedCourses++;\n          } else if (progress > 0) {\n            progressData.inProgressCourses++;\n          }\n        } catch (error) {\n          // If completion data is not available, add course with 0 progress\n          progressData.courses.push({\n            ...course,\n            progress: 0,\n            isCompleted: false\n          });\n        }\n      }\n\n      // Calculate overall progress\n      if (progressData.courses.length > 0) {\n        const totalProgress = progressData.courses.reduce((sum, course) => sum + course.progress, 0);\n        progressData.overallProgress = Math.round(totalProgress / progressData.courses.length);\n      }\n\n      return progressData;\n    } catch (error) {\n      throw new Error(`Failed to get user progress: ${error.message}`);\n    }\n  }\n}\n\n// Create and export a singleton instance\nconst moodleAPI = new MoodleAPI();\nexport default moodleAPI;\n"], "mappings": "qIAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CAEzB,KAAM,CAAAC,SAAU,CACdC,WAAWA,CAAA,CAAG,CACZ,IAAI,CAACC,OAAO,CAAGC,OAAO,CAACC,GAAG,CAACC,wBAAwB,CACnD,IAAI,CAACC,KAAK,CAAGH,OAAO,CAACC,GAAG,CAACG,sBAAsB,CAC/C,IAAI,CAACC,SAAS,CAAGL,OAAO,CAACC,GAAG,CAACK,oBAAoB,CAEjD;AACA,IAAI,CAACC,GAAG,CAAGX,KAAK,CAACY,MAAM,CAAC,CACtBT,OAAO,CAAE,IAAI,CAACA,OAAO,CACrBU,OAAO,CAAE,KAAK,CACdC,OAAO,CAAE,CACP,cAAc,CAAE,mCAClB,CACF,CAAC,CAAC,CACJ,CAEA;AACA,KAAM,CAAAC,WAAWA,CAACC,UAAU,CAAe,IAAb,CAAAC,MAAM,CAAAC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,CAAC,CAAC,CACvC,GAAI,CACF,KAAM,CAAAG,IAAI,CAAG,GAAI,CAAAC,eAAe,CAAAC,aAAA,EAC9BC,OAAO,CAAE,IAAI,CAACjB,KAAK,CACnBS,UAAU,CAAEA,UAAU,CACtBS,kBAAkB,CAAE,MAAM,EACvBR,MAAM,CACV,CAAC,CAEF,KAAM,CAAAS,QAAQ,CAAG,KAAM,KAAI,CAACf,GAAG,CAACgB,IAAI,CAAC,EAAE,CAAEN,IAAI,CAAC,CAE9C,GAAIK,QAAQ,CAACL,IAAI,CAACO,SAAS,CAAE,CAC3B,KAAM,IAAI,CAAAC,KAAK,CAACH,QAAQ,CAACL,IAAI,CAACS,OAAO,EAAI,kBAAkB,CAAC,CAC9D,CAEA,MAAO,CAAAJ,QAAQ,CAACL,IAAI,CACtB,CAAE,MAAOU,KAAK,CAAE,CACd,GAAIA,KAAK,CAACL,QAAQ,CAAE,CAClB,KAAM,IAAI,CAAAG,KAAK,eAAAG,MAAA,CAAeD,KAAK,CAACL,QAAQ,CAACO,MAAM,QAAAD,MAAA,CAAMD,KAAK,CAACL,QAAQ,CAACQ,UAAU,CAAE,CAAC,CACvF,CAAC,IAAM,IAAIH,KAAK,CAACI,OAAO,CAAE,CACxB,KAAM,IAAI,CAAAN,KAAK,CAAC,4CAA4C,CAAC,CAC/D,CAAC,IAAM,CACL,KAAM,IAAI,CAAAA,KAAK,CAACE,KAAK,CAACD,OAAO,CAAC,CAChC,CACF,CACF,CAEA;AACA,KAAM,CAAAM,WAAWA,CAAA,CAAG,CAClB,MAAO,MAAM,KAAI,CAACrB,WAAW,CAAC,+BAA+B,CAAC,CAChE,CAEA;AACA,KAAM,CAAAsB,cAAcA,CAAA,CAAG,CACrB,KAAM,CAAAC,QAAQ,CAAG,KAAM,KAAI,CAACF,WAAW,CAAC,CAAC,CACzC,MAAO,CACLG,EAAE,CAAED,QAAQ,CAACE,MAAM,CACnBC,QAAQ,CAAEH,QAAQ,CAACG,QAAQ,CAC3BC,SAAS,CAAEJ,QAAQ,CAACI,SAAS,CAC7BC,QAAQ,CAAEL,QAAQ,CAACK,QAAQ,CAC3BC,KAAK,CAAEN,QAAQ,CAACO,SAAS,CACzBC,IAAI,CAAER,QAAQ,CAACQ,IACjB,CAAC,CACH,CAEA;AACA,KAAM,CAAAC,UAAUA,CAAA,CAAgB,IAAf,CAAAP,MAAM,CAAAtB,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,IAAI,CAC5B,KAAM,CAAAD,MAAM,CAAGuB,MAAM,CAAG,CAAEA,MAAO,CAAC,CAAG,CAAC,CAAC,CACvC,MAAO,MAAM,KAAI,CAACzB,WAAW,CAAC,8BAA8B,CAAEE,MAAM,CAAC,CACvE,CAEA;AACA,KAAM,CAAA+B,iBAAiBA,CAACC,QAAQ,CAAE,CAChC,MAAO,MAAM,KAAI,CAAClC,WAAW,CAAC,0BAA0B,CAAE,CAAEkC,QAAS,CAAC,CAAC,CACzE,CAEA;AACA,KAAM,CAAAC,eAAeA,CAACD,QAAQ,CAAiB,IAAf,CAAAT,MAAM,CAAAtB,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,IAAI,CAC3C,KAAM,CAAAD,MAAM,CAAG,CAAEgC,QAAS,CAAC,CAC3B,GAAIT,MAAM,CAAEvB,MAAM,CAACuB,MAAM,CAAGA,MAAM,CAClC,MAAO,MAAM,KAAI,CAACzB,WAAW,CAAC,kCAAkC,CAAEE,MAAM,CAAC,CAC3E,CAEA;AACA,KAAM,CAAAkC,cAAcA,CAAA,CAAiB,IAAhB,CAAAC,SAAS,CAAAlC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,EAAE,CACjC,KAAM,CAAAD,MAAM,CAAGmC,SAAS,CAACjC,MAAM,CAAG,CAAC,CAAG,CAAEiC,SAAU,CAAC,CAAG,CAAC,CAAC,CACxD,MAAO,MAAM,KAAI,CAACrC,WAAW,CAAC,4BAA4B,CAAEE,MAAM,CAAC,CACrE,CAEA;AACA,KAAM,CAAAoC,iBAAiBA,CAAA,CAAe,IAAd,CAAAC,OAAO,CAAApC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,CAAC,CAAC,CAClC,KAAM,CAAAqC,cAAc,CAAG,CACrBC,MAAM,CAAE,CACNC,QAAQ,CAAE,EAAE,CACZL,SAAS,CAAE,EAAE,CACbM,QAAQ,CAAE,EAAE,CACZC,WAAW,CAAE,EACf,CAAC,CACDL,OAAO,CAAE,CACPM,UAAU,CAAE,IAAI,CAChBC,UAAU,CAAE,IAAI,CAChBC,SAAS,CAAEC,IAAI,CAACC,KAAK,CAACC,IAAI,CAACC,GAAG,CAAC,CAAC,CAAG,IAAI,CAAC,CACxCC,OAAO,CAAEJ,IAAI,CAACC,KAAK,CAACC,IAAI,CAACC,GAAG,CAAC,CAAC,CAAG,IAAI,CAAC,CAAI,EAAE,CAAG,EAAE,CAAG,EAAE,CAAG,EAAI;AAC/D,CACF,CAAC,CAED,KAAM,CAAAE,aAAa,CAAA7C,aAAA,CAAAA,aAAA,IAAQgC,cAAc,EAAKD,OAAO,CAAE,CACvD,MAAO,MAAM,KAAI,CAACvC,WAAW,CAAC,mCAAmC,CAAEqD,aAAa,CAAC,CACnF,CAEA;AACA,KAAM,CAAAC,gBAAgBA,CAAA,CAA8B,IAA7B,CAAAC,QAAQ,CAAApD,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,IAAI,IAAE,CAAAqD,KAAK,CAAArD,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,EAAE,CAChD,KAAM,CAAAD,MAAM,CAAG,CAAEsD,KAAM,CAAC,CACxB,GAAID,QAAQ,CAAErD,MAAM,CAACqD,QAAQ,CAAGA,QAAQ,CACxC,MAAO,MAAM,KAAI,CAACvD,WAAW,CAAC,uCAAuC,CAAEE,MAAM,CAAC,CAChF,CAEA;AACA,KAAM,CAAAuD,qBAAqBA,CAACvB,QAAQ,CAAE,CACpC,MAAO,MAAM,KAAI,CAAClC,WAAW,CAAC,+BAA+B,CAAE,CAAEkC,QAAS,CAAC,CAAC,CAC9E,CAEA;AACA,KAAM,CAAAwB,iBAAiBA,CAACxB,QAAQ,CAAgB,IAAd,CAAAyB,KAAK,CAAAxD,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,IAAI,CAC5C,KAAM,CAAAD,MAAM,CAAG,CAAEgC,QAAS,CAAC,CAC3B,GAAIyB,KAAK,CAAEzD,MAAM,CAACyD,KAAK,CAAGA,KAAK,CAC/B,MAAO,MAAM,KAAI,CAAC3D,WAAW,CAAC,gCAAgC,CAAEE,MAAM,CAAC,CACzE,CAEA;AACA,KAAM,CAAA0D,aAAaA,CAACC,YAAY,CAAEC,aAAa,CAA0B,IAAxB,CAAAC,IAAI,CAAA5D,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,CAAC,IAAE,CAAA6D,OAAO,CAAA7D,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,EAAE,CACrE,MAAO,MAAM,KAAI,CAACH,WAAW,CAAC,4BAA4B,CAAE,CAC1D6D,YAAY,CACZC,aAAa,CACbC,IAAI,CACJC,OACF,CAAC,CAAC,CACJ,CAEA;AACA,KAAM,CAAAC,kBAAkBA,CAAA,CAAc,IAAb,CAAAC,IAAI,CAAA/D,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,IAAI,CAClC,KAAM,CAAAD,MAAM,CAAGgE,IAAI,CAAG,CAAEA,IAAK,CAAC,CAAG,CAAC,CAAC,CACnC,MAAO,MAAM,KAAI,CAAClE,WAAW,CAAC,gCAAgC,CAAEE,MAAM,CAAC,CACzE,CAEA;AACA,KAAM,CAAAiE,kBAAkBA,CAACC,WAAW,CAAE,CACpC,MAAO,MAAM,KAAI,CAACpE,WAAW,CAAC,gCAAgC,CAAE,CAAEoE,WAAY,CAAC,CAAC,CAClF,CAEA;AACA,KAAM,CAAAC,mBAAmBA,CAAA,CAAgB,IAAf,CAAAC,QAAQ,CAAAnE,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,EAAE,CACrC,MAAO,MAAM,KAAI,CAACH,WAAW,CAAC,4BAA4B,CAAE,CAAEsE,QAAS,CAAC,CAAC,CAC3E,CAEA;AACA,KAAM,CAAAC,kBAAkBA,CAAA,CAAG,CACzB,MAAO,MAAM,KAAI,CAACvE,WAAW,CAAC,iCAAiC,CAAC,CAClE,CAEA;AACA,KAAM,CAAAwE,yBAAyBA,CAACtC,QAAQ,CAAiB,IAAf,CAAAT,MAAM,CAAAtB,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,IAAI,CACrD,KAAM,CAAAD,MAAM,CAAG,CAAEgC,QAAS,CAAC,CAC3B,GAAIT,MAAM,CAAEvB,MAAM,CAACuB,MAAM,CAAGA,MAAM,CAClC,MAAO,MAAM,KAAI,CAACzB,WAAW,CAAC,8CAA8C,CAAEE,MAAM,CAAC,CACvF,CAEA;AACAuE,YAAYA,CAAA,CAAY,IAAX,CAAAC,IAAI,CAAAvE,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,EAAE,CACpB,SAAAc,MAAA,CAAU,IAAI,CAACvB,SAAS,EAAAuB,MAAA,CAAGyD,IAAI,EACjC,CAEA;AACAC,YAAYA,CAACzC,QAAQ,CAAE,CACrB,MAAO,KAAI,CAACuC,YAAY,wBAAAxD,MAAA,CAAwBiB,QAAQ,CAAE,CAAC,CAC7D,CAEA;AACA0C,iBAAiBA,CAACnD,MAAM,CAAE,CACxB,MAAO,KAAI,CAACgD,YAAY,yBAAAxD,MAAA,CAAyBQ,MAAM,CAAE,CAAC,CAC5D,CAEA;AACAoD,YAAYA,CAAA,CAAG,CACb,MAAO,CAAC,EAAE,IAAI,CAACzF,OAAO,EAAI,IAAI,CAACI,KAAK,EAAI,IAAI,CAACE,SAAS,CAAC,CACzD,CAEA;AACA,KAAM,CAAAoF,cAAcA,CAAA,CAAG,CACrB,GAAI,CACF,KAAM,KAAI,CAACzD,WAAW,CAAC,CAAC,CACxB,MAAO,CAAE0D,OAAO,CAAE,IAAI,CAAEhE,OAAO,CAAE,uBAAwB,CAAC,CAC5D,CAAE,MAAOC,KAAK,CAAE,CACd,MAAO,CAAE+D,OAAO,CAAE,KAAK,CAAEhE,OAAO,CAAEC,KAAK,CAACD,OAAQ,CAAC,CACnD,CACF,CAEA;AACA,KAAM,CAAAiE,eAAeA,CAAA,CAAgB,IAAf,CAAAvD,MAAM,CAAAtB,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,IAAI,CACjC,GAAI,CACF,KAAM,CAAC8E,OAAO,CAAE1D,QAAQ,CAAC,CAAG,KAAM,CAAA2D,OAAO,CAACC,GAAG,CAAC,CAC5C,IAAI,CAACnD,UAAU,CAACP,MAAM,CAAC,CACvB,IAAI,CAACJ,WAAW,CAAC,CAAC,CACnB,CAAC,CAEF,KAAM,CAAA+D,YAAY,CAAG,CACnBC,YAAY,CAAEJ,OAAO,CAAC7E,MAAM,CAC5BkF,gBAAgB,CAAE,CAAC,CACnBC,iBAAiB,CAAE,CAAC,CACpBC,eAAe,CAAE,CAAC,CAClBP,OAAO,CAAE,EACX,CAAC,CAED;AACA,IAAK,KAAM,CAAAQ,MAAM,GAAI,CAAAR,OAAO,CAAE,CAC5B,GAAI,CACF,KAAM,CAAAS,UAAU,CAAG,KAAM,KAAI,CAAClB,yBAAyB,CAACiB,MAAM,CAACjE,EAAE,CAAEC,MAAM,CAAC,CAC1E,KAAM,CAAAkE,QAAQ,CAAGD,UAAU,CAACE,oBAAoB,EAAI,CAAC,CAErDR,YAAY,CAACH,OAAO,CAACY,IAAI,CAAArF,aAAA,CAAAA,aAAA,IACpBiF,MAAM,MACTE,QAAQ,CACRG,WAAW,CAAEH,QAAQ,GAAK,GAAG,EAC9B,CAAC,CAEF,GAAIA,QAAQ,GAAK,GAAG,CAAE,CACpBP,YAAY,CAACE,gBAAgB,EAAE,CACjC,CAAC,IAAM,IAAIK,QAAQ,CAAG,CAAC,CAAE,CACvBP,YAAY,CAACG,iBAAiB,EAAE,CAClC,CACF,CAAE,MAAOvE,KAAK,CAAE,CACd;AACAoE,YAAY,CAACH,OAAO,CAACY,IAAI,CAAArF,aAAA,CAAAA,aAAA,IACpBiF,MAAM,MACTE,QAAQ,CAAE,CAAC,CACXG,WAAW,CAAE,KAAK,EACnB,CAAC,CACJ,CACF,CAEA;AACA,GAAIV,YAAY,CAACH,OAAO,CAAC7E,MAAM,CAAG,CAAC,CAAE,CACnC,KAAM,CAAA2F,aAAa,CAAGX,YAAY,CAACH,OAAO,CAACe,MAAM,CAAC,CAACC,GAAG,CAAER,MAAM,GAAKQ,GAAG,CAAGR,MAAM,CAACE,QAAQ,CAAE,CAAC,CAAC,CAC5FP,YAAY,CAACI,eAAe,CAAGxC,IAAI,CAACkD,KAAK,CAACH,aAAa,CAAGX,YAAY,CAACH,OAAO,CAAC7E,MAAM,CAAC,CACxF,CAEA,MAAO,CAAAgF,YAAY,CACrB,CAAE,MAAOpE,KAAK,CAAE,CACd,KAAM,IAAI,CAAAF,KAAK,iCAAAG,MAAA,CAAiCD,KAAK,CAACD,OAAO,CAAE,CAAC,CAClE,CACF,CACF,CAEA;AACA,KAAM,CAAAoF,SAAS,CAAG,GAAI,CAAAjH,SAAS,CAAC,CAAC,CACjC,cAAe,CAAAiH,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}