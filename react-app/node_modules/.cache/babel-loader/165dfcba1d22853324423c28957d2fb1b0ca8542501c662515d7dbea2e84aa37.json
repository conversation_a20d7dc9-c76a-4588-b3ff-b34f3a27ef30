{"ast": null, "code": "import React from'react';import{<PERSON>}from'react-router-dom';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const LeadershipTeamPage=()=>{return/*#__PURE__*/_jsx(\"div\",{className:\"min-h-screen bg-white text-gray-800 font-sans pt-20\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"container mx-auto px-6 py-12\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-center mb-12\",children:[/*#__PURE__*/_jsxs(\"h1\",{className:\"text-4xl md:text-5xl font-serif font-bold mb-6\",children:[\"Leadership Team \",/*#__PURE__*/_jsx(\"span\",{className:\"text-red-600\",children:\"(Council of Archmages)\"})]}),/*#__PURE__*/_jsx(\"p\",{className:\"text-xl text-gray-600 max-w-3xl mx-auto\",children:\"The guiding council of master technomancers who oversee the strategic direction of our mystical institution.\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"bg-red-50 rounded-lg p-8 text-center\",children:[/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-crown text-6xl text-red-600 mb-4\"}),/*#__PURE__*/_jsx(\"h2\",{className:\"text-2xl font-bold text-gray-800 mb-4\",children:\"Department Page Coming Soon\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-600 mb-6\",children:\"The Council of Archmages is currently deliberating on the mystical content for this page.\"}),/*#__PURE__*/_jsxs(Link,{to:\"/departments\",className:\"inline-flex items-center px-6 py-3 bg-red-600 text-white rounded-full hover:bg-red-700 transition-colors duration-300\",children:[/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-arrow-left mr-2\"}),\"Back to Departments\"]})]})]})});};export default LeadershipTeamPage;", "map": {"version": 3, "names": ["React", "Link", "jsx", "_jsx", "jsxs", "_jsxs", "LeadershipTeamPage", "className", "children", "to"], "sources": ["/Users/<USER>/Documents/FatbeamU/react-app/src/pages/departments/LeadershipTeamPage.js"], "sourcesContent": ["import React from 'react';\nimport { Link } from 'react-router-dom';\n\nconst LeadershipTeamPage = () => {\n  return (\n    <div className=\"min-h-screen bg-white text-gray-800 font-sans pt-20\">\n      <div className=\"container mx-auto px-6 py-12\">\n        <div className=\"text-center mb-12\">\n          <h1 className=\"text-4xl md:text-5xl font-serif font-bold mb-6\">\n            Leadership Team <span className=\"text-red-600\">(Council of Archmages)</span>\n          </h1>\n          <p className=\"text-xl text-gray-600 max-w-3xl mx-auto\">\n            The guiding council of master technomancers who oversee the strategic direction of our mystical institution.\n          </p>\n        </div>\n        \n        <div className=\"bg-red-50 rounded-lg p-8 text-center\">\n          <i className=\"fas fa-crown text-6xl text-red-600 mb-4\"></i>\n          <h2 className=\"text-2xl font-bold text-gray-800 mb-4\">Department Page Coming Soon</h2>\n          <p className=\"text-gray-600 mb-6\">\n            The Council of Archmages is currently deliberating on the mystical content for this page.\n          </p>\n          <Link to=\"/departments\" className=\"inline-flex items-center px-6 py-3 bg-red-600 text-white rounded-full hover:bg-red-700 transition-colors duration-300\">\n            <i className=\"fas fa-arrow-left mr-2\"></i>\n            Back to Departments\n          </Link>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default LeadershipTeamPage;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OAASC,IAAI,KAAQ,kBAAkB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAExC,KAAM,CAAAC,kBAAkB,CAAGA,CAAA,GAAM,CAC/B,mBACEH,IAAA,QAAKI,SAAS,CAAC,qDAAqD,CAAAC,QAAA,cAClEH,KAAA,QAAKE,SAAS,CAAC,8BAA8B,CAAAC,QAAA,eAC3CH,KAAA,QAAKE,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChCH,KAAA,OAAIE,SAAS,CAAC,gDAAgD,CAAAC,QAAA,EAAC,kBAC7C,cAAAL,IAAA,SAAMI,SAAS,CAAC,cAAc,CAAAC,QAAA,CAAC,wBAAsB,CAAM,CAAC,EAC1E,CAAC,cACLL,IAAA,MAAGI,SAAS,CAAC,yCAAyC,CAAAC,QAAA,CAAC,8GAEvD,CAAG,CAAC,EACD,CAAC,cAENH,KAAA,QAAKE,SAAS,CAAC,sCAAsC,CAAAC,QAAA,eACnDL,IAAA,MAAGI,SAAS,CAAC,yCAAyC,CAAI,CAAC,cAC3DJ,IAAA,OAAII,SAAS,CAAC,uCAAuC,CAAAC,QAAA,CAAC,6BAA2B,CAAI,CAAC,cACtFL,IAAA,MAAGI,SAAS,CAAC,oBAAoB,CAAAC,QAAA,CAAC,2FAElC,CAAG,CAAC,cACJH,KAAA,CAACJ,IAAI,EAACQ,EAAE,CAAC,cAAc,CAACF,SAAS,CAAC,uHAAuH,CAAAC,QAAA,eACvJL,IAAA,MAAGI,SAAS,CAAC,wBAAwB,CAAI,CAAC,sBAE5C,EAAM,CAAC,EACJ,CAAC,EACH,CAAC,CACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAAD,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}